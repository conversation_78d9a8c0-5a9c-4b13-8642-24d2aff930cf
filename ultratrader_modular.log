2025-07-16 20:40:02,485 - __main__ - INFO - Running on Windows - using standard asyncio event loop
2025-07-16 20:40:02,485 - __main__ - INFO - Ultratrader Orchestrator initialized
2025-07-16 20:40:02,485 - __main__ - INFO - Starting Ultratrader optimization pipeline
2025-07-16 20:40:02,485 - __main__ - ERROR - Failed to initialize Binance client: Binance API credentials not found. Please set BINANCE_API_KEY and BINANCE_SECRET_KEY environment variables.
2025-07-16 20:40:02,485 - __main__ - ERROR - Critical error in optimization pipeline: Binance API credentials not found. Please set BINANCE_API_KEY and BINANCE_SECRET_KEY environment variables.
2025-07-16 20:40:02,486 - __main__ - ERROR - Fatal error: Binance API credentials not found. Please set BINANCE_API_KEY and BINANCE_SECRET_KEY environment variables.
2025-07-16 20:43:25,563 - __main__ - INFO - Running on Windows - using standard asyncio event loop
2025-07-16 20:43:25,563 - __main__ - INFO - Ultratrader Orchestrator initialized
2025-07-16 20:43:25,563 - __main__ - INFO - Starting Ultratrader optimization pipeline
2025-07-16 20:43:25,594 - __main__ - ERROR - Failed to initialize Binance client: aiodns needs a SelectorEventLoop on Windows. See more: https://github.com/saghul/aiodns/issues/86
2025-07-16 20:43:25,594 - __main__ - ERROR - Critical error in optimization pipeline: aiodns needs a SelectorEventLoop on Windows. See more: https://github.com/saghul/aiodns/issues/86
2025-07-16 20:43:25,633 - __main__ - ERROR - Fatal error: aiodns needs a SelectorEventLoop on Windows. See more: https://github.com/saghul/aiodns/issues/86
2025-07-16 20:46:36,783 - __main__ - INFO - Ultratrader Orchestrator initialized
2025-07-16 20:46:36,784 - __main__ - INFO - Ultratrader Orchestrator initialized
2025-07-16 20:46:36,784 - __main__ - INFO - Starting Ultratrader optimization pipeline
2025-07-16 20:46:37,663 - __main__ - INFO - Binance client initialized successfully
2025-07-16 20:46:37,663 - __main__ - INFO - Optimizing strategies for symbols: ['BTCUSDT']
2025-07-16 20:46:37,663 - __main__ - INFO - Fetching market data for BTCUSDT
2025-07-16 20:46:38,937 - __main__ - INFO - Retrieved 1500 bars for BTCUSDT (15m)
2025-07-16 20:46:38,937 - __main__ - INFO - Retrieved 1500 bars for BTCUSDT (1h)
2025-07-16 20:46:38,937 - __main__ - INFO - Optimizing Trend strategy for BTCUSDT
2025-07-16 20:46:42,936 - root - WARNING - Trial failed: 
2025-07-16 20:46:43,217 - root - WARNING - Trial failed: 
2025-07-16 20:46:43,581 - root - WARNING - Trial failed: 
2025-07-16 20:46:45,585 - root - WARNING - Trial failed: 
2025-07-16 20:46:45,695 - root - WARNING - Trial failed: 
2025-07-16 20:46:46,371 - root - WARNING - Trial failed: 
2025-07-16 20:46:47,829 - root - WARNING - Trial failed: 
2025-07-16 20:46:47,899 - __main__ - INFO - BTCUSDT Trend: Best score = 3.514
2025-07-16 20:46:47,899 - __main__ - INFO - Running walk-forward validation for BTCUSDT Trend
2025-07-16 20:46:47,900 - __main__ - WARNING - No valid walk-forward windows for BTCUSDT Trend
2025-07-16 20:46:47,900 - __main__ - INFO - Optimizing Reversion strategy for BTCUSDT
2025-07-16 20:46:47,941 - root - WARNING - Trial failed: 
2025-07-16 20:46:47,978 - root - WARNING - Trial failed: 
2025-07-16 20:46:48,014 - root - WARNING - Trial failed: 
2025-07-16 20:46:48,055 - root - WARNING - Trial failed: 
2025-07-16 20:46:48,091 - root - WARNING - Trial failed: 
2025-07-16 20:46:48,127 - root - WARNING - Trial failed: 
2025-07-16 20:46:48,163 - root - WARNING - Trial failed: 
2025-07-16 20:46:48,198 - root - WARNING - Trial failed: 
2025-07-16 20:46:48,234 - root - WARNING - Trial failed: 
2025-07-16 20:46:48,272 - root - WARNING - Trial failed: 
2025-07-16 20:46:48,310 - root - WARNING - Trial failed: 
2025-07-16 20:46:48,352 - root - WARNING - Trial failed: 
2025-07-16 20:46:48,436 - root - WARNING - Trial failed: 
2025-07-16 20:46:48,523 - root - WARNING - Trial failed: 
2025-07-16 20:46:48,616 - root - WARNING - Trial failed: 
2025-07-16 20:46:48,705 - root - WARNING - Trial failed: 
2025-07-16 20:46:48,792 - root - WARNING - Trial failed: 
2025-07-16 20:46:48,884 - root - WARNING - Trial failed: 
2025-07-16 20:46:48,968 - root - WARNING - Trial failed: 
2025-07-16 20:46:49,059 - root - WARNING - Trial failed: 
2025-07-16 20:46:49,151 - root - WARNING - Trial failed: 
2025-07-16 20:46:49,244 - root - WARNING - Trial failed: 
2025-07-16 20:46:49,336 - root - WARNING - Trial failed: 
2025-07-16 20:46:49,437 - root - WARNING - Trial failed: 
2025-07-16 20:46:49,528 - root - WARNING - Trial failed: 
2025-07-16 20:46:49,620 - root - WARNING - Trial failed: 
2025-07-16 20:46:49,712 - root - WARNING - Trial failed: 
2025-07-16 20:46:49,802 - root - WARNING - Trial failed: 
2025-07-16 20:46:49,944 - root - WARNING - Trial failed: 
2025-07-16 20:46:50,034 - root - WARNING - Trial failed: 
2025-07-16 20:46:50,129 - root - WARNING - Trial failed: 
2025-07-16 20:46:50,224 - root - WARNING - Trial failed: 
2025-07-16 20:46:50,322 - root - WARNING - Trial failed: 
2025-07-16 20:46:50,421 - root - WARNING - Trial failed: 
2025-07-16 20:46:50,512 - root - WARNING - Trial failed: 
2025-07-16 20:46:50,609 - root - WARNING - Trial failed: 
2025-07-16 20:46:50,705 - root - WARNING - Trial failed: 
2025-07-16 20:46:50,800 - root - WARNING - Trial failed: 
2025-07-16 20:46:50,902 - root - WARNING - Trial failed: 
2025-07-16 20:46:50,999 - root - WARNING - Trial failed: 
2025-07-16 20:46:51,096 - root - WARNING - Trial failed: 
2025-07-16 20:46:51,202 - root - WARNING - Trial failed: 
2025-07-16 20:46:51,310 - root - WARNING - Trial failed: 
2025-07-16 20:46:51,411 - root - WARNING - Trial failed: 
2025-07-16 20:46:51,510 - root - WARNING - Trial failed: 
2025-07-16 20:46:51,610 - root - WARNING - Trial failed: 
2025-07-16 20:46:51,712 - root - WARNING - Trial failed: 
2025-07-16 20:46:51,808 - root - WARNING - Trial failed: 
2025-07-16 20:46:51,908 - root - WARNING - Trial failed: 
2025-07-16 20:46:52,008 - root - WARNING - Trial failed: 
2025-07-16 20:46:52,058 - __main__ - INFO - BTCUSDT Reversion: Best score = -999.000
2025-07-16 20:46:52,058 - __main__ - INFO - Running walk-forward validation for BTCUSDT Reversion
2025-07-16 20:46:52,059 - __main__ - WARNING - No valid walk-forward windows for BTCUSDT Reversion
2025-07-16 20:46:52,059 - __main__ - INFO - Optimizing Adaptive strategy for BTCUSDT
2025-07-16 20:46:52,305 - root - WARNING - Trial failed: 
2025-07-16 20:46:52,383 - root - WARNING - Trial failed: 
2025-07-16 20:46:53,162 - root - WARNING - Trial failed: 
2025-07-16 20:46:53,927 - root - WARNING - Trial failed: 
2025-07-16 20:46:54,758 - root - WARNING - Trial failed: 
2025-07-16 20:46:55,623 - __main__ - INFO - BTCUSDT Adaptive: Best score = 3.496
2025-07-16 20:46:55,624 - __main__ - INFO - Running walk-forward validation for BTCUSDT Adaptive
2025-07-16 20:46:55,624 - __main__ - WARNING - No valid walk-forward windows for BTCUSDT Adaptive
2025-07-16 20:46:55,624 - __main__ - INFO - Validating and scoring strategies
2025-07-16 20:46:55,624 - __main__ - WARNING - No valid walk-forward results for BTCUSDT_Trend
2025-07-16 20:46:55,624 - __main__ - WARNING - No valid walk-forward results for BTCUSDT_Reversion
2025-07-16 20:46:55,624 - __main__ - WARNING - No valid walk-forward results for BTCUSDT_Adaptive
2025-07-16 20:46:55,624 - __main__ - INFO - Saving optimization results
2025-07-16 20:46:55,624 - __main__ - WARNING - No validated strategies to save
2025-07-16 20:46:55,624 - __main__ - WARNING - Optimization completed but no strategies passed validation
2025-07-16 20:46:55,625 - __main__ - INFO - Binance client connection closed
2025-07-16 21:08:20,140 - __main__ - INFO - Ultratrader Orchestrator initialized
2025-07-16 21:08:20,140 - __main__ - INFO - Ultratrader Orchestrator initialized
2025-07-16 21:08:20,140 - __main__ - INFO - Starting Ultratrader optimization pipeline
2025-07-16 21:08:20,856 - __main__ - INFO - Binance client initialized successfully
2025-07-16 21:08:20,858 - __main__ - INFO - Optimizing strategies for symbols: ['BTCUSDT']
2025-07-16 21:08:20,858 - __main__ - INFO - Fetching market data for BTCUSDT
2025-07-16 21:08:22,627 - __main__ - INFO - Retrieved 1500 bars for BTCUSDT (15m)
2025-07-16 21:08:22,627 - __main__ - INFO - Retrieved 1500 bars for BTCUSDT (1h)
2025-07-16 21:08:22,627 - __main__ - INFO - Optimizing Trend strategy for BTCUSDT
2025-07-16 21:08:24,418 - root - WARNING - Trial failed: 
2025-07-16 21:08:24,476 - root - WARNING - Trial failed: 
2025-07-16 21:08:24,499 - root - WARNING - Trial failed: 
2025-07-16 21:08:24,562 - root - WARNING - Trial failed: 
2025-07-16 21:08:24,606 - root - WARNING - Trial failed: 
2025-07-16 21:08:25,365 - root - WARNING - Trial failed: 
2025-07-16 21:08:25,506 - root - WARNING - Trial failed: 
2025-07-16 21:08:25,644 - root - WARNING - Trial failed: 
2025-07-16 21:08:25,791 - root - WARNING - Trial failed: 
2025-07-16 21:08:26,900 - root - WARNING - Trial failed: 
2025-07-16 21:08:26,976 - root - WARNING - Trial failed: 
2025-07-16 21:08:27,057 - root - WARNING - Trial failed: 
2025-07-16 21:08:27,129 - root - WARNING - Trial failed: 
2025-07-16 21:08:27,285 - root - WARNING - Trial failed: 
2025-07-16 21:08:27,393 - __main__ - INFO - BTCUSDT Trend: Best score = 6.290
2025-07-16 21:08:27,393 - __main__ - INFO - Running walk-forward validation for BTCUSDT Trend
2025-07-16 21:08:27,393 - __main__ - WARNING - No valid walk-forward windows for BTCUSDT Trend
2025-07-16 21:08:27,393 - __main__ - INFO - Optimizing Reversion strategy for BTCUSDT
2025-07-16 21:08:27,416 - root - WARNING - Trial failed: 
2025-07-16 21:08:27,434 - root - WARNING - Trial failed: 
2025-07-16 21:08:27,455 - root - WARNING - Trial failed: 
2025-07-16 21:08:27,474 - root - WARNING - Trial failed: 
2025-07-16 21:08:27,493 - root - WARNING - Trial failed: 
2025-07-16 21:08:27,515 - root - WARNING - Trial failed: 
2025-07-16 21:08:27,541 - root - WARNING - Trial failed: 
2025-07-16 21:08:27,562 - root - WARNING - Trial failed: 
2025-07-16 21:08:27,581 - root - WARNING - Trial failed: 
2025-07-16 21:08:27,601 - root - WARNING - Trial failed: 
2025-07-16 21:08:27,621 - root - WARNING - Trial failed: 
2025-07-16 21:08:27,640 - root - WARNING - Trial failed: 
2025-07-16 21:08:27,708 - root - WARNING - Trial failed: 
2025-07-16 21:08:27,780 - root - WARNING - Trial failed: 
2025-07-16 21:08:27,848 - root - WARNING - Trial failed: 
2025-07-16 21:08:27,920 - root - WARNING - Trial failed: 
2025-07-16 21:08:27,990 - root - WARNING - Trial failed: 
2025-07-16 21:08:28,065 - root - WARNING - Trial failed: 
2025-07-16 21:08:28,134 - root - WARNING - Trial failed: 
2025-07-16 21:08:28,208 - root - WARNING - Trial failed: 
2025-07-16 21:08:28,279 - root - WARNING - Trial failed: 
2025-07-16 21:08:28,357 - root - WARNING - Trial failed: 
2025-07-16 21:08:28,428 - root - WARNING - Trial failed: 
2025-07-16 21:08:28,502 - root - WARNING - Trial failed: 
2025-07-16 21:08:28,580 - root - WARNING - Trial failed: 
2025-07-16 21:08:28,652 - root - WARNING - Trial failed: 
2025-07-16 21:08:28,726 - root - WARNING - Trial failed: 
2025-07-16 21:08:28,804 - root - WARNING - Trial failed: 
2025-07-16 21:08:28,878 - root - WARNING - Trial failed: 
2025-07-16 21:08:28,951 - root - WARNING - Trial failed: 
2025-07-16 21:08:29,025 - root - WARNING - Trial failed: 
2025-07-16 21:08:29,105 - root - WARNING - Trial failed: 
2025-07-16 21:08:29,177 - root - WARNING - Trial failed: 
2025-07-16 21:08:29,252 - root - WARNING - Trial failed: 
2025-07-16 21:08:29,339 - root - WARNING - Trial failed: 
2025-07-16 21:08:29,414 - root - WARNING - Trial failed: 
2025-07-16 21:08:29,492 - root - WARNING - Trial failed: 
2025-07-16 21:08:29,577 - root - WARNING - Trial failed: 
2025-07-16 21:08:29,653 - root - WARNING - Trial failed: 
2025-07-16 21:08:29,731 - root - WARNING - Trial failed: 
2025-07-16 21:08:29,808 - root - WARNING - Trial failed: 
2025-07-16 21:08:29,900 - root - WARNING - Trial failed: 
2025-07-16 21:08:29,981 - root - WARNING - Trial failed: 
2025-07-16 21:08:30,068 - root - WARNING - Trial failed: 
2025-07-16 21:08:30,152 - root - WARNING - Trial failed: 
2025-07-16 21:08:30,231 - root - WARNING - Trial failed: 
2025-07-16 21:08:30,316 - root - WARNING - Trial failed: 
2025-07-16 21:08:30,443 - root - WARNING - Trial failed: 
2025-07-16 21:08:30,523 - root - WARNING - Trial failed: 
2025-07-16 21:08:30,611 - root - WARNING - Trial failed: 
2025-07-16 21:08:30,645 - __main__ - INFO - BTCUSDT Reversion: Best score = -999.000
2025-07-16 21:08:30,645 - __main__ - INFO - Running walk-forward validation for BTCUSDT Reversion
2025-07-16 21:08:30,645 - __main__ - WARNING - No valid walk-forward windows for BTCUSDT Reversion
2025-07-16 21:08:30,645 - __main__ - INFO - Optimizing Adaptive strategy for BTCUSDT
2025-07-16 21:08:30,678 - root - WARNING - Trial failed: 
2025-07-16 21:08:30,708 - root - WARNING - Trial failed: 
2025-07-16 21:08:30,739 - root - WARNING - Trial failed: 
2025-07-16 21:08:30,768 - root - WARNING - Trial failed: 
2025-07-16 21:08:30,801 - root - WARNING - Trial failed: 
2025-07-16 21:08:30,838 - root - WARNING - Trial failed: 
2025-07-16 21:08:30,934 - root - WARNING - Trial failed: 
2025-07-16 21:08:31,031 - root - WARNING - Trial failed: 
2025-07-16 21:08:31,133 - root - WARNING - Trial failed: 
2025-07-16 21:08:31,232 - root - WARNING - Trial failed: 
2025-07-16 21:08:31,331 - root - WARNING - Trial failed: 
2025-07-16 21:08:31,443 - root - WARNING - Trial failed: 
2025-07-16 21:08:31,542 - root - WARNING - Trial failed: 
2025-07-16 21:08:31,650 - root - WARNING - Trial failed: 
2025-07-16 21:08:31,755 - root - WARNING - Trial failed: 
2025-07-16 21:08:31,860 - root - WARNING - Trial failed: 
2025-07-16 21:08:31,963 - root - WARNING - Trial failed: 
2025-07-16 21:08:32,063 - root - WARNING - Trial failed: 
2025-07-16 21:08:32,172 - root - WARNING - Trial failed: 
2025-07-16 21:08:32,275 - root - WARNING - Trial failed: 
2025-07-16 21:08:32,489 - root - WARNING - Trial failed: 
2025-07-16 21:08:32,850 - __main__ - INFO - BTCUSDT Adaptive: Best score = 1.000
2025-07-16 21:08:32,851 - __main__ - INFO - Running walk-forward validation for BTCUSDT Adaptive
2025-07-16 21:08:32,851 - __main__ - WARNING - No valid walk-forward windows for BTCUSDT Adaptive
2025-07-16 21:08:32,851 - __main__ - INFO - Validating and scoring strategies
2025-07-16 21:08:32,851 - __main__ - WARNING - No valid walk-forward results for BTCUSDT_Trend
2025-07-16 21:08:32,851 - __main__ - WARNING - No valid walk-forward results for BTCUSDT_Reversion
2025-07-16 21:08:32,851 - __main__ - WARNING - No valid walk-forward results for BTCUSDT_Adaptive
2025-07-16 21:08:32,851 - __main__ - INFO - Saving optimization results
2025-07-16 21:08:32,852 - __main__ - WARNING - No validated strategies to save
2025-07-16 21:08:32,852 - __main__ - WARNING - Optimization completed but no strategies passed validation
2025-07-16 21:08:32,852 - __main__ - INFO - Binance client connection closed
2025-07-16 21:17:36,538 - __main__ - INFO - Ultratrader Orchestrator initialized
2025-07-16 21:17:36,539 - __main__ - INFO - Ultratrader Orchestrator initialized
2025-07-16 21:17:36,539 - __main__ - INFO - Starting Ultratrader optimization pipeline
2025-07-16 21:17:37,258 - __main__ - INFO - Binance client initialized successfully
2025-07-16 21:17:37,258 - __main__ - INFO - Optimizing strategies for symbols: ['BTCUSDT']
2025-07-16 21:17:37,258 - __main__ - INFO - Fetching market data for BTCUSDT
2025-07-16 21:17:37,283 - __main__ - INFO - Retrieved 1500 bars for BTCUSDT (15m)
2025-07-16 21:17:37,283 - __main__ - INFO - Retrieved 1500 bars for BTCUSDT (1h)
2025-07-16 21:17:37,283 - __main__ - INFO - Optimizing Trend strategy for BTCUSDT
2025-07-16 21:17:42,174 - __main__ - INFO - BTCUSDT Trend: Best score = 6.802
2025-07-16 21:17:42,174 - __main__ - INFO - Running walk-forward validation for BTCUSDT Trend
2025-07-16 21:17:42,174 - __main__ - WARNING - No valid walk-forward windows for BTCUSDT Trend
2025-07-16 21:17:42,174 - __main__ - INFO - Optimizing Reversion strategy for BTCUSDT
2025-07-16 21:17:45,890 - __main__ - INFO - BTCUSDT Reversion: Best score = 9.099
2025-07-16 21:17:45,890 - __main__ - INFO - Running walk-forward validation for BTCUSDT Reversion
2025-07-16 21:17:45,890 - __main__ - WARNING - No valid walk-forward windows for BTCUSDT Reversion
2025-07-16 21:17:45,890 - __main__ - INFO - Optimizing Adaptive strategy for BTCUSDT
2025-07-16 21:17:48,870 - __main__ - INFO - BTCUSDT Adaptive: Best score = 7.107
2025-07-16 21:17:48,870 - __main__ - INFO - Running walk-forward validation for BTCUSDT Adaptive
2025-07-16 21:17:48,870 - __main__ - WARNING - No valid walk-forward windows for BTCUSDT Adaptive
2025-07-16 21:17:48,872 - __main__ - INFO - Validating and scoring strategies
2025-07-16 21:17:48,872 - __main__ - WARNING - No valid walk-forward results for BTCUSDT_Trend
2025-07-16 21:17:48,872 - __main__ - WARNING - No valid walk-forward results for BTCUSDT_Reversion
2025-07-16 21:17:48,872 - __main__ - WARNING - No valid walk-forward results for BTCUSDT_Adaptive
2025-07-16 21:17:48,872 - __main__ - INFO - Saving optimization results
2025-07-16 21:17:48,872 - __main__ - WARNING - No validated strategies to save
2025-07-16 21:17:48,872 - __main__ - WARNING - Optimization completed but no strategies passed validation
2025-07-16 21:17:48,873 - __main__ - INFO - Binance client connection closed
2025-07-16 21:19:17,114 - __main__ - INFO - Ultratrader Orchestrator initialized
2025-07-16 21:19:17,114 - __main__ - INFO - Ultratrader Orchestrator initialized
2025-07-16 21:19:17,115 - __main__ - INFO - Starting Ultratrader optimization pipeline
2025-07-16 21:19:17,115 - __main__ - ERROR - Failed to initialize Binance client: Binance API credentials not found. Please set BINANCE_API_KEY and BINANCE_SECRET_KEY environment variables.
2025-07-16 21:19:17,115 - __main__ - ERROR - Critical error in optimization pipeline: Binance API credentials not found. Please set BINANCE_API_KEY and BINANCE_SECRET_KEY environment variables.
2025-07-16 21:19:17,116 - __main__ - ERROR - Fatal error: Binance API credentials not found. Please set BINANCE_API_KEY and BINANCE_SECRET_KEY environment variables.
2025-07-16 21:21:15,991 - __main__ - INFO - Ultratrader Orchestrator initialized
2025-07-16 21:21:15,991 - __main__ - INFO - Ultratrader Orchestrator initialized
2025-07-16 21:21:15,992 - __main__ - INFO - Starting Ultratrader optimization pipeline
2025-07-16 21:21:16,941 - __main__ - INFO - Binance client initialized successfully
2025-07-16 21:21:16,941 - __main__ - INFO - Optimizing strategies for symbols: ['BTCUSDT']
2025-07-16 21:21:16,941 - __main__ - INFO - Fetching market data for BTCUSDT
2025-07-16 21:21:16,952 - __main__ - INFO - Retrieved 1500 bars for BTCUSDT (15m)
2025-07-16 21:21:16,952 - __main__ - INFO - Retrieved 1500 bars for BTCUSDT (1h)
2025-07-16 21:21:16,952 - __main__ - INFO - Optimizing Trend strategy for BTCUSDT
2025-07-16 21:21:21,705 - __main__ - INFO - BTCUSDT Trend: Best score = 6.802
2025-07-16 21:21:21,705 - __main__ - INFO - Running walk-forward validation for BTCUSDT Trend
2025-07-16 21:21:21,705 - __main__ - WARNING - No valid walk-forward windows for BTCUSDT Trend, using optimization results only
2025-07-16 21:21:21,705 - __main__ - INFO - BTCUSDT Trend: 1 valid WF windows, WF score = 5.000
2025-07-16 21:21:21,705 - __main__ - INFO - Optimizing Reversion strategy for BTCUSDT
2025-07-16 21:21:25,451 - __main__ - INFO - BTCUSDT Reversion: Best score = 9.099
2025-07-16 21:21:25,451 - __main__ - INFO - Running walk-forward validation for BTCUSDT Reversion
2025-07-16 21:21:25,451 - __main__ - WARNING - No valid walk-forward windows for BTCUSDT Reversion, using optimization results only
2025-07-16 21:21:25,451 - __main__ - INFO - BTCUSDT Reversion: 1 valid WF windows, WF score = 5.000
2025-07-16 21:21:25,452 - __main__ - INFO - Optimizing Adaptive strategy for BTCUSDT
2025-07-16 21:21:27,943 - __main__ - INFO - BTCUSDT Adaptive: Best score = 7.107
2025-07-16 21:21:27,944 - __main__ - INFO - Running walk-forward validation for BTCUSDT Adaptive
2025-07-16 21:21:27,944 - __main__ - WARNING - No valid walk-forward windows for BTCUSDT Adaptive, using optimization results only
2025-07-16 21:21:27,944 - __main__ - INFO - BTCUSDT Adaptive: 1 valid WF windows, WF score = 5.000
2025-07-16 21:21:27,944 - __main__ - INFO - Validating and scoring strategies
2025-07-16 21:21:27,944 - __main__ - INFO - BTCUSDT_Trend validated - Comprehensive Score: 79.51
2025-07-16 21:21:27,944 - __main__ - INFO - BTCUSDT_Reversion validated - Comprehensive Score: 90.99
2025-07-16 21:21:27,944 - __main__ - INFO - BTCUSDT_Adaptive validated - Comprehensive Score: 81.03
2025-07-16 21:21:27,944 - __main__ - INFO - Saving optimization results
2025-07-16 21:21:27,945 - __main__ - INFO - Saved 3 strategies to optimized_strategies.json
2025-07-16 21:21:27,947 - __main__ - INFO - Optimization completed successfully with 3 validated strategies
2025-07-16 21:21:27,947 - __main__ - INFO - Binance client connection closed
2025-07-16 21:29:58,826 - __main__ - INFO - Ultratrader Orchestrator initialized
2025-07-16 21:29:58,826 - __main__ - INFO - Ultratrader Orchestrator initialized
2025-07-16 21:29:58,826 - __main__ - INFO - Starting Ultratrader optimization pipeline
2025-07-16 21:29:59,659 - __main__ - INFO - Binance client initialized successfully
2025-07-16 21:29:59,659 - __main__ - INFO - Optimizing strategies for symbols: ['BTCUSDT']
2025-07-16 21:29:59,660 - __main__ - INFO - Fetching market data for BTCUSDT
2025-07-16 21:29:59,688 - __main__ - INFO - Retrieved 1500 bars for BTCUSDT (15m)
2025-07-16 21:29:59,688 - __main__ - INFO - Retrieved 1500 bars for BTCUSDT (1h)
2025-07-16 21:29:59,688 - __main__ - INFO - Optimizing Trend strategy for BTCUSDT
2025-07-16 21:30:09,209 - __main__ - INFO - BTCUSDT Trend: Best score = 6.444
2025-07-16 21:30:09,210 - __main__ - INFO - Running walk-forward validation for BTCUSDT Trend
2025-07-16 21:30:09,210 - __main__ - WARNING - No valid walk-forward windows for BTCUSDT Trend, using optimization results only
2025-07-16 21:30:09,210 - __main__ - INFO - BTCUSDT Trend: 1 valid WF windows, WF score = 5.000
2025-07-16 21:30:09,210 - __main__ - INFO - Optimizing Reversion strategy for BTCUSDT
2025-07-16 21:30:09,834 - root - WARNING - Trial failed: 
2025-07-16 21:30:17,006 - __main__ - INFO - BTCUSDT Reversion: Best score = 10.202
2025-07-16 21:30:17,006 - __main__ - INFO - Running walk-forward validation for BTCUSDT Reversion
2025-07-16 21:30:17,007 - __main__ - WARNING - No valid walk-forward windows for BTCUSDT Reversion, using optimization results only
2025-07-16 21:30:17,007 - __main__ - INFO - BTCUSDT Reversion: 1 valid WF windows, WF score = 5.000
2025-07-16 21:30:17,007 - __main__ - INFO - Optimizing Adaptive strategy for BTCUSDT
2025-07-16 21:30:24,862 - __main__ - INFO - BTCUSDT Adaptive: Best score = 9.234
2025-07-16 21:30:24,862 - __main__ - INFO - Running walk-forward validation for BTCUSDT Adaptive
2025-07-16 21:30:24,862 - __main__ - WARNING - No valid walk-forward windows for BTCUSDT Adaptive, using optimization results only
2025-07-16 21:30:24,863 - __main__ - INFO - BTCUSDT Adaptive: 1 valid WF windows, WF score = 5.000
2025-07-16 21:30:24,863 - __main__ - INFO - Validating and scoring strategies
2025-07-16 21:30:24,863 - __main__ - INFO - BTCUSDT_Trend validated - Comprehensive Score: 77.72
2025-07-16 21:30:24,863 - __main__ - INFO - BTCUSDT_Reversion validated - Comprehensive Score: 95.50
2025-07-16 21:30:24,863 - __main__ - INFO - BTCUSDT_Adaptive validated - Comprehensive Score: 91.67
2025-07-16 21:30:24,863 - __main__ - INFO - Saving optimization results
2025-07-16 21:30:24,864 - __main__ - INFO - Saved 3 strategies to optimized_strategies.json
2025-07-16 21:30:24,866 - __main__ - INFO - Optimization completed successfully with 3 validated strategies
2025-07-16 21:30:24,867 - __main__ - INFO - Binance client connection closed
2025-07-16 21:54:12,747 - __main__ - INFO - Ultratrader Orchestrator initialized
2025-07-16 21:54:12,747 - __main__ - INFO - Ultratrader Orchestrator initialized
2025-07-16 21:54:12,747 - __main__ - INFO - Starting Ultratrader optimization pipeline
2025-07-16 21:54:13,573 - __main__ - INFO - Binance client initialized successfully
2025-07-16 21:54:13,573 - __main__ - INFO - Optimizing strategies for symbols: ['BTCUSDT']
2025-07-16 21:54:13,573 - __main__ - INFO - Fetching market data for BTCUSDT
2025-07-16 21:54:13,586 - __main__ - INFO - Retrieved 1500 bars for BTCUSDT (15m)
2025-07-16 21:54:13,586 - __main__ - INFO - Retrieved 1500 bars for BTCUSDT (1h)
2025-07-16 21:54:13,586 - __main__ - INFO - Optimizing Trend strategy for BTCUSDT
2025-07-16 21:54:27,594 - __main__ - INFO - BTCUSDT Trend: Best score = 6.401
2025-07-16 21:54:27,594 - __main__ - INFO - Running walk-forward validation for BTCUSDT Trend
2025-07-16 21:54:27,594 - __main__ - WARNING - No valid walk-forward windows for BTCUSDT Trend, using optimization results only
2025-07-16 21:54:27,594 - __main__ - INFO - BTCUSDT Trend: 1 valid WF windows, WF score = 5.000
2025-07-16 21:54:27,594 - __main__ - INFO - Optimizing Reversion strategy for BTCUSDT
2025-07-16 21:54:28,208 - root - WARNING - Trial failed: 
2025-07-16 21:54:34,249 - root - WARNING - Trial failed: 
2025-07-16 21:54:40,058 - __main__ - INFO - BTCUSDT Reversion: Best score = 7.328
2025-07-16 21:54:40,059 - __main__ - INFO - Running walk-forward validation for BTCUSDT Reversion
2025-07-16 21:54:40,059 - __main__ - WARNING - No valid walk-forward windows for BTCUSDT Reversion, using optimization results only
2025-07-16 21:54:40,059 - __main__ - INFO - BTCUSDT Reversion: 1 valid WF windows, WF score = 5.000
2025-07-16 21:54:40,059 - __main__ - INFO - Optimizing Adaptive strategy for BTCUSDT
2025-07-16 21:54:50,845 - __main__ - INFO - BTCUSDT Adaptive: Best score = 7.358
2025-07-16 21:54:50,845 - __main__ - INFO - Running walk-forward validation for BTCUSDT Adaptive
2025-07-16 21:54:50,845 - __main__ - WARNING - No valid walk-forward windows for BTCUSDT Adaptive, using optimization results only
2025-07-16 21:54:50,845 - __main__ - INFO - BTCUSDT Adaptive: 1 valid WF windows, WF score = 5.000
2025-07-16 21:54:50,845 - __main__ - INFO - Validating and scoring strategies
2025-07-16 21:54:50,845 - __main__ - INFO - BTCUSDT_Trend validated - Comprehensive Score: 77.50
2025-07-16 21:54:50,845 - __main__ - INFO - BTCUSDT_Reversion validated - Comprehensive Score: 82.14
2025-07-16 21:54:50,845 - __main__ - INFO - BTCUSDT_Adaptive validated - Comprehensive Score: 82.29
2025-07-16 21:54:50,846 - __main__ - INFO - Saving optimization results
2025-07-16 21:54:50,847 - __main__ - INFO - Saved 3 strategies to optimized_strategies.json
2025-07-16 21:54:50,848 - __main__ - INFO - Optimization completed successfully with 3 validated strategies
2025-07-16 21:54:50,848 - __main__ - INFO - Binance client connection closed
2025-07-16 21:57:00,393 - __main__ - INFO - Ultratrader Orchestrator initialized
2025-07-16 21:57:00,393 - __main__ - INFO - Ultratrader Orchestrator initialized
2025-07-16 21:57:00,394 - __main__ - INFO - Starting Ultratrader optimization pipeline
2025-07-16 21:57:01,108 - __main__ - INFO - Binance client initialized successfully
2025-07-16 21:57:01,108 - __main__ - INFO - Optimizing strategies for symbols: ['BTCUSDT']
2025-07-16 21:57:01,109 - __main__ - INFO - Fetching market data for BTCUSDT
2025-07-16 21:57:01,120 - __main__ - INFO - Retrieved 1500 bars for BTCUSDT (15m)
2025-07-16 21:57:01,120 - __main__ - INFO - Retrieved 1500 bars for BTCUSDT (1h)
2025-07-16 21:57:01,120 - __main__ - INFO - Optimizing Trend strategy for BTCUSDT
2025-07-16 21:57:08,167 - __main__ - INFO - BTCUSDT Trend: Best score = 3.266
2025-07-16 21:57:08,169 - __main__ - INFO - Running walk-forward validation for BTCUSDT Trend
2025-07-16 21:57:08,169 - __main__ - WARNING - No valid walk-forward windows for BTCUSDT Trend, using optimization results only
2025-07-16 21:57:08,169 - __main__ - INFO - BTCUSDT Trend: 1 valid WF windows, WF score = 5.000
2025-07-16 21:57:08,169 - __main__ - INFO - Optimizing Reversion strategy for BTCUSDT
2025-07-16 21:57:13,653 - __main__ - INFO - BTCUSDT Reversion: Best score = 7.347
2025-07-16 21:57:13,653 - __main__ - INFO - Running walk-forward validation for BTCUSDT Reversion
2025-07-16 21:57:13,654 - __main__ - WARNING - No valid walk-forward windows for BTCUSDT Reversion, using optimization results only
2025-07-16 21:57:13,654 - __main__ - INFO - BTCUSDT Reversion: 1 valid WF windows, WF score = 5.000
2025-07-16 21:57:13,654 - __main__ - INFO - Optimizing Adaptive strategy for BTCUSDT
2025-07-16 21:57:14,320 - root - WARNING - Trial failed: 
2025-07-16 21:57:18,634 - __main__ - INFO - BTCUSDT Adaptive: Best score = 6.956
2025-07-16 21:57:18,634 - __main__ - INFO - Running walk-forward validation for BTCUSDT Adaptive
2025-07-16 21:57:18,634 - __main__ - WARNING - No valid walk-forward windows for BTCUSDT Adaptive, using optimization results only
2025-07-16 21:57:18,634 - __main__ - INFO - BTCUSDT Adaptive: 1 valid WF windows, WF score = 5.000
2025-07-16 21:57:18,634 - __main__ - INFO - Validating and scoring strategies
2025-07-16 21:57:18,635 - __main__ - INFO - BTCUSDT_Trend validated - Comprehensive Score: 61.83
2025-07-16 21:57:18,635 - __main__ - INFO - BTCUSDT_Reversion validated - Comprehensive Score: 82.23
2025-07-16 21:57:18,635 - __main__ - INFO - BTCUSDT_Adaptive validated - Comprehensive Score: 80.28
2025-07-16 21:57:18,635 - __main__ - INFO - Saving optimization results
2025-07-16 21:57:18,635 - __main__ - INFO - Saved 3 strategies to optimized_strategies.json
2025-07-16 21:57:18,637 - __main__ - INFO - Optimization completed successfully with 3 validated strategies
2025-07-16 21:57:18,638 - __main__ - INFO - Binance client connection closed
2025-07-16 21:58:38,300 - __main__ - INFO - Ultratrader Orchestrator initialized
2025-07-16 21:58:38,301 - __main__ - INFO - Ultratrader Orchestrator initialized
2025-07-16 21:58:38,301 - __main__ - INFO - Starting Ultratrader optimization pipeline
2025-07-16 21:58:39,018 - __main__ - INFO - Binance client initialized successfully
2025-07-16 21:58:39,018 - __main__ - INFO - Optimizing strategies for symbols: ['BTCUSDT']
2025-07-16 21:58:39,018 - __main__ - INFO - Fetching market data for BTCUSDT
2025-07-16 21:58:39,030 - __main__ - INFO - Retrieved 1500 bars for BTCUSDT (15m)
2025-07-16 21:58:39,030 - __main__ - INFO - Retrieved 1500 bars for BTCUSDT (1h)
2025-07-16 21:58:39,030 - __main__ - INFO - Optimizing Trend strategy for BTCUSDT
2025-07-16 21:58:46,212 - __main__ - INFO - BTCUSDT Trend: Best score = 6.556
2025-07-16 21:58:46,212 - __main__ - INFO - Running walk-forward validation for BTCUSDT Trend
2025-07-16 21:58:46,212 - __main__ - WARNING - No valid walk-forward windows for BTCUSDT Trend, using optimization results only
2025-07-16 21:58:46,212 - __main__ - INFO - BTCUSDT Trend: 1 valid WF windows, WF score = 5.000
2025-07-16 21:58:46,213 - __main__ - INFO - Optimizing Reversion strategy for BTCUSDT
2025-07-16 21:58:52,679 - __main__ - INFO - BTCUSDT Reversion: Best score = 8.040
2025-07-16 21:58:52,680 - __main__ - INFO - Running walk-forward validation for BTCUSDT Reversion
2025-07-16 21:58:52,680 - __main__ - WARNING - No valid walk-forward windows for BTCUSDT Reversion, using optimization results only
2025-07-16 21:58:52,680 - __main__ - INFO - BTCUSDT Reversion: 1 valid WF windows, WF score = 5.000
2025-07-16 21:58:52,680 - __main__ - INFO - Optimizing Adaptive strategy for BTCUSDT
2025-07-16 21:58:53,311 - root - WARNING - Trial failed: 
2025-07-16 22:02:42,580 - __main__ - INFO - Ultratrader Orchestrator initialized
2025-07-16 22:02:42,580 - __main__ - INFO - Ultratrader Orchestrator initialized
2025-07-16 22:02:42,581 - __main__ - INFO - Starting Ultratrader optimization pipeline
2025-07-16 22:02:43,533 - __main__ - INFO - Binance client initialized successfully
2025-07-16 22:02:43,533 - __main__ - INFO - Optimizing strategies for symbols: ['BTCUSDT']
2025-07-16 22:02:43,533 - __main__ - INFO - Fetching market data for BTCUSDT
2025-07-16 22:02:43,547 - __main__ - INFO - Retrieved 1500 bars for BTCUSDT (15m)
2025-07-16 22:02:43,547 - __main__ - INFO - Retrieved 1500 bars for BTCUSDT (1h)
2025-07-16 22:02:43,547 - __main__ - INFO - Optimizing Trend strategy for BTCUSDT
2025-07-16 22:02:50,542 - __main__ - INFO - BTCUSDT Trend: Best score = 6.556
2025-07-16 22:02:50,542 - __main__ - INFO - Running walk-forward validation for BTCUSDT Trend
2025-07-16 22:02:50,542 - __main__ - INFO - Total data available: 15.6 days (1500 bars)
2025-07-16 22:02:50,542 - __main__ - INFO - Walk-forward parameters: IS=7d, OOS=3d, step=1d
2025-07-16 22:02:58,934 - __main__ - INFO - BTCUSDT Trend: 6 valid WF windows, WF score = 0.600
2025-07-16 22:02:58,934 - __main__ - INFO - Optimizing Reversion strategy for BTCUSDT
2025-07-16 22:03:04,174 - __main__ - INFO - BTCUSDT Reversion: Best score = 8.040
2025-07-16 22:03:04,175 - __main__ - INFO - Running walk-forward validation for BTCUSDT Reversion
2025-07-16 22:03:04,175 - __main__ - INFO - Total data available: 15.6 days (1500 bars)
2025-07-16 22:03:04,175 - __main__ - INFO - Walk-forward parameters: IS=7d, OOS=3d, step=1d
2025-07-16 22:03:11,384 - __main__ - INFO - BTCUSDT Reversion: 6 valid WF windows, WF score = 0.600
2025-07-16 22:03:11,384 - __main__ - INFO - Optimizing Adaptive strategy for BTCUSDT
2025-07-16 22:03:11,998 - root - WARNING - Trial failed: 
2025-07-16 22:03:16,223 - __main__ - INFO - BTCUSDT Adaptive: Best score = 7.033
2025-07-16 22:03:16,223 - __main__ - INFO - Running walk-forward validation for BTCUSDT Adaptive
2025-07-16 22:03:16,223 - __main__ - INFO - Total data available: 15.6 days (1500 bars)
2025-07-16 22:03:16,224 - __main__ - INFO - Walk-forward parameters: IS=7d, OOS=3d, step=1d
2025-07-16 22:03:28,728 - __main__ - INFO - BTCUSDT Adaptive: 6 valid WF windows, WF score = 0.600
2025-07-16 22:03:28,728 - __main__ - INFO - Validating and scoring strategies
2025-07-16 22:03:28,729 - __main__ - INFO - BTCUSDT_Trend validated - Comprehensive Score: 53.78
2025-07-16 22:03:28,729 - __main__ - INFO - BTCUSDT_Reversion validated - Comprehensive Score: 61.20
2025-07-16 22:03:28,729 - __main__ - INFO - BTCUSDT_Adaptive validated - Comprehensive Score: 56.17
2025-07-16 22:03:28,729 - __main__ - INFO - Saving optimization results
2025-07-16 22:03:28,729 - __main__ - INFO - Saved 3 strategies to optimized_strategies.json
2025-07-16 22:03:28,731 - __main__ - INFO - Optimization completed successfully with 3 validated strategies
2025-07-16 22:03:28,731 - __main__ - INFO - Binance client connection closed
2025-07-16 22:11:14,233 - __main__ - INFO - Ultratrader Orchestrator initialized
2025-07-16 22:11:14,233 - __main__ - INFO - Ultratrader Orchestrator initialized
2025-07-16 22:11:14,234 - __main__ - INFO - Starting Ultratrader optimization pipeline
2025-07-16 22:11:14,921 - __main__ - INFO - Binance client initialized successfully
2025-07-16 22:11:14,922 - __main__ - INFO - Optimizing strategies for symbols: ['BTCUSDT']
2025-07-16 22:11:14,922 - __main__ - INFO - Fetching market data for BTCUSDT
2025-07-16 22:11:16,660 - __main__ - INFO - Retrieved 1500 bars for BTCUSDT (15m)
2025-07-16 22:11:16,660 - __main__ - INFO - Retrieved 1500 bars for BTCUSDT (1h)
2025-07-16 22:11:16,660 - __main__ - INFO - Optimizing Trend strategy for BTCUSDT
2025-07-16 22:11:16,661 - __main__ - WARNING - Insufficient in-sample data for BTCUSDT Trend
2025-07-16 22:11:16,661 - __main__ - INFO - Running walk-forward validation for BTCUSDT Trend
2025-07-16 22:11:16,661 - __main__ - INFO - Total data available: 15.6 days (1500 bars)
2025-07-16 22:11:16,661 - __main__ - INFO - Walk-forward parameters: IS=7d, OOS=3d, step=1d
2025-07-16 22:11:22,772 - __main__ - INFO - BTCUSDT Trend: 6 valid WF windows, WF score = 0.660
2025-07-16 22:11:22,772 - __main__ - INFO - Optimizing Reversion strategy for BTCUSDT
2025-07-16 22:11:22,773 - __main__ - WARNING - Insufficient in-sample data for BTCUSDT Reversion
2025-07-16 22:11:22,773 - __main__ - INFO - Running walk-forward validation for BTCUSDT Reversion
2025-07-16 22:11:22,773 - __main__ - INFO - Total data available: 15.6 days (1500 bars)
2025-07-16 22:11:22,774 - __main__ - INFO - Walk-forward parameters: IS=7d, OOS=3d, step=1d
2025-07-16 22:11:23,037 - root - WARNING - Trial failed: 
2025-07-16 22:11:23,646 - root - WARNING - Trial failed: 
2025-07-16 22:11:26,653 - __main__ - INFO - BTCUSDT Reversion: 6 valid WF windows, WF score = 0.600
2025-07-16 22:11:26,653 - __main__ - INFO - Optimizing Adaptive strategy for BTCUSDT
2025-07-16 22:11:26,654 - __main__ - WARNING - Insufficient in-sample data for BTCUSDT Adaptive
2025-07-16 22:11:26,654 - __main__ - INFO - Running walk-forward validation for BTCUSDT Adaptive
2025-07-16 22:11:26,654 - __main__ - INFO - Total data available: 15.6 days (1500 bars)
2025-07-16 22:11:26,654 - __main__ - INFO - Walk-forward parameters: IS=7d, OOS=3d, step=1d
2025-07-16 22:11:33,145 - __main__ - INFO - BTCUSDT Adaptive: 6 valid WF windows, WF score = 0.600
2025-07-16 22:11:33,145 - __main__ - INFO - Validating and scoring strategies with anti-overfitting criteria
2025-07-16 22:11:33,146 - __main__ - INFO - Saving optimization results
2025-07-16 22:11:33,146 - __main__ - WARNING - No validated strategies to save
2025-07-16 22:11:33,146 - __main__ - WARNING - Optimization completed but no strategies passed validation
2025-07-16 22:11:33,146 - __main__ - INFO - Binance client connection closed
2025-07-16 22:14:19,402 - __main__ - INFO - Ultratrader Orchestrator initialized
2025-07-16 22:14:19,402 - __main__ - INFO - Ultratrader Orchestrator initialized
2025-07-16 22:14:19,402 - __main__ - INFO - Starting Ultratrader optimization pipeline
2025-07-16 22:14:20,094 - __main__ - INFO - Binance client initialized successfully
2025-07-16 22:14:20,094 - __main__ - INFO - Optimizing strategies for symbols: ['BTCUSDT']
2025-07-16 22:14:20,094 - __main__ - INFO - Fetching market data for BTCUSDT
2025-07-16 22:15:18,117 - __main__ - INFO - Binance client connection closed
2025-07-16 22:15:55,288 - __main__ - INFO - Ultratrader Orchestrator initialized
2025-07-16 22:15:55,289 - __main__ - INFO - Ultratrader Orchestrator initialized
2025-07-16 22:15:55,289 - __main__ - INFO - Starting Ultratrader optimization pipeline
2025-07-16 22:15:55,976 - __main__ - INFO - Binance client initialized successfully
2025-07-16 22:15:55,977 - __main__ - INFO - Optimizing strategies for symbols: ['BTCUSDT']
2025-07-16 22:15:55,977 - __main__ - INFO - Fetching market data for BTCUSDT
2025-07-16 22:16:45,816 - __main__ - INFO - Binance client connection closed
2025-07-16 22:18:08,908 - __main__ - INFO - Ultratrader Orchestrator initialized
2025-07-16 22:18:08,909 - __main__ - INFO - Ultratrader Orchestrator initialized
2025-07-16 22:18:08,909 - __main__ - INFO - Starting Ultratrader optimization pipeline
2025-07-16 22:18:09,596 - __main__ - INFO - Binance client initialized successfully
2025-07-16 22:18:09,596 - __main__ - INFO - Optimizing strategies for symbols: ['BTCUSDT']
2025-07-16 22:18:09,596 - __main__ - INFO - Fetching market data for BTCUSDT
2025-07-16 22:18:15,352 - __main__ - INFO - Retrieved 1000 bars for BTCUSDT (15m)
2025-07-16 22:18:15,352 - __main__ - INFO - Retrieved 1000 bars for BTCUSDT (1h)
2025-07-16 22:18:15,352 - __main__ - INFO - Optimizing Trend strategy for BTCUSDT
2025-07-16 22:18:15,352 - __main__ - WARNING - Insufficient in-sample data for BTCUSDT Trend
2025-07-16 22:18:15,352 - __main__ - INFO - Running walk-forward validation for BTCUSDT Trend
2025-07-16 22:18:15,352 - __main__ - INFO - Total data available: 10.4 days (1000 bars)
2025-07-16 22:18:15,352 - __main__ - INFO - Walk-forward parameters: IS=7d, OOS=2d, step=1d
2025-07-16 22:18:18,598 - __main__ - INFO - BTCUSDT Trend: 2 valid WF windows, WF score = 0.242
2025-07-16 22:18:18,598 - __main__ - INFO - Optimizing Reversion strategy for BTCUSDT
2025-07-16 22:18:18,598 - __main__ - WARNING - Insufficient in-sample data for BTCUSDT Reversion
2025-07-16 22:18:18,598 - __main__ - INFO - Running walk-forward validation for BTCUSDT Reversion
2025-07-16 22:18:18,598 - __main__ - INFO - Total data available: 10.4 days (1000 bars)
2025-07-16 22:18:18,598 - __main__ - INFO - Walk-forward parameters: IS=7d, OOS=2d, step=1d
2025-07-16 22:18:18,825 - root - WARNING - Trial failed: 
2025-07-16 22:18:19,420 - root - WARNING - Trial failed: 
2025-07-16 22:18:19,802 - __main__ - INFO - BTCUSDT Reversion: 2 valid WF windows, WF score = 0.221
2025-07-16 22:18:19,803 - __main__ - INFO - Optimizing Adaptive strategy for BTCUSDT
2025-07-16 22:18:19,803 - __main__ - WARNING - Insufficient in-sample data for BTCUSDT Adaptive
2025-07-16 22:18:19,803 - __main__ - INFO - Running walk-forward validation for BTCUSDT Adaptive
2025-07-16 22:18:19,803 - __main__ - INFO - Total data available: 10.4 days (1000 bars)
2025-07-16 22:18:19,803 - __main__ - INFO - Walk-forward parameters: IS=7d, OOS=2d, step=1d
2025-07-16 22:18:21,882 - __main__ - INFO - BTCUSDT Adaptive: 2 valid WF windows, WF score = 0.717
2025-07-16 22:18:21,883 - __main__ - INFO - Validating and scoring strategies with anti-overfitting criteria
2025-07-16 22:18:21,883 - __main__ - INFO - Saving optimization results
2025-07-16 22:18:21,883 - __main__ - WARNING - No validated strategies to save
2025-07-16 22:18:21,883 - __main__ - WARNING - Optimization completed but no strategies passed validation
2025-07-16 22:18:21,883 - __main__ - INFO - Binance client connection closed
2025-07-16 22:21:47,226 - __main__ - INFO - Ultratrader Orchestrator initialized
2025-07-16 22:21:47,226 - __main__ - INFO - Ultratrader Orchestrator initialized
2025-07-16 22:21:47,226 - __main__ - INFO - Starting Ultratrader optimization pipeline
2025-07-16 22:21:47,923 - __main__ - INFO - Binance client initialized successfully
2025-07-16 22:21:47,923 - __main__ - INFO - Optimizing strategies for symbols: ['BTCUSDT']
2025-07-16 22:21:47,923 - __main__ - INFO - Fetching market data for BTCUSDT
2025-07-16 22:21:51,078 - __main__ - INFO - Retrieved 1000 bars for BTCUSDT (15m)
2025-07-16 22:21:51,078 - __main__ - INFO - Retrieved 1000 bars for BTCUSDT (1h)
2025-07-16 22:21:51,078 - __main__ - INFO - Optimizing Trend strategy for BTCUSDT
2025-07-16 22:21:51,079 - __main__ - ERROR - Error optimizing BTCUSDT Trend: 'OptimizationConfig' object has no attribute 'optuna_timeout_per_phase'
2025-07-16 22:21:51,079 - __main__ - INFO - Running walk-forward validation for BTCUSDT Trend
2025-07-16 22:21:51,079 - __main__ - INFO - Total data available: 10.4 days (1000 bars)
2025-07-16 22:21:51,079 - __main__ - INFO - Walk-forward parameters: IS=7d, OOS=2d, step=1d
2025-07-16 22:21:54,122 - __main__ - INFO - BTCUSDT Trend: 2 valid WF windows, WF score = 0.224
2025-07-16 22:21:54,122 - __main__ - INFO - Optimizing Reversion strategy for BTCUSDT
2025-07-16 22:21:54,123 - __main__ - ERROR - Error optimizing BTCUSDT Reversion: 'OptimizationConfig' object has no attribute 'optuna_timeout_per_phase'
2025-07-16 22:21:54,123 - __main__ - INFO - Running walk-forward validation for BTCUSDT Reversion
2025-07-16 22:21:54,123 - __main__ - INFO - Total data available: 10.4 days (1000 bars)
2025-07-16 22:21:54,123 - __main__ - INFO - Walk-forward parameters: IS=7d, OOS=2d, step=1d
2025-07-16 22:21:55,291 - __main__ - INFO - BTCUSDT Reversion: 2 valid WF windows, WF score = 0.241
2025-07-16 22:21:55,291 - __main__ - INFO - Optimizing Adaptive strategy for BTCUSDT
2025-07-16 22:21:55,291 - __main__ - ERROR - Error optimizing BTCUSDT Adaptive: 'OptimizationConfig' object has no attribute 'optuna_timeout_per_phase'
2025-07-16 22:21:55,292 - __main__ - INFO - Running walk-forward validation for BTCUSDT Adaptive
2025-07-16 22:21:55,292 - __main__ - INFO - Total data available: 10.4 days (1000 bars)
2025-07-16 22:21:55,292 - __main__ - INFO - Walk-forward parameters: IS=7d, OOS=2d, step=1d
2025-07-16 22:21:57,323 - __main__ - INFO - BTCUSDT Adaptive: 2 valid WF windows, WF score = 0.244
2025-07-16 22:21:57,323 - __main__ - INFO - Validating and scoring strategies with anti-overfitting criteria
2025-07-16 22:21:57,323 - __main__ - INFO - Saving optimization results
2025-07-16 22:21:57,323 - __main__ - WARNING - No validated strategies to save
2025-07-16 22:21:57,323 - __main__ - WARNING - Optimization completed but no strategies passed validation
2025-07-16 22:21:57,323 - __main__ - INFO - Binance client connection closed
2025-07-16 22:22:32,430 - __main__ - INFO - Ultratrader Orchestrator initialized
2025-07-16 22:22:32,430 - __main__ - INFO - Ultratrader Orchestrator initialized
2025-07-16 22:22:32,430 - __main__ - INFO - Starting Ultratrader optimization pipeline
2025-07-16 22:22:33,112 - __main__ - INFO - Binance client initialized successfully
2025-07-16 22:22:33,112 - __main__ - INFO - Optimizing strategies for symbols: ['BTCUSDT']
2025-07-16 22:22:33,112 - __main__ - INFO - Fetching market data for BTCUSDT
2025-07-16 22:22:33,123 - __main__ - INFO - Retrieved 1000 bars for BTCUSDT (15m)
2025-07-16 22:22:33,123 - __main__ - INFO - Retrieved 1000 bars for BTCUSDT (1h)
2025-07-16 22:22:33,123 - __main__ - INFO - Optimizing Trend strategy for BTCUSDT
2025-07-16 22:22:36,791 - __main__ - INFO - BTCUSDT Trend: Best score = 1.661
2025-07-16 22:22:36,791 - __main__ - INFO - Running walk-forward validation for BTCUSDT Trend
2025-07-16 22:22:36,791 - __main__ - INFO - Total data available: 10.4 days (1000 bars)
2025-07-16 22:22:36,791 - __main__ - INFO - Walk-forward parameters: IS=7d, OOS=2d, step=1d
2025-07-16 22:22:38,295 - __main__ - INFO - BTCUSDT Trend: 2 valid WF windows, WF score = 0.224
2025-07-16 22:22:38,295 - __main__ - INFO - Optimizing Reversion strategy for BTCUSDT
2025-07-16 22:22:40,083 - __main__ - INFO - BTCUSDT Reversion: Best score = 8.205
2025-07-16 22:22:40,083 - __main__ - INFO - Running walk-forward validation for BTCUSDT Reversion
2025-07-16 22:22:40,083 - __main__ - INFO - Total data available: 10.4 days (1000 bars)
2025-07-16 22:22:40,083 - __main__ - INFO - Walk-forward parameters: IS=7d, OOS=2d, step=1d
2025-07-16 22:22:41,260 - __main__ - INFO - BTCUSDT Reversion: 2 valid WF windows, WF score = 0.241
2025-07-16 22:22:41,260 - __main__ - INFO - Optimizing Adaptive strategy for BTCUSDT
2025-07-16 22:22:43,020 - __main__ - INFO - BTCUSDT Adaptive: Best score = 1.000
2025-07-16 22:22:43,020 - __main__ - INFO - Running walk-forward validation for BTCUSDT Adaptive
2025-07-16 22:22:43,021 - __main__ - INFO - Total data available: 10.4 days (1000 bars)
2025-07-16 22:22:43,021 - __main__ - INFO - Walk-forward parameters: IS=7d, OOS=2d, step=1d
2025-07-16 22:22:45,000 - __main__ - INFO - BTCUSDT Adaptive: 2 valid WF windows, WF score = 0.244
2025-07-16 22:22:45,001 - __main__ - INFO - Validating and scoring strategies with anti-overfitting criteria
2025-07-16 22:22:45,001 - __main__ - WARNING - BTCUSDT_Trend: Insufficient walk-forward windows (2 < 3)
2025-07-16 22:22:45,001 - __main__ - WARNING - BTCUSDT_Reversion: Insufficient walk-forward windows (2 < 3)
2025-07-16 22:22:45,001 - __main__ - WARNING - BTCUSDT_Adaptive: Insufficient walk-forward windows (2 < 3)
2025-07-16 22:22:45,001 - __main__ - INFO - Saving optimization results
2025-07-16 22:22:45,001 - __main__ - WARNING - No validated strategies to save
2025-07-16 22:22:45,001 - __main__ - WARNING - Optimization completed but no strategies passed validation
2025-07-16 22:22:45,002 - __main__ - INFO - Binance client connection closed
2025-07-16 22:23:13,665 - __main__ - INFO - Ultratrader Orchestrator initialized
2025-07-16 22:23:13,666 - __main__ - INFO - Ultratrader Orchestrator initialized
2025-07-16 22:23:13,666 - __main__ - INFO - Starting Ultratrader optimization pipeline
2025-07-16 22:23:14,454 - __main__ - INFO - Binance client initialized successfully
2025-07-16 22:23:14,454 - __main__ - INFO - Optimizing strategies for symbols: ['BTCUSDT']
2025-07-16 22:23:14,454 - __main__ - INFO - Fetching market data for BTCUSDT
2025-07-16 22:23:14,466 - __main__ - INFO - Retrieved 1000 bars for BTCUSDT (15m)
2025-07-16 22:23:14,466 - __main__ - INFO - Retrieved 1000 bars for BTCUSDT (1h)
2025-07-16 22:23:14,466 - __main__ - INFO - Optimizing Trend strategy for BTCUSDT
2025-07-16 22:23:18,281 - __main__ - INFO - BTCUSDT Trend: Best score = 1.661
2025-07-16 22:23:18,281 - __main__ - INFO - Running walk-forward validation for BTCUSDT Trend
2025-07-16 22:23:18,281 - __main__ - INFO - Total data available: 10.4 days (1000 bars)
2025-07-16 22:23:18,281 - __main__ - INFO - Walk-forward parameters: IS=7d, OOS=2d, step=1d
2025-07-16 22:23:19,748 - __main__ - INFO - BTCUSDT Trend: 2 valid WF windows, WF score = 0.224
2025-07-16 22:23:19,748 - __main__ - INFO - Optimizing Reversion strategy for BTCUSDT
2025-07-16 22:23:21,487 - __main__ - INFO - BTCUSDT Reversion: Best score = 8.205
2025-07-16 22:23:21,487 - __main__ - INFO - Running walk-forward validation for BTCUSDT Reversion
2025-07-16 22:23:21,487 - __main__ - INFO - Total data available: 10.4 days (1000 bars)
2025-07-16 22:23:21,487 - __main__ - INFO - Walk-forward parameters: IS=7d, OOS=2d, step=1d
2025-07-16 22:23:22,649 - __main__ - INFO - BTCUSDT Reversion: 2 valid WF windows, WF score = 0.241
2025-07-16 22:23:22,649 - __main__ - INFO - Optimizing Adaptive strategy for BTCUSDT
2025-07-16 22:23:24,440 - __main__ - INFO - BTCUSDT Adaptive: Best score = 1.000
2025-07-16 22:23:24,440 - __main__ - INFO - Running walk-forward validation for BTCUSDT Adaptive
2025-07-16 22:23:24,440 - __main__ - INFO - Total data available: 10.4 days (1000 bars)
2025-07-16 22:23:24,440 - __main__ - INFO - Walk-forward parameters: IS=7d, OOS=2d, step=1d
2025-07-16 22:23:26,378 - __main__ - INFO - BTCUSDT Adaptive: 2 valid WF windows, WF score = 0.244
2025-07-16 22:23:26,378 - __main__ - INFO - Validating and scoring strategies with anti-overfitting criteria
2025-07-16 22:23:26,379 - __main__ - INFO - BTCUSDT_Trend failed robustness validation. Failed checks: ['min_sortino', 'min_profit_factor', 'sortino_stability', 'oos_positive_return', 'oos_min_sortino']
2025-07-16 22:23:26,379 - __main__ - INFO -   Robustness score: 0.38, Sortino stability: 0.00
2025-07-16 22:23:26,379 - __main__ - INFO - BTCUSDT_Reversion failed robustness validation. Failed checks: ['sortino_stability', 'oos_positive_return', 'oos_min_sortino']
2025-07-16 22:23:26,379 - __main__ - INFO -   Robustness score: 0.62, Sortino stability: -0.37
2025-07-16 22:23:26,379 - __main__ - INFO - BTCUSDT_Adaptive failed robustness validation. Failed checks: ['min_sortino', 'min_profit_factor', 'min_win_rate', 'sortino_stability', 'oos_positive_return', 'oos_min_sortino']
2025-07-16 22:23:26,380 - __main__ - INFO -   Robustness score: 0.25, Sortino stability: 0.00
2025-07-16 22:23:26,380 - __main__ - INFO - Saving optimization results
2025-07-16 22:23:26,380 - __main__ - WARNING - No validated strategies to save
2025-07-16 22:23:26,380 - __main__ - WARNING - Optimization completed but no strategies passed validation
2025-07-16 22:23:26,380 - __main__ - INFO - Binance client connection closed
2025-07-16 22:38:46,470 - __main__ - INFO - Ultratrader Orchestrator initialized
2025-07-16 22:38:46,470 - __main__ - INFO - Ultratrader Orchestrator initialized
2025-07-16 22:38:46,470 - __main__ - INFO - Starting Ultratrader optimization pipeline
2025-07-16 22:38:47,279 - __main__ - INFO - Binance client initialized successfully
2025-07-16 22:38:47,280 - __main__ - INFO - Optimizing strategies for symbols: ['BTCUSDT']
2025-07-16 22:38:47,280 - __main__ - INFO - Fetching market data for BTCUSDT
2025-07-16 22:38:48,879 - __main__ - INFO - Retrieved 1500 bars for BTCUSDT (15m)
2025-07-16 22:38:48,879 - __main__ - INFO - Retrieved 1500 bars for BTCUSDT (1h)
2025-07-16 22:38:48,879 - __main__ - INFO - Optimizing Trend strategy for BTCUSDT
2025-07-16 22:38:53,396 - __main__ - INFO - BTCUSDT Trend: Best score = 3.792
2025-07-16 22:38:53,396 - __main__ - INFO - Running walk-forward validation for BTCUSDT Trend
2025-07-16 22:38:53,396 - __main__ - INFO - Total data available: 15.6 days (1500 bars)
2025-07-16 22:38:53,396 - __main__ - INFO - Walk-forward parameters: IS=7d, OOS=3d, step=1d
2025-07-16 22:38:57,769 - __main__ - INFO - BTCUSDT Trend: 6 valid WF windows, WF score = 0.600
2025-07-16 22:38:57,769 - __main__ - INFO - Optimizing Reversion strategy for BTCUSDT
2025-07-16 22:38:59,781 - __main__ - INFO - BTCUSDT Reversion: Best score = 1.000
2025-07-16 22:38:59,783 - __main__ - INFO - Running walk-forward validation for BTCUSDT Reversion
2025-07-16 22:38:59,783 - __main__ - INFO - Total data available: 15.6 days (1500 bars)
2025-07-16 22:38:59,783 - __main__ - INFO - Walk-forward parameters: IS=7d, OOS=3d, step=1d
2025-07-16 22:39:00,005 - root - WARNING - Trial failed: 
2025-07-16 22:39:00,604 - root - WARNING - Trial failed: 
2025-07-16 22:39:01,292 - root - WARNING - Trial failed: 
2025-07-16 22:39:01,899 - root - WARNING - Trial failed: 
2025-07-16 22:39:02,491 - root - WARNING - Trial failed: 
2025-07-16 22:39:03,430 - __main__ - INFO - BTCUSDT Reversion: 6 valid WF windows, WF score = 0.600
2025-07-16 22:39:03,430 - __main__ - INFO - Optimizing Adaptive strategy for BTCUSDT
2025-07-16 22:39:05,564 - __main__ - INFO - BTCUSDT Adaptive: Best score = 3.605
2025-07-16 22:39:05,564 - __main__ - INFO - Running walk-forward validation for BTCUSDT Adaptive
2025-07-16 22:39:05,564 - __main__ - INFO - Total data available: 15.6 days (1500 bars)
2025-07-16 22:39:05,564 - __main__ - INFO - Walk-forward parameters: IS=7d, OOS=3d, step=1d
2025-07-16 22:39:11,678 - __main__ - INFO - BTCUSDT Adaptive: 6 valid WF windows, WF score = 0.600
2025-07-16 22:39:11,679 - __main__ - INFO - Validating and scoring strategies with anti-overfitting criteria
2025-07-16 22:39:11,679 - __main__ - INFO - BTCUSDT_Trend failed robustness validation. Failed checks: ['sortino_stability', 'oos_positive_return', 'oos_min_sortino']
2025-07-16 22:39:11,679 - __main__ - INFO -   Robustness score: 0.62, Sortino stability: -7.34
2025-07-16 22:39:11,679 - __main__ - INFO - BTCUSDT_Reversion failed robustness validation. Failed checks: ['min_sortino', 'min_profit_factor', 'sortino_stability', 'oos_positive_return', 'oos_min_sortino']
2025-07-16 22:39:11,679 - __main__ - INFO -   Robustness score: 0.38, Sortino stability: 0.00
2025-07-16 22:39:11,679 - __main__ - INFO - BTCUSDT_Adaptive failed robustness validation. Failed checks: ['sortino_stability', 'oos_positive_return', 'oos_min_sortino']
2025-07-16 22:39:11,679 - __main__ - INFO -   Robustness score: 0.62, Sortino stability: -4.99
2025-07-16 22:39:11,679 - __main__ - INFO - Saving optimization results
2025-07-16 22:39:11,680 - __main__ - WARNING - No validated strategies to save
2025-07-16 22:39:11,680 - __main__ - WARNING - Optimization completed but no strategies passed validation
2025-07-16 22:39:11,680 - __main__ - INFO - Binance client connection closed
2025-07-16 22:47:16,972 - __main__ - INFO - Ultratrader Orchestrator initialized
2025-07-16 22:47:16,972 - __main__ - INFO - Ultratrader Orchestrator initialized
2025-07-16 22:47:16,972 - __main__ - INFO - Starting Ultratrader optimization pipeline
2025-07-16 22:47:17,664 - __main__ - INFO - Binance client initialized successfully
2025-07-16 22:47:17,664 - __main__ - INFO - Optimizing strategies for symbols: ['BTCUSDT']
2025-07-16 22:47:17,664 - __main__ - INFO - Fetching market data for BTCUSDT
2025-07-16 22:47:19,293 - __main__ - INFO - Retrieved 1500 bars for BTCUSDT (15m)
2025-07-16 22:47:19,293 - __main__ - INFO - Retrieved 1500 bars for BTCUSDT (1h)
2025-07-16 22:47:19,293 - __main__ - INFO - Optimizing Trend strategy for BTCUSDT
2025-07-16 22:47:26,276 - __main__ - INFO - BTCUSDT Trend: Best score = 6.642
2025-07-16 22:47:26,276 - __main__ - INFO - Running walk-forward validation for BTCUSDT Trend
2025-07-16 22:47:26,277 - __main__ - INFO - Total data available: 15.6 days (1500 bars)
2025-07-16 22:47:26,277 - __main__ - INFO - Walk-forward parameters: IS=7d, OOS=3d, step=1d
2025-07-16 22:47:30,427 - __main__ - INFO - BTCUSDT Trend: 6 valid WF windows, WF score = 0.600
2025-07-16 22:47:30,427 - __main__ - INFO - Optimizing Reversion strategy for BTCUSDT
2025-07-16 22:47:30,940 - root - WARNING - Trial failed: 
2025-07-16 22:47:35,374 - __main__ - INFO - BTCUSDT Reversion: Best score = 6.530
2025-07-16 22:47:35,375 - __main__ - INFO - Running walk-forward validation for BTCUSDT Reversion
2025-07-16 22:47:35,375 - __main__ - INFO - Total data available: 15.6 days (1500 bars)
2025-07-16 22:47:35,375 - __main__ - INFO - Walk-forward parameters: IS=7d, OOS=3d, step=1d
2025-07-16 22:47:36,699 - root - WARNING - Trial failed: 
2025-07-16 22:47:37,250 - root - WARNING - Trial failed: 
2025-07-16 22:47:38,715 - __main__ - INFO - BTCUSDT Reversion: 6 valid WF windows, WF score = 0.600
2025-07-16 22:47:38,715 - __main__ - INFO - Optimizing Adaptive strategy for BTCUSDT
2025-07-16 22:47:43,237 - __main__ - INFO - BTCUSDT Adaptive: Best score = 7.335
2025-07-16 22:47:43,237 - __main__ - INFO - Running walk-forward validation for BTCUSDT Adaptive
2025-07-16 22:47:43,237 - __main__ - INFO - Total data available: 15.6 days (1500 bars)
2025-07-16 22:47:43,237 - __main__ - INFO - Walk-forward parameters: IS=7d, OOS=3d, step=1d
2025-07-16 22:47:49,094 - __main__ - INFO - BTCUSDT Adaptive: 6 valid WF windows, WF score = 0.600
2025-07-16 22:47:49,094 - __main__ - INFO - Validating and scoring strategies with anti-overfitting criteria
2025-07-16 22:47:49,094 - __main__ - INFO - BTCUSDT_Trend failed robustness validation. Failed checks: ['sortino_stability', 'oos_positive_return', 'oos_min_sortino']
2025-07-16 22:47:49,094 - __main__ - INFO -   Robustness score: 0.62, Sortino stability: -1.09
2025-07-16 22:47:49,094 - __main__ - INFO - BTCUSDT_Reversion failed robustness validation. Failed checks: ['sortino_stability', 'oos_positive_return', 'oos_min_sortino']
2025-07-16 22:47:49,094 - __main__ - INFO -   Robustness score: 0.62, Sortino stability: -0.06
2025-07-16 22:47:49,094 - __main__ - INFO - BTCUSDT_Adaptive failed robustness validation. Failed checks: ['sortino_stability', 'oos_positive_return', 'oos_min_sortino']
2025-07-16 22:47:49,094 - __main__ - INFO -   Robustness score: 0.62, Sortino stability: -0.29
2025-07-16 22:47:49,095 - __main__ - INFO - Saving optimization results
2025-07-16 22:47:49,095 - __main__ - WARNING - No validated strategies to save
2025-07-16 22:47:49,095 - __main__ - WARNING - Optimization completed but no strategies passed validation
2025-07-16 22:47:49,095 - __main__ - INFO - Binance client connection closed
2025-07-16 22:48:25,435 - __main__ - INFO - Ultratrader Orchestrator initialized
2025-07-16 22:48:25,435 - __main__ - INFO - Ultratrader Orchestrator initialized
2025-07-16 22:48:25,435 - __main__ - INFO - Starting Ultratrader optimization pipeline
2025-07-16 22:48:26,121 - __main__ - INFO - Binance client initialized successfully
2025-07-16 22:48:26,121 - __main__ - INFO - Optimizing strategies for symbols: ['BTCUSDT']
2025-07-16 22:48:26,121 - __main__ - INFO - Fetching market data for BTCUSDT
2025-07-16 22:48:26,152 - __main__ - INFO - Retrieved 1500 bars for BTCUSDT (15m)
2025-07-16 22:48:26,152 - __main__ - INFO - Retrieved 1500 bars for BTCUSDT (1h)
2025-07-16 22:48:26,153 - __main__ - INFO - Optimizing Trend strategy for BTCUSDT
2025-07-16 22:48:32,497 - __main__ - INFO - BTCUSDT Trend: Best score = 3.757
2025-07-16 22:48:32,498 - __main__ - INFO - Running walk-forward validation for BTCUSDT Trend
2025-07-16 22:48:32,498 - __main__ - INFO - Total data available: 15.6 days (1500 bars)
2025-07-16 22:48:32,498 - __main__ - INFO - Walk-forward parameters: IS=7d, OOS=3d, step=1d
2025-07-16 22:48:36,471 - __main__ - INFO - BTCUSDT Trend: 6 valid WF windows, WF score = 0.600
2025-07-16 22:48:36,471 - __main__ - INFO - Optimizing Reversion strategy for BTCUSDT
2025-07-16 22:48:36,989 - root - WARNING - Trial failed: 
2025-07-16 22:48:41,665 - __main__ - INFO - BTCUSDT Reversion: Best score = 7.296
2025-07-16 22:48:41,666 - __main__ - INFO - Running walk-forward validation for BTCUSDT Reversion
2025-07-16 22:48:41,666 - __main__ - INFO - Total data available: 15.6 days (1500 bars)
2025-07-16 22:48:41,666 - __main__ - INFO - Walk-forward parameters: IS=7d, OOS=3d, step=1d
2025-07-16 22:48:43,126 - root - WARNING - Trial failed: 
2025-07-16 22:48:43,715 - root - WARNING - Trial failed: 
2025-07-16 22:48:45,274 - __main__ - INFO - BTCUSDT Reversion: 6 valid WF windows, WF score = 0.600
2025-07-16 22:48:45,274 - __main__ - INFO - Optimizing Adaptive strategy for BTCUSDT
2025-07-16 22:48:45,766 - root - WARNING - Trial failed: 
2025-07-16 22:48:45,877 - root - WARNING - Trial failed: 
2025-07-16 22:48:46,295 - root - WARNING - Trial failed: 
2025-07-16 22:48:47,180 - root - WARNING - Trial failed: 
2025-07-16 22:48:47,966 - root - WARNING - Trial failed: 
2025-07-16 22:48:50,118 - __main__ - INFO - BTCUSDT Adaptive: Best score = 7.002
2025-07-16 22:48:50,119 - __main__ - INFO - Running walk-forward validation for BTCUSDT Adaptive
2025-07-16 22:48:50,119 - __main__ - INFO - Total data available: 15.6 days (1500 bars)
2025-07-16 22:48:50,119 - __main__ - INFO - Walk-forward parameters: IS=7d, OOS=3d, step=1d
2025-07-16 22:48:56,263 - __main__ - INFO - BTCUSDT Adaptive: 6 valid WF windows, WF score = 0.600
2025-07-16 22:48:56,263 - __main__ - INFO - Validating and scoring strategies with anti-overfitting criteria
2025-07-16 22:48:56,264 - __main__ - INFO - BTCUSDT_Trend failed robustness validation. Failed checks: ['oos_positive_return', 'oos_min_sortino']
2025-07-16 22:48:56,264 - __main__ - INFO -   Robustness score: 0.75, Sortino stability: -3.33
2025-07-16 22:48:56,264 - __main__ - INFO - BTCUSDT_Reversion failed robustness validation. Failed checks: ['oos_positive_return']
2025-07-16 22:48:56,264 - __main__ - INFO -   Robustness score: 0.88, Sortino stability: -0.07
2025-07-16 22:48:56,264 - __main__ - INFO - BTCUSDT_Adaptive failed robustness validation. Failed checks: ['oos_positive_return', 'oos_min_sortino']
2025-07-16 22:48:56,264 - __main__ - INFO -   Robustness score: 0.75, Sortino stability: -0.42
2025-07-16 22:48:56,265 - __main__ - INFO - Saving optimization results
2025-07-16 22:48:56,265 - __main__ - WARNING - No validated strategies to save
2025-07-16 22:48:56,265 - __main__ - WARNING - Optimization completed but no strategies passed validation
2025-07-16 22:48:56,265 - __main__ - INFO - Binance client connection closed
2025-07-16 23:16:59,057 - __main__ - INFO - Ultratrader Orchestrator initialized
2025-07-16 23:16:59,057 - __main__ - INFO - Ultratrader Orchestrator initialized
2025-07-16 23:16:59,057 - __main__ - INFO - Starting Ultratrader optimization pipeline
2025-07-16 23:16:59,748 - __main__ - INFO - Binance client initialized successfully
2025-07-16 23:16:59,749 - __main__ - INFO - Optimizing strategies for symbols: ['BTCUSDT']
2025-07-16 23:16:59,749 - __main__ - INFO - Fetching market data for BTCUSDT
2025-07-16 23:16:59,781 - __main__ - INFO - Retrieved 1500 bars for BTCUSDT (15m)
2025-07-16 23:16:59,781 - __main__ - INFO - Retrieved 1500 bars for BTCUSDT (1h)
2025-07-16 23:16:59,781 - __main__ - INFO - Optimizing Trend strategy for BTCUSDT
2025-07-16 23:17:05,898 - __main__ - INFO - BTCUSDT Trend: Best score = 1.000
2025-07-16 23:17:05,898 - __main__ - INFO - Running walk-forward validation for BTCUSDT Trend
2025-07-16 23:17:05,898 - __main__ - INFO - Total data available: 15.6 days (1500 bars)
2025-07-16 23:17:05,898 - __main__ - INFO - Walk-forward parameters: IS=7d, OOS=3d, step=1d
2025-07-16 23:17:08,327 - __main__ - INFO - BTCUSDT Trend: 6 valid WF windows, WF score = 0.600
2025-07-16 23:17:08,327 - __main__ - INFO - Optimizing Reversion strategy for BTCUSDT
2025-07-16 23:17:08,337 - root - WARNING - Trial failed: 
2025-07-16 23:17:08,345 - root - WARNING - Trial failed: 
2025-07-16 23:17:08,354 - root - WARNING - Trial failed: 
2025-07-16 23:17:08,363 - root - WARNING - Trial failed: 
2025-07-16 23:17:08,371 - root - WARNING - Trial failed: 
2025-07-16 23:17:08,379 - root - WARNING - Trial failed: 
2025-07-16 23:17:08,386 - root - WARNING - Trial failed: 
2025-07-16 23:17:08,394 - root - WARNING - Trial failed: 
2025-07-16 23:17:08,402 - root - WARNING - Trial failed: 
2025-07-16 23:17:08,412 - root - WARNING - Trial failed: 
2025-07-16 23:17:08,419 - root - WARNING - Trial failed: 
2025-07-16 23:17:08,426 - root - WARNING - Trial failed: 
2025-07-16 23:17:08,435 - root - WARNING - Trial failed: 
2025-07-16 23:17:08,444 - root - WARNING - Trial failed: 
2025-07-16 23:17:08,451 - root - WARNING - Trial failed: 
2025-07-16 23:17:08,460 - root - WARNING - Trial failed: 
2025-07-16 23:17:08,467 - root - WARNING - Trial failed: 
2025-07-16 23:17:08,477 - root - WARNING - Trial failed: 
2025-07-16 23:17:08,549 - root - WARNING - Trial failed: 
2025-07-16 23:17:08,620 - root - WARNING - Trial failed: 
2025-07-16 23:17:08,682 - root - WARNING - Trial failed: 
2025-07-16 23:17:08,747 - root - WARNING - Trial failed: 
2025-07-16 23:17:08,820 - root - WARNING - Trial failed: 
2025-07-16 23:17:08,888 - root - WARNING - Trial failed: 
2025-07-16 23:17:08,952 - root - WARNING - Trial failed: 
2025-07-16 23:17:09,015 - root - WARNING - Trial failed: 
2025-07-16 23:17:09,087 - root - WARNING - Trial failed: 
2025-07-16 23:17:09,151 - root - WARNING - Trial failed: 
2025-07-16 23:17:09,217 - root - WARNING - Trial failed: 
2025-07-16 23:17:09,296 - root - WARNING - Trial failed: 
2025-07-16 23:17:09,366 - root - WARNING - Trial failed: 
2025-07-16 23:17:09,462 - root - WARNING - Trial failed: 
2025-07-16 23:17:09,561 - root - WARNING - Trial failed: 
2025-07-16 23:17:09,629 - root - WARNING - Trial failed: 
2025-07-16 23:17:09,701 - root - WARNING - Trial failed: 
2025-07-16 23:17:09,780 - root - WARNING - Trial failed: 
2025-07-16 23:17:09,863 - root - WARNING - Trial failed: 
2025-07-16 23:17:09,936 - root - WARNING - Trial failed: 
2025-07-16 23:17:10,008 - root - WARNING - Trial failed: 
2025-07-16 23:17:10,081 - root - WARNING - Trial failed: 
2025-07-16 23:17:10,160 - root - WARNING - Trial failed: 
2025-07-16 23:17:10,230 - root - WARNING - Trial failed: 
2025-07-16 23:17:10,304 - root - WARNING - Trial failed: 
2025-07-16 23:17:10,398 - root - WARNING - Trial failed: 
2025-07-16 23:17:10,476 - root - WARNING - Trial failed: 
2025-07-16 23:17:10,547 - root - WARNING - Trial failed: 
2025-07-16 23:17:10,628 - root - WARNING - Trial failed: 
2025-07-16 23:17:10,706 - root - WARNING - Trial failed: 
2025-07-16 23:17:10,777 - root - WARNING - Trial failed: 
2025-07-16 23:17:10,847 - root - WARNING - Trial failed: 
2025-07-16 23:17:10,926 - root - WARNING - Trial failed: 
2025-07-16 23:17:11,001 - root - WARNING - Trial failed: 
2025-07-16 23:17:11,075 - root - WARNING - Trial failed: 
2025-07-16 23:17:11,158 - root - WARNING - Trial failed: 
2025-07-16 23:17:11,246 - root - WARNING - Trial failed: 
2025-07-16 23:17:11,325 - root - WARNING - Trial failed: 
2025-07-16 23:17:11,410 - root - WARNING - Trial failed: 
2025-07-16 23:17:11,497 - root - WARNING - Trial failed: 
2025-07-16 23:17:11,578 - root - WARNING - Trial failed: 
2025-07-16 23:17:11,658 - root - WARNING - Trial failed: 
2025-07-16 23:17:11,748 - root - WARNING - Trial failed: 
2025-07-16 23:17:11,826 - root - WARNING - Trial failed: 
2025-07-16 23:17:11,901 - root - WARNING - Trial failed: 
2025-07-16 23:17:11,991 - root - WARNING - Trial failed: 
2025-07-16 23:17:12,069 - root - WARNING - Trial failed: 
2025-07-16 23:17:12,146 - root - WARNING - Trial failed: 
2025-07-16 23:17:12,232 - root - WARNING - Trial failed: 
2025-07-16 23:17:12,316 - root - WARNING - Trial failed: 
2025-07-16 23:17:12,393 - root - WARNING - Trial failed: 
2025-07-16 23:17:12,476 - root - WARNING - Trial failed: 
2025-07-16 23:17:12,566 - root - WARNING - Trial failed: 
2025-07-16 23:17:12,643 - root - WARNING - Trial failed: 
2025-07-16 23:17:12,720 - root - WARNING - Trial failed: 
2025-07-16 23:17:12,806 - root - WARNING - Trial failed: 
2025-07-16 23:17:12,885 - root - WARNING - Trial failed: 
2025-07-16 23:17:12,914 - __main__ - INFO - BTCUSDT Reversion: Best score = -999.000
2025-07-16 23:17:12,915 - __main__ - INFO - Running walk-forward validation for BTCUSDT Reversion
2025-07-16 23:17:12,915 - __main__ - INFO - Total data available: 15.6 days (1500 bars)
2025-07-16 23:17:12,915 - __main__ - INFO - Walk-forward parameters: IS=7d, OOS=3d, step=1d
2025-07-16 23:17:12,927 - root - WARNING - Trial failed: 
2025-07-16 23:17:12,939 - root - WARNING - Trial failed: 
2025-07-16 23:17:12,998 - root - WARNING - Trial failed: 
2025-07-16 23:17:13,066 - root - WARNING - Trial failed: 
2025-07-16 23:17:13,127 - root - WARNING - Trial failed: 
2025-07-16 23:17:13,185 - root - WARNING - Trial failed: 
2025-07-16 23:17:13,248 - root - WARNING - Trial failed: 
2025-07-16 23:17:13,324 - root - WARNING - Trial failed: 
2025-07-16 23:17:13,357 - root - WARNING - Trial failed: 
2025-07-16 23:17:13,367 - root - WARNING - Trial failed: 
2025-07-16 23:17:13,423 - root - WARNING - Trial failed: 
2025-07-16 23:17:13,478 - root - WARNING - Trial failed: 
2025-07-16 23:17:13,543 - root - WARNING - Trial failed: 
2025-07-16 23:17:13,601 - root - WARNING - Trial failed: 
2025-07-16 23:17:13,665 - root - WARNING - Trial failed: 
2025-07-16 23:17:13,726 - root - WARNING - Trial failed: 
2025-07-16 23:17:13,760 - root - WARNING - Trial failed: 
2025-07-16 23:17:13,771 - root - WARNING - Trial failed: 
2025-07-16 23:17:13,841 - root - WARNING - Trial failed: 
2025-07-16 23:17:13,900 - root - WARNING - Trial failed: 
2025-07-16 23:17:13,958 - root - WARNING - Trial failed: 
2025-07-16 23:17:14,015 - root - WARNING - Trial failed: 
2025-07-16 23:17:14,093 - root - WARNING - Trial failed: 
2025-07-16 23:17:14,157 - root - WARNING - Trial failed: 
2025-07-16 23:17:14,191 - root - WARNING - Trial failed: 
2025-07-16 23:17:14,202 - root - WARNING - Trial failed: 
2025-07-16 23:17:14,260 - root - WARNING - Trial failed: 
2025-07-16 23:17:14,327 - root - WARNING - Trial failed: 
2025-07-16 23:17:14,391 - root - WARNING - Trial failed: 
2025-07-16 23:17:14,451 - root - WARNING - Trial failed: 
2025-07-16 23:17:14,517 - root - WARNING - Trial failed: 
2025-07-16 23:17:14,637 - root - WARNING - Trial failed: 
2025-07-16 23:17:14,677 - root - WARNING - Trial failed: 
2025-07-16 23:17:14,689 - root - WARNING - Trial failed: 
2025-07-16 23:17:14,762 - root - WARNING - Trial failed: 
2025-07-16 23:17:14,845 - root - WARNING - Trial failed: 
2025-07-16 23:17:14,934 - root - WARNING - Trial failed: 
2025-07-16 23:17:15,011 - root - WARNING - Trial failed: 
2025-07-16 23:17:15,073 - root - WARNING - Trial failed: 
2025-07-16 23:17:15,146 - root - WARNING - Trial failed: 
2025-07-16 23:17:15,185 - root - WARNING - Trial failed: 
2025-07-16 23:17:15,200 - root - WARNING - Trial failed: 
2025-07-16 23:17:15,266 - root - WARNING - Trial failed: 
2025-07-16 23:17:15,328 - root - WARNING - Trial failed: 
2025-07-16 23:17:15,412 - root - WARNING - Trial failed: 
2025-07-16 23:17:15,503 - root - WARNING - Trial failed: 
2025-07-16 23:17:15,620 - root - WARNING - Trial failed: 
2025-07-16 23:17:15,704 - root - WARNING - Trial failed: 
2025-07-16 23:17:15,728 - __main__ - INFO - BTCUSDT Reversion: 6 valid WF windows, WF score = 0.200
2025-07-16 23:17:15,728 - __main__ - INFO - Optimizing Adaptive strategy for BTCUSDT
2025-07-16 23:17:15,761 - root - WARNING - Trial failed: 
2025-07-16 23:17:15,772 - root - WARNING - Trial failed: 
2025-07-16 23:17:15,782 - root - WARNING - Trial failed: 
2025-07-16 23:17:15,804 - root - WARNING - Trial failed: 
2025-07-16 23:17:15,814 - root - WARNING - Trial failed: 
2025-07-16 23:17:15,826 - root - WARNING - Trial failed: 
2025-07-16 23:17:15,835 - root - WARNING - Trial failed: 
2025-07-16 23:17:16,156 - root - WARNING - Trial failed: 
2025-07-16 23:17:16,395 - root - WARNING - Trial failed: 
2025-07-16 23:17:16,848 - root - WARNING - Trial failed: 
2025-07-16 23:17:17,745 - root - WARNING - Trial failed: 
2025-07-16 23:17:17,846 - root - WARNING - Trial failed: 
2025-07-16 23:17:18,235 - root - WARNING - Trial failed: 
2025-07-16 23:17:18,876 - root - WARNING - Trial failed: 
2025-07-16 23:17:19,539 - root - WARNING - Trial failed: 
2025-07-16 23:17:19,931 - __main__ - INFO - BTCUSDT Adaptive: Best score = 1.000
2025-07-16 23:17:19,931 - __main__ - INFO - Running walk-forward validation for BTCUSDT Adaptive
2025-07-16 23:17:19,931 - __main__ - INFO - Total data available: 15.6 days (1500 bars)
2025-07-16 23:17:19,931 - __main__ - INFO - Walk-forward parameters: IS=7d, OOS=3d, step=1d
2025-07-16 23:17:20,145 - root - WARNING - Trial failed: 
2025-07-16 23:17:20,274 - root - WARNING - Trial failed: 
2025-07-16 23:17:20,560 - root - WARNING - Trial failed: 
2025-07-16 23:17:20,786 - root - WARNING - Trial failed: 
2025-07-16 23:17:20,867 - root - WARNING - Trial failed: 
2025-07-16 23:17:21,131 - root - WARNING - Trial failed: 
2025-07-16 23:17:21,368 - root - WARNING - Trial failed: 
2025-07-16 23:17:21,463 - root - WARNING - Trial failed: 
2025-07-16 23:17:21,726 - root - WARNING - Trial failed: 
2025-07-16 23:17:21,958 - root - WARNING - Trial failed: 
2025-07-16 23:17:22,046 - root - WARNING - Trial failed: 
2025-07-16 23:17:22,057 - __main__ - INFO - Ultratrader Orchestrator initialized
2025-07-16 23:17:22,057 - __main__ - INFO - Ultratrader Orchestrator initialized
2025-07-16 23:17:22,057 - __main__ - INFO - Starting Ultratrader optimization pipeline
2025-07-16 23:17:22,306 - root - WARNING - Trial failed: 
2025-07-16 23:17:22,526 - root - WARNING - Trial failed: 
2025-07-16 23:17:22,601 - root - WARNING - Trial failed: 
2025-07-16 23:17:22,739 - __main__ - INFO - Binance client initialized successfully
2025-07-16 23:17:22,739 - __main__ - INFO - Optimizing strategies for symbols: ['BTCUSDT']
2025-07-16 23:17:22,739 - __main__ - INFO - Fetching market data for BTCUSDT
2025-07-16 23:17:22,752 - __main__ - INFO - Retrieved 1500 bars for BTCUSDT (15m)
2025-07-16 23:17:22,752 - __main__ - INFO - Retrieved 1500 bars for BTCUSDT (1h)
2025-07-16 23:17:22,753 - __main__ - INFO - Optimizing Trend strategy for BTCUSDT
2025-07-16 23:17:22,905 - root - WARNING - Trial failed: 
2025-07-16 23:17:23,137 - root - WARNING - Trial failed: 
2025-07-16 23:17:23,221 - root - WARNING - Trial failed: 
2025-07-16 23:17:23,482 - root - WARNING - Trial failed: 
2025-07-16 23:17:23,513 - __main__ - INFO - BTCUSDT Adaptive: 6 valid WF windows, WF score = 0.600
2025-07-16 23:17:23,513 - __main__ - INFO - Validating and scoring strategies with anti-overfitting criteria
2025-07-16 23:17:23,513 - __main__ - INFO - BTCUSDT_Trend failed robustness validation. Failed checks: ['min_sortino', 'min_profit_factor', 'oos_positive_return', 'oos_min_sortino']
2025-07-16 23:17:23,513 - __main__ - INFO -   Robustness score: 0.50, Sortino stability: 0.00
2025-07-16 23:17:23,513 - __main__ - INFO - BTCUSDT_Reversion failed robustness validation. Failed checks: ['min_trades', 'min_win_rate', 'oos_positive_return']
2025-07-16 23:17:23,513 - __main__ - INFO -   Robustness score: 0.62, Sortino stability: 0.00
2025-07-16 23:17:23,514 - __main__ - INFO - BTCUSDT_Adaptive failed robustness validation. Failed checks: ['min_sortino', 'min_profit_factor', 'oos_positive_return', 'oos_min_sortino']
2025-07-16 23:17:23,514 - __main__ - INFO -   Robustness score: 0.50, Sortino stability: 0.00
2025-07-16 23:17:23,514 - __main__ - INFO - Saving optimization results
2025-07-16 23:17:23,514 - __main__ - WARNING - No validated strategies to save
2025-07-16 23:17:23,514 - __main__ - WARNING - Optimization completed but no strategies passed validation
2025-07-16 23:17:23,514 - __main__ - INFO - Binance client connection closed
2025-07-16 23:17:29,886 - __main__ - INFO - BTCUSDT Trend: Best score = 3.757
2025-07-16 23:17:29,887 - __main__ - INFO - Running walk-forward validation for BTCUSDT Trend
2025-07-16 23:17:29,887 - __main__ - INFO - Total data available: 15.6 days (1500 bars)
2025-07-16 23:17:29,887 - __main__ - INFO - Walk-forward parameters: IS=7d, OOS=3d, step=1d
2025-07-16 23:17:34,850 - __main__ - INFO - BTCUSDT Trend: 6 valid WF windows, WF score = 0.600
2025-07-16 23:17:34,850 - __main__ - INFO - Optimizing Reversion strategy for BTCUSDT
2025-07-16 23:17:35,377 - root - WARNING - Trial failed: 
2025-07-16 23:17:40,106 - __main__ - INFO - BTCUSDT Reversion: Best score = 7.296
2025-07-16 23:17:40,106 - __main__ - INFO - Running walk-forward validation for BTCUSDT Reversion
2025-07-16 23:17:40,106 - __main__ - INFO - Total data available: 15.6 days (1500 bars)
2025-07-16 23:17:40,106 - __main__ - INFO - Walk-forward parameters: IS=7d, OOS=3d, step=1d
2025-07-16 23:17:41,495 - root - WARNING - Trial failed: 
2025-07-16 23:17:42,066 - root - WARNING - Trial failed: 
2025-07-16 23:17:43,592 - __main__ - INFO - BTCUSDT Reversion: 6 valid WF windows, WF score = 0.600
2025-07-16 23:17:43,592 - __main__ - INFO - Optimizing Adaptive strategy for BTCUSDT
2025-07-16 23:17:44,059 - root - WARNING - Trial failed: 
2025-07-16 23:17:44,157 - root - WARNING - Trial failed: 
2025-07-16 23:17:44,570 - root - WARNING - Trial failed: 
2025-07-16 23:17:45,445 - root - WARNING - Trial failed: 
2025-07-16 23:17:46,243 - root - WARNING - Trial failed: 
2025-07-16 23:17:48,322 - __main__ - INFO - BTCUSDT Adaptive: Best score = 7.002
2025-07-16 23:17:48,322 - __main__ - INFO - Running walk-forward validation for BTCUSDT Adaptive
2025-07-16 23:17:48,322 - __main__ - INFO - Total data available: 15.6 days (1500 bars)
2025-07-16 23:17:48,322 - __main__ - INFO - Walk-forward parameters: IS=7d, OOS=3d, step=1d
2025-07-16 23:17:54,548 - __main__ - INFO - BTCUSDT Adaptive: 6 valid WF windows, WF score = 0.600
2025-07-16 23:17:54,548 - __main__ - INFO - Validating and scoring strategies with anti-overfitting criteria
2025-07-16 23:17:54,549 - __main__ - INFO - BTCUSDT_Trend failed robustness validation. Failed checks: ['oos_positive_return', 'oos_min_sortino']
2025-07-16 23:17:54,549 - __main__ - INFO -   Robustness score: 0.75, Sortino stability: -3.33
2025-07-16 23:17:54,549 - __main__ - INFO - BTCUSDT_Reversion failed robustness validation. Failed checks: ['oos_positive_return']
2025-07-16 23:17:54,549 - __main__ - INFO -   Robustness score: 0.88, Sortino stability: -0.07
2025-07-16 23:17:54,549 - __main__ - INFO - BTCUSDT_Adaptive failed robustness validation. Failed checks: ['oos_positive_return', 'oos_min_sortino']
2025-07-16 23:17:54,549 - __main__ - INFO -   Robustness score: 0.75, Sortino stability: -0.42
2025-07-16 23:17:54,549 - __main__ - INFO - Saving optimization results
2025-07-16 23:17:54,549 - __main__ - WARNING - No validated strategies to save
2025-07-16 23:17:54,549 - __main__ - WARNING - Optimization completed but no strategies passed validation
2025-07-16 23:17:54,549 - __main__ - INFO - Binance client connection closed
2025-07-16 23:20:23,915 - __main__ - INFO - Ultratrader Orchestrator initialized
2025-07-16 23:20:23,917 - __main__ - INFO - Ultratrader Orchestrator initialized
2025-07-16 23:20:23,918 - __main__ - INFO - Starting Ultratrader optimization pipeline
2025-07-16 23:20:24,714 - __main__ - INFO - Binance client initialized successfully
2025-07-16 23:20:24,714 - __main__ - INFO - Optimizing strategies for symbols: ['BTCUSDT']
2025-07-16 23:20:24,714 - __main__ - INFO - Fetching market data for BTCUSDT
2025-07-16 23:20:24,745 - __main__ - INFO - Retrieved 1500 bars for BTCUSDT (15m)
2025-07-16 23:20:24,745 - __main__ - INFO - Retrieved 1500 bars for BTCUSDT (1h)
2025-07-16 23:20:24,745 - __main__ - INFO - Optimizing Trend strategy for BTCUSDT
2025-07-16 23:20:24,749 - root - WARNING - Trial 0 failed - ValueError: Invalid parameters sampled: {'ema_fast': 14, 'ema_slow': 97, 'atr_len': 21, 'vol_sma_len': 27, 'sl_atr_mult': 2.312037280884873, 'trail_atr_mult': 1.2991824650758486, 'risk_multiplier': 1.6452090304204987, 'min_bars_between_trades': 2, 'use_volume_filter': False, 'st_len': 5, 'st_mult': 4.89468448256698, 'adx_len': 22, 'adx_threshold': 20.308477766956905, 'trend_confirmations': 1, 'macd_fast': 8, 'macd_slow': 28, 'macd_signal': 10, 'rsi_len': 15, 'tp_atr_mult': 8.621062261782377}
2025-07-16 23:20:24,751 - root - WARNING - Trial 1 failed - ValueError: Invalid parameters sampled: {'ema_fast': 20, 'ema_slow': 31, 'atr_len': 13, 'vol_sma_len': 20, 'sl_atr_mult': 2.912139968434072, 'trail_atr_mult': 3.3125630764576437, 'risk_multiplier': 1.9991844553958993, 'min_bars_between_trades': 2, 'use_volume_filter': True, 'st_len': 26, 'st_mult': 2.0968344329055206, 'adx_len': 9, 'adx_threshold': 38.722138431333335, 'trend_confirmations': 2, 'macd_fast': 18, 'macd_slow': 28, 'macd_signal': 6, 'rsi_len': 20, 'tp_atr_mult': 9.961372443656412}
2025-07-16 23:20:24,753 - root - WARNING - Trial 2 failed - ValueError: Invalid parameters sampled: {'ema_fast': 8, 'ema_slow': 60, 'atr_len': 8, 'vol_sma_len': 38, 'sl_atr_mult': 2.517559963200034, 'trail_atr_mult': 2.9200713099327427, 'risk_multiplier': 2.2792776902235277, 'min_bars_between_trades': 2, 'use_volume_filter': True, 'st_len': 39, 'st_mult': 4.2129648817639005, 'adx_len': 24, 'adx_threshold': 37.37068376069122, 'trend_confirmations': 2, 'macd_fast': 19, 'macd_slow': 20, 'macd_signal': 7, 'rsi_len': 8, 'tp_atr_mult': 8.927972976869379}
2025-07-16 23:20:24,756 - root - WARNING - Trial 3 failed - ValueError: Invalid parameters sampled: {'ema_fast': 15, 'ema_slow': 41, 'atr_len': 22, 'vol_sma_len': 19, 'sl_atr_mult': 2.5618690193747615, 'trail_atr_mult': 2.5366274661063954, 'risk_multiplier': 1.8523105624369065, 'min_bars_between_trades': 2, 'use_volume_filter': False, 'st_len': 32, 'st_mult': 2.1955048853696035, 'adx_len': 8, 'adx_threshold': 35.386535711370854, 'trend_confirmations': 2, 'macd_fast': 16, 'macd_slow': 43, 'macd_signal': 5, 'rsi_len': 14, 'tp_atr_mult': 7.042821535726167}
2025-07-16 23:20:24,758 - root - WARNING - Trial 4 failed - ValueError: Invalid parameters sampled: {'ema_fast': 27, 'ema_slow': 70, 'atr_len': 13, 'vol_sma_len': 10, 'sl_atr_mult': 2.6219646434313244, 'trail_atr_mult': 1.8405866304855907, 'risk_multiplier': 3.32401544584516, 'min_bars_between_trades': 2, 'use_volume_filter': True, 'st_len': 9, 'st_mult': 3.9963567552804826, 'adx_len': 21, 'adx_threshold': 29.031929939237408, 'trend_confirmations': 2, 'macd_fast': 13, 'macd_slow': 35, 'macd_signal': 9, 'rsi_len': 8, 'tp_atr_mult': 6.97102284293974}
2025-07-16 23:20:24,760 - root - WARNING - Trial 5 failed - ValueError: Invalid parameters sampled: {'ema_fast': 5, 'ema_slow': 71, 'atr_len': 13, 'vol_sma_len': 24, 'sl_atr_mult': 3.8151329478521863, 'trail_atr_mult': 1.5977351332763998, 'risk_multiplier': 2.525957307589074, 'min_bars_between_trades': 2, 'use_volume_filter': True, 'st_len': 15, 'st_mult': 2.0642745053890152, 'adx_len': 24, 'adx_threshold': 35.20300948911043, 'trend_confirmations': 2, 'macd_fast': 19, 'macd_slow': 44, 'macd_signal': 7, 'rsi_len': 24, 'tp_atr_mult': 10.854080177240856}
2025-07-16 23:20:24,761 - root - WARNING - Trial 6 failed - ValueError: Invalid parameters sampled: {'ema_fast': 25, 'ema_slow': 92, 'atr_len': 13, 'vol_sma_len': 11, 'sl_atr_mult': 2.4558703250838834, 'trail_atr_mult': 2.1667449236040204, 'risk_multiplier': 3.5450369148062326, 'min_bars_between_trades': 2, 'use_volume_filter': False, 'st_len': 20, 'st_mult': 2.2773773366475556, 'adx_len': 10, 'adx_threshold': 23.4403792850907, 'trend_confirmations': 2, 'macd_fast': 10, 'macd_slow': 35, 'macd_signal': 12, 'rsi_len': 14, 'tp_atr_mult': 14.746038744488645}
2025-07-16 23:20:24,763 - root - WARNING - Trial 7 failed - ValueError: Invalid parameters sampled: {'ema_fast': 30, 'ema_slow': 40, 'atr_len': 16, 'vol_sma_len': 17, 'sl_atr_mult': 2.5696809887549352, 'trail_atr_mult': 0.918038231534505, 'risk_multiplier': 3.023910834949742, 'min_bars_between_trades': 2, 'use_volume_filter': False, 'st_len': 37, 'st_mult': 2.3384666173344035, 'adx_len': 10, 'adx_threshold': 27.236319006939077, 'trend_confirmations': 2, 'macd_fast': 9, 'macd_slow': 40, 'macd_signal': 13, 'rsi_len': 12, 'tp_atr_mult': 12.553947137506736}
2025-07-16 23:20:24,764 - root - WARNING - Trial 8 failed - ValueError: Invalid parameters sampled: {'ema_fast': 14, 'ema_slow': 71, 'atr_len': 19, 'vol_sma_len': 25, 'sl_atr_mult': 2.1805795401088166, 'trail_atr_mult': 3.4729679858855613, 'risk_multiplier': 2.3019501624293395, 'min_bars_between_trades': 1, 'use_volume_filter': False, 'st_len': 29, 'st_mult': 1.5580574012474966, 'adx_len': 17, 'adx_threshold': 20.66239437994845, 'trend_confirmations': 2, 'macd_fast': 8, 'macd_slow': 40, 'macd_signal': 9, 'rsi_len': 24, 'tp_atr_mult': 7.23768849731394}
2025-07-16 23:20:24,766 - root - WARNING - Trial 9 failed - ValueError: Invalid parameters sampled: {'ema_fast': 13, 'ema_slow': 29, 'atr_len': 24, 'vol_sma_len': 36, 'sl_atr_mult': 2.515883255430311, 'trail_atr_mult': 2.911948947309373, 'risk_multiplier': 3.5430555005030397, 'min_bars_between_trades': 2, 'use_volume_filter': True, 'st_len': 8, 'st_mult': 4.640255152836644, 'adx_len': 24, 'adx_threshold': 30.827536431831696, 'trend_confirmations': 1, 'macd_fast': 11, 'macd_slow': 41, 'macd_signal': 14, 'rsi_len': 23, 'tp_atr_mult': 13.018879912718614}
2025-07-16 23:20:24,768 - root - WARNING - Trial 10 failed - ValueError: Invalid parameters sampled: {'ema_fast': 21, 'ema_slow': 26, 'atr_len': 10, 'vol_sma_len': 37, 'sl_atr_mult': 3.21285811931918, 'trail_atr_mult': 0.8294305651732149, 'risk_multiplier': 1.7536788571650803, 'min_bars_between_trades': 2, 'use_volume_filter': False, 'st_len': 24, 'st_mult': 3.9216331919244265, 'adx_len': 19, 'adx_threshold': 20.606732736513994, 'trend_confirmations': 2, 'macd_fast': 9, 'macd_slow': 28, 'macd_signal': 13, 'rsi_len': 19, 'tp_atr_mult': 13.6430106944476}
2025-07-16 23:20:24,770 - root - WARNING - Trial 11 failed - ValueError: Invalid parameters sampled: {'ema_fast': 22, 'ema_slow': 66, 'atr_len': 9, 'vol_sma_len': 20, 'sl_atr_mult': 2.530404735363451, 'trail_atr_mult': 1.5807668588130674, 'risk_multiplier': 3.932526386881114, 'min_bars_between_trades': 1, 'use_volume_filter': True, 'st_len': 33, 'st_mult': 3.2592298258681724, 'adx_len': 18, 'adx_threshold': 27.312942345471598, 'trend_confirmations': 1, 'macd_fast': 16, 'macd_slow': 27, 'macd_signal': 5, 'rsi_len': 19, 'tp_atr_mult': 7.59399611466344}
2025-07-16 23:20:24,772 - root - WARNING - Trial 12 failed - ValueError: Invalid parameters sampled: {'ema_fast': 29, 'ema_slow': 97, 'atr_len': 24, 'vol_sma_len': 20, 'sl_atr_mult': 2.030913233057735, 'trail_atr_mult': 3.7706194002807214, 'risk_multiplier': 2.5704603707932856, 'min_bars_between_trades': 2, 'use_volume_filter': True, 'st_len': 15, 'st_mult': 2.8478420501067383, 'adx_len': 23, 'adx_threshold': 22.923050128906944, 'trend_confirmations': 1, 'macd_fast': 14, 'macd_slow': 48, 'macd_signal': 12, 'rsi_len': 18, 'tp_atr_mult': 6.874588443936917}
2025-07-16 23:20:24,774 - root - WARNING - Trial 13 failed - ValueError: Invalid parameters sampled: {'ema_fast': 20, 'ema_slow': 100, 'atr_len': 10, 'vol_sma_len': 25, 'sl_atr_mult': 3.754746143855911, 'trail_atr_mult': 3.170459576813454, 'risk_multiplier': 3.24253935248817, 'min_bars_between_trades': 2, 'use_volume_filter': True, 'st_len': 34, 'st_mult': 4.335396881377132, 'adx_len': 23, 'adx_threshold': 37.83101381391178, 'trend_confirmations': 2, 'macd_fast': 13, 'macd_slow': 44, 'macd_signal': 12, 'rsi_len': 20, 'tp_atr_mult': 13.16213402492491}
2025-07-16 23:20:24,775 - root - WARNING - Trial 14 failed - ValueError: Invalid parameters sampled: {'ema_fast': 28, 'ema_slow': 47, 'atr_len': 14, 'vol_sma_len': 11, 'sl_atr_mult': 3.156560281992348, 'trail_atr_mult': 0.9150152761495747, 'risk_multiplier': 2.6639950453311503, 'min_bars_between_trades': 2, 'use_volume_filter': False, 'st_len': 6, 'st_mult': 1.6307186606222506, 'adx_len': 22, 'adx_threshold': 24.00476603528157, 'trend_confirmations': 1, 'macd_fast': 13, 'macd_slow': 43, 'macd_signal': 7, 'rsi_len': 19, 'tp_atr_mult': 6.768127184943912}
2025-07-16 23:20:24,777 - root - WARNING - Trial 15 failed - ValueError: Invalid parameters sampled: {'ema_fast': 6, 'ema_slow': 63, 'atr_len': 17, 'vol_sma_len': 29, 'sl_atr_mult': 3.452182667445323, 'trail_atr_mult': 3.922726654280111, 'risk_multiplier': 2.790750870752988, 'min_bars_between_trades': 1, 'use_volume_filter': True, 'st_len': 20, 'st_mult': 1.774597334697931, 'adx_len': 8, 'adx_threshold': 39.06621036694813, 'trend_confirmations': 2, 'macd_fast': 16, 'macd_slow': 31, 'macd_signal': 6, 'rsi_len': 10, 'tp_atr_mult': 8.252186083481359}
2025-07-16 23:20:24,779 - root - WARNING - Trial 16 failed - ValueError: Invalid parameters sampled: {'ema_fast': 19, 'ema_slow': 77, 'atr_len': 19, 'vol_sma_len': 17, 'sl_atr_mult': 3.909730561326388, 'trail_atr_mult': 3.1612701334264592, 'risk_multiplier': 2.885885131278502, 'min_bars_between_trades': 2, 'use_volume_filter': True, 'st_len': 17, 'st_mult': 4.152461386625292, 'adx_len': 8, 'adx_threshold': 17.901816012672906, 'trend_confirmations': 1, 'macd_fast': 6, 'macd_slow': 46, 'macd_signal': 12, 'rsi_len': 16, 'tp_atr_mult': 6.880507445859013}
2025-07-16 23:20:24,780 - root - WARNING - Trial 17 failed - ValueError: Invalid parameters sampled: {'ema_fast': 17, 'ema_slow': 58, 'atr_len': 11, 'vol_sma_len': 22, 'sl_atr_mult': 2.797009468794747, 'trail_atr_mult': 2.770720313767093, 'risk_multiplier': 3.087734127169109, 'min_bars_between_trades': 1, 'use_volume_filter': False, 'st_len': 23, 'st_mult': 4.4977144441591275, 'adx_len': 19, 'adx_threshold': 19.073360677035744, 'trend_confirmations': 1, 'macd_fast': 15, 'macd_slow': 18, 'macd_signal': 11, 'rsi_len': 24, 'tp_atr_mult': 11.17926760088291}
2025-07-16 23:20:24,841 - root - WARNING - Trial 18 failed - ValueError: Invalid parameters sampled: {'ema_fast': 10, 'ema_slow': 82, 'atr_len': 20, 'vol_sma_len': 30, 'sl_atr_mult': 2.937778244709311, 'trail_atr_mult': 2.201858967877856, 'risk_multiplier': 1.5240439779740749, 'min_bars_between_trades': 1, 'use_volume_filter': False, 'st_len': 28, 'st_mult': 3.403074199164126, 'adx_len': 14, 'adx_threshold': 15.689601652243532, 'trend_confirmations': 1, 'macd_fast': 20, 'macd_slow': 24, 'macd_signal': 9, 'rsi_len': 21, 'tp_atr_mult': 9.610357407266028}
2025-07-16 23:20:24,892 - root - WARNING - Trial 19 failed - ValueError: Invalid parameters sampled: {'ema_fast': 11, 'ema_slow': 49, 'atr_len': 22, 'vol_sma_len': 30, 'sl_atr_mult': 2.2438836452010444, 'trail_atr_mult': 1.3712416500090063, 'risk_multiplier': 2.0498772432050067, 'min_bars_between_trades': 2, 'use_volume_filter': False, 'st_len': 11, 'st_mult': 4.972065322786524, 'adx_len': 15, 'adx_threshold': 31.97479191089225, 'trend_confirmations': 1, 'macd_fast': 6, 'macd_slow': 23, 'macd_signal': 15, 'rsi_len': 16, 'tp_atr_mult': 9.604866288596508}
2025-07-16 23:20:24,956 - root - WARNING - Trial 20 failed - ValueError: Invalid parameters sampled: {'ema_fast': 24, 'ema_slow': 20, 'atr_len': 15, 'vol_sma_len': 14, 'sl_atr_mult': 3.485927447555941, 'trail_atr_mult': 3.5783489442560805, 'risk_multiplier': 1.5747652813924093, 'min_bars_between_trades': 1, 'use_volume_filter': False, 'st_len': 27, 'st_mult': 2.808921970095546, 'adx_len': 12, 'adx_threshold': 33.167069582769685, 'trend_confirmations': 1, 'macd_fast': 18, 'macd_slow': 32, 'macd_signal': 10, 'rsi_len': 22, 'tp_atr_mult': 11.727319378823598}
2025-07-16 23:20:25,008 - root - WARNING - Trial 21 failed - ValueError: Invalid parameters sampled: {'ema_fast': 9, 'ema_slow': 87, 'atr_len': 8, 'vol_sma_len': 39, 'sl_atr_mult': 2.7775383895727725, 'trail_atr_mult': 3.2543834614759204, 'risk_multiplier': 2.187132372488157, 'min_bars_between_trades': 2, 'use_volume_filter': True, 'st_len': 39, 'st_mult': 4.897131804446349, 'adx_len': 25, 'adx_threshold': 39.15181438666859, 'trend_confirmations': 2, 'macd_fast': 18, 'macd_slow': 18, 'macd_signal': 7, 'rsi_len': 9, 'tp_atr_mult': 9.020279162109832}
2025-07-16 23:20:25,062 - root - WARNING - Trial 22 failed - ValueError: Invalid parameters sampled: {'ema_fast': 17, 'ema_slow': 58, 'atr_len': 11, 'vol_sma_len': 34, 'sl_atr_mult': 2.307336225177311, 'trail_atr_mult': 2.718516112369129, 'risk_multiplier': 1.9418522719243074, 'min_bars_between_trades': 2, 'use_volume_filter': True, 'st_len': 40, 'st_mult': 3.7159209949157206, 'adx_len': 20, 'adx_threshold': 35.59127044835278, 'trend_confirmations': 2, 'macd_fast': 20, 'macd_slow': 22, 'macd_signal': 8, 'rsi_len': 11, 'tp_atr_mult': 8.47695740110828}
2025-07-16 23:20:25,115 - root - WARNING - Trial 23 failed - ValueError: Invalid parameters sampled: {'ema_fast': 8, 'ema_slow': 35, 'atr_len': 17, 'vol_sma_len': 33, 'sl_atr_mult': 2.834052170332559, 'trail_atr_mult': 2.473612162861648, 'risk_multiplier': 2.327664237881329, 'min_bars_between_trades': 2, 'use_volume_filter': True, 'st_len': 35, 'st_mult': 2.7914691950134136, 'adx_len': 14, 'adx_threshold': 36.96567516457583, 'trend_confirmations': 2, 'macd_fast': 17, 'macd_slow': 27, 'macd_signal': 6, 'rsi_len': 13, 'tp_atr_mult': 10.096030070888405}
2025-07-16 23:20:25,176 - root - WARNING - Trial 24 failed - ValueError: Invalid parameters sampled: {'ema_fast': 13, 'ema_slow': 50, 'atr_len': 22, 'vol_sma_len': 27, 'sl_atr_mult': 2.3286873050523385, 'trail_atr_mult': 2.9378684047770993, 'risk_multiplier': 1.7322742966016444, 'min_bars_between_trades': 2, 'use_volume_filter': True, 'st_len': 12, 'st_mult': 4.68662085329031, 'adx_len': 21, 'adx_threshold': 39.73340435797695, 'trend_confirmations': 2, 'macd_fast': 11, 'macd_slow': 30, 'macd_signal': 8, 'rsi_len': 17, 'tp_atr_mult': 8.425316955068343}
2025-07-16 23:20:25,238 - root - WARNING - Trial 25 failed - ValueError: Invalid parameters sampled: {'ema_fast': 11, 'ema_slow': 54, 'atr_len': 8, 'vol_sma_len': 33, 'sl_atr_mult': 3.0642830525671854, 'trail_atr_mult': 2.1967587233545065, 'risk_multiplier': 2.0787650814089993, 'min_bars_between_trades': 2, 'use_volume_filter': True, 'st_len': 5, 'st_mult': 3.570901922504669, 'adx_len': 25, 'adx_threshold': 33.42352140467496, 'trend_confirmations': 2, 'macd_fast': 18, 'macd_slow': 21, 'macd_signal': 6, 'rsi_len': 15, 'tp_atr_mult': 9.237670527402553}
2025-07-16 23:20:25,296 - root - WARNING - Trial 26 failed - ValueError: Invalid parameters sampled: {'ema_fast': 7, 'ema_slow': 80, 'atr_len': 12, 'vol_sma_len': 14, 'sl_atr_mult': 2.043962669377726, 'trail_atr_mult': 3.4253714439520078, 'risk_multiplier': 2.44080896980311, 'min_bars_between_trades': 2, 'use_volume_filter': True, 'st_len': 30, 'st_mult': 4.338323915558945, 'adx_len': 16, 'adx_threshold': 25.360779811349182, 'trend_confirmations': 1, 'macd_fast': 19, 'macd_slow': 26, 'macd_signal': 10, 'rsi_len': 8, 'tp_atr_mult': 10.076443932999341}
2025-07-16 23:20:25,348 - root - WARNING - Trial 27 failed - ValueError: Invalid parameters sampled: {'ema_fast': 16, 'ema_slow': 36, 'atr_len': 25, 'vol_sma_len': 40, 'sl_atr_mult': 2.719935648672245, 'trail_atr_mult': 1.1843560324447284, 'risk_multiplier': 1.9340547467791227, 'min_bars_between_trades': 2, 'use_volume_filter': True, 'st_len': 25, 'st_mult': 2.590876771631912, 'adx_len': 22, 'adx_threshold': 29.87036814774063, 'trend_confirmations': 2, 'macd_fast': 7, 'macd_slow': 37, 'macd_signal': 8, 'rsi_len': 21, 'tp_atr_mult': 8.073017973703923}
2025-07-16 23:20:25,407 - root - WARNING - Trial 28 failed - ValueError: Invalid parameters sampled: {'ema_fast': 19, 'ema_slow': 88, 'atr_len': 18, 'vol_sma_len': 27, 'sl_atr_mult': 2.3932922001433496, 'trail_atr_mult': 2.0245098975271314, 'risk_multiplier': 1.6789726178432423, 'min_bars_between_trades': 2, 'use_volume_filter': False, 'st_len': 20, 'st_mult': 3.040839777335763, 'adx_len': 12, 'adx_threshold': 15.168158585090957, 'trend_confirmations': 1, 'macd_fast': 11, 'macd_slow': 20, 'macd_signal': 5, 'rsi_len': 11, 'tp_atr_mult': 6.06286413029522}
2025-07-16 23:20:25,476 - root - WARNING - Trial 29 failed - ValueError: Invalid parameters sampled: {'ema_fast': 23, 'ema_slow': 44, 'atr_len': 21, 'vol_sma_len': 22, 'sl_atr_mult': 2.9175435254165314, 'trail_atr_mult': 2.52723606783631, 'risk_multiplier': 1.88643258721061, 'min_bars_between_trades': 2, 'use_volume_filter': False, 'st_len': 31, 'st_mult': 4.769367990186868, 'adx_len': 12, 'adx_threshold': 35.246517425383, 'trend_confirmations': 2, 'macd_fast': 15, 'macd_slow': 29, 'macd_signal': 5, 'rsi_len': 14, 'tp_atr_mult': 11.93288081644418}
2025-07-16 23:20:25,530 - root - WARNING - Trial 30 failed - ValueError: Invalid parameters sampled: {'ema_fast': 14, 'ema_slow': 20, 'atr_len': 14, 'vol_sma_len': 17, 'sl_atr_mult': 2.661971114155774, 'trail_atr_mult': 3.0567947054474525, 'risk_multiplier': 2.1799699741359078, 'min_bars_between_trades': 2, 'use_volume_filter': True, 'st_len': 26, 'st_mult': 1.9561000136602877, 'adx_len': 20, 'adx_threshold': 37.212944054057445, 'trend_confirmations': 2, 'macd_fast': 17, 'macd_slow': 25, 'macd_signal': 7, 'rsi_len': 17, 'tp_atr_mult': 8.88669832856292}
2025-07-16 23:20:25,591 - root - WARNING - Trial 31 failed - ValueError: Invalid parameters sampled: {'ema_fast': 15, 'ema_slow': 31, 'atr_len': 22, 'vol_sma_len': 20, 'sl_atr_mult': 2.1586744975164898, 'trail_atr_mult': 2.662640965171076, 'risk_multiplier': 1.8409613638127085, 'min_bars_between_trades': 2, 'use_volume_filter': False, 'st_len': 37, 'st_mult': 2.324768948120655, 'adx_len': 9, 'adx_threshold': 36.0877814303595, 'trend_confirmations': 2, 'macd_fast': 19, 'macd_slow': 34, 'macd_signal': 6, 'rsi_len': 14, 'tp_atr_mult': 7.72401334336229}
2025-07-16 23:20:25,657 - root - WARNING - Trial 32 failed - ValueError: Invalid parameters sampled: {'ema_fast': 12, 'ema_slow': 41, 'atr_len': 23, 'vol_sma_len': 15, 'sl_atr_mult': 2.601083039630547, 'trail_atr_mult': 1.7872413053001832, 'risk_multiplier': 1.5076812320928727, 'min_bars_between_trades': 2, 'use_volume_filter': False, 'st_len': 31, 'st_mult': 2.111375993206775, 'adx_len': 9, 'adx_threshold': 33.71822401200191, 'trend_confirmations': 2, 'macd_fast': 17, 'macd_slow': 32, 'macd_signal': 5, 'rsi_len': 8, 'tp_atr_mult': 6.09346189451147}
2025-07-16 23:20:25,730 - root - WARNING - Trial 33 failed - ValueError: Invalid parameters sampled: {'ema_fast': 19, 'ema_slow': 73, 'atr_len': 20, 'vol_sma_len': 23, 'sl_atr_mult': 2.4455888406177326, 'trail_atr_mult': 2.3418856342520815, 'risk_multiplier': 2.061592773593774, 'min_bars_between_trades': 2, 'use_volume_filter': False, 'st_len': 37, 'st_mult': 4.00840050347274, 'adx_len': 11, 'adx_threshold': 38.064795331084255, 'trend_confirmations': 2, 'macd_fast': 16, 'macd_slow': 37, 'macd_signal': 6, 'rsi_len': 13, 'tp_atr_mult': 10.669588499498932}
2025-07-16 23:20:25,789 - root - WARNING - Trial 34 failed - ValueError: Invalid parameters sampled: {'ema_fast': 18, 'ema_slow': 25, 'atr_len': 16, 'vol_sma_len': 8, 'sl_atr_mult': 2.6844966710636085, 'trail_atr_mult': 1.9210685140117219, 'risk_multiplier': 2.3983799300244444, 'min_bars_between_trades': 2, 'use_volume_filter': False, 'st_len': 33, 'st_mult': 2.6065026108606673, 'adx_len': 10, 'adx_threshold': 33.995549289031004, 'trend_confirmations': 2, 'macd_fast': 19, 'macd_slow': 49, 'macd_signal': 7, 'rsi_len': 15, 'tp_atr_mult': 10.025748242469797}
2025-07-16 23:20:25,849 - root - WARNING - Trial 35 failed - ValueError: Invalid parameters sampled: {'ema_fast': 5, 'ema_slow': 37, 'atr_len': 12, 'vol_sma_len': 19, 'sl_atr_mult': 2.389999857543355, 'trail_atr_mult': 3.3047105776423304, 'risk_multiplier': 1.6891009164438144, 'min_bars_between_trades': 2, 'use_volume_filter': False, 'st_len': 36, 'st_mult': 1.9525140270320365, 'adx_len': 8, 'adx_threshold': 39.95039328687155, 'trend_confirmations': 2, 'macd_fast': 14, 'macd_slow': 38, 'macd_signal': 8, 'rsi_len': 12, 'tp_atr_mult': 8.826372563541662}
2025-07-16 23:20:25,905 - root - WARNING - Trial 36 failed - ValueError: Invalid parameters sampled: {'ema_fast': 26, 'ema_slow': 53, 'atr_len': 18, 'vol_sma_len': 27, 'sl_atr_mult': 3.0668119057241934, 'trail_atr_mult': 2.9481269259290097, 'risk_multiplier': 2.251984287475719, 'min_bars_between_trades': 2, 'use_volume_filter': True, 'st_len': 22, 'st_mult': 2.4376113738093124, 'adx_len': 24, 'adx_threshold': 29.242064859452768, 'trend_confirmations': 2, 'macd_fast': 20, 'macd_slow': 34, 'macd_signal': 11, 'rsi_len': 18, 'tp_atr_mult': 7.672743694921468}
2025-07-16 23:20:25,967 - root - WARNING - Trial 37 failed - ValueError: Invalid parameters sampled: {'ema_fast': 15, 'ema_slow': 43, 'atr_len': 25, 'vol_sma_len': 16, 'sl_atr_mult': 2.5121988718151864, 'trail_atr_mult': 3.5647089049278695, 'risk_multiplier': 1.8354545033762446, 'min_bars_between_trades': 2, 'use_volume_filter': False, 'st_len': 39, 'st_mult': 2.1211840725227034, 'adx_len': 22, 'adx_threshold': 31.924695628246507, 'trend_confirmations': 2, 'macd_fast': 12, 'macd_slow': 20, 'macd_signal': 9, 'rsi_len': 10, 'tp_atr_mult': 9.590290206813679}
2025-07-16 23:20:26,024 - root - WARNING - Trial 38 failed - ValueError: Invalid parameters sampled: {'ema_fast': 21, 'ema_slow': 31, 'atr_len': 15, 'vol_sma_len': 13, 'sl_atr_mult': 2.193103391923057, 'trail_atr_mult': 2.5876739954793484, 'risk_multiplier': 2.0345742896492838, 'min_bars_between_trades': 2, 'use_volume_filter': True, 'st_len': 18, 'st_mult': 3.7885111225981736, 'adx_len': 11, 'adx_threshold': 25.79310954174621, 'trend_confirmations': 2, 'macd_fast': 18, 'macd_slow': 24, 'macd_signal': 5, 'rsi_len': 15, 'tp_atr_mult': 11.085298687427525}
2025-07-16 23:20:26,081 - root - WARNING - Trial 39 failed - ValueError: Invalid parameters sampled: {'ema_fast': 16, 'ema_slow': 66, 'atr_len': 21, 'vol_sma_len': 19, 'sl_atr_mult': 3.3635196188886543, 'trail_atr_mult': 2.3571870600189713, 'risk_multiplier': 2.493059781157816, 'min_bars_between_trades': 1, 'use_volume_filter': False, 'st_len': 32, 'st_mult': 3.0777262189543855, 'adx_len': 13, 'adx_threshold': 21.797260656413677, 'trend_confirmations': 2, 'macd_fast': 10, 'macd_slow': 29, 'macd_signal': 9, 'rsi_len': 25, 'tp_atr_mult': 6.50328181289019}
2025-07-16 23:20:26,140 - root - WARNING - Trial 40 failed - ValueError: Invalid parameters sampled: {'ema_fast': 9, 'ema_slow': 25, 'atr_len': 9, 'vol_sma_len': 37, 'sl_atr_mult': 2.5572687333560675, 'trail_atr_mult': 1.1818226248056631, 'risk_multiplier': 1.625480471039159, 'min_bars_between_trades': 2, 'use_volume_filter': True, 'st_len': 29, 'st_mult': 4.443084597374386, 'adx_len': 18, 'adx_threshold': 36.497274393589954, 'trend_confirmations': 1, 'macd_fast': 8, 'macd_slow': 41, 'macd_signal': 6, 'rsi_len': 13, 'tp_atr_mult': 14.680876729451963}
2025-07-16 23:20:26,198 - root - WARNING - Trial 41 failed - ValueError: Invalid parameters sampled: {'ema_fast': 26, 'ema_slow': 71, 'atr_len': 13, 'vol_sma_len': 10, 'sl_atr_mult': 2.61406916407726, 'trail_atr_mult': 1.669665050979741, 'risk_multiplier': 3.528853055676966, 'min_bars_between_trades': 2, 'use_volume_filter': True, 'st_len': 8, 'st_mult': 4.114587513225037, 'adx_len': 24, 'adx_threshold': 28.553095508267027, 'trend_confirmations': 2, 'macd_fast': 15, 'macd_slow': 36, 'macd_signal': 11, 'rsi_len': 9, 'tp_atr_mult': 7.262400140494115}
2025-07-16 23:20:26,266 - root - WARNING - Trial 42 failed - ValueError: Invalid parameters sampled: {'ema_fast': 28, 'ema_slow': 93, 'atr_len': 13, 'vol_sma_len': 25, 'sl_atr_mult': 2.886806417366355, 'trail_atr_mult': 1.4614647606466455, 'risk_multiplier': 3.1302747414862613, 'min_bars_between_trades': 2, 'use_volume_filter': True, 'st_len': 7, 'st_mult': 3.429908486638639, 'adx_len': 23, 'adx_threshold': 34.708802216336956, 'trend_confirmations': 2, 'macd_fast': 12, 'macd_slow': 39, 'macd_signal': 10, 'rsi_len': 8, 'tp_atr_mult': 7.293565636769424}
2025-07-16 23:20:26,326 - root - WARNING - Trial 43 failed - ValueError: Invalid parameters sampled: {'ema_fast': 30, 'ema_slow': 68, 'atr_len': 10, 'vol_sma_len': 8, 'sl_atr_mult': 2.461091509980017, 'trail_atr_mult': 1.990534972563981, 'risk_multiplier': 3.889949413628523, 'min_bars_between_trades': 2, 'use_volume_filter': True, 'st_len': 10, 'st_mult': 1.7513737294210774, 'adx_len': 21, 'adx_threshold': 38.267320976148575, 'trend_confirmations': 2, 'macd_fast': 9, 'macd_slow': 42, 'macd_signal': 8, 'rsi_len': 10, 'tp_atr_mult': 7.907667324053354}
2025-07-16 23:20:26,387 - root - WARNING - Trial 44 failed - ValueError: Invalid parameters sampled: {'ema_fast': 13, 'ema_slow': 83, 'atr_len': 15, 'vol_sma_len': 18, 'sl_atr_mult': 2.73935374505334, 'trail_atr_mult': 2.826514599234851, 'risk_multiplier': 3.4353840047181823, 'min_bars_between_trades': 2, 'use_volume_filter': True, 'st_len': 9, 'st_mult': 4.23047639844897, 'adx_len': 17, 'adx_threshold': 31.255650755494912, 'trend_confirmations': 2, 'macd_fast': 14, 'macd_slow': 32, 'macd_signal': 9, 'rsi_len': 18, 'tp_atr_mult': 8.541765199724969}
2025-07-16 23:20:26,448 - root - WARNING - Trial 45 failed - ValueError: Invalid parameters sampled: {'ema_fast': 21, 'ema_slow': 100, 'atr_len': 23, 'vol_sma_len': 21, 'sl_atr_mult': 2.2819244687446734, 'trail_atr_mult': 1.1231947504759512, 'risk_multiplier': 3.80481171912169, 'min_bars_between_trades': 2, 'use_volume_filter': True, 'st_len': 22, 'st_mult': 1.507423910200321, 'adx_len': 24, 'adx_threshold': 17.181590267710252, 'trend_confirmations': 2, 'macd_fast': 17, 'macd_slow': 35, 'macd_signal': 7, 'rsi_len': 9, 'tp_atr_mult': 6.595991766356472}
2025-07-16 23:20:26,522 - root - WARNING - Trial 46 failed - ValueError: Invalid parameters sampled: {'ema_fast': 23, 'ema_slow': 76, 'atr_len': 12, 'vol_sma_len': 11, 'sl_atr_mult': 2.1144351647590804, 'trail_atr_mult': 1.5303887802225795, 'risk_multiplier': 2.9363047365917034, 'min_bars_between_trades': 2, 'use_volume_filter': False, 'st_len': 13, 'st_mult': 4.553230949363276, 'adx_len': 21, 'adx_threshold': 25.802087349551766, 'trend_confirmations': 2, 'macd_fast': 8, 'macd_slow': 46, 'macd_signal': 7, 'rsi_len': 20, 'tp_atr_mult': 7.2769283018823465}
2025-07-16 23:20:26,580 - root - WARNING - Trial 47 failed - ValueError: Invalid parameters sampled: {'ema_fast': 25, 'ema_slow': 94, 'atr_len': 19, 'vol_sma_len': 24, 'sl_atr_mult': 3.2589270954436858, 'trail_atr_mult': 1.7528835305406711, 'risk_multiplier': 3.269437742213928, 'min_bars_between_trades': 2, 'use_volume_filter': True, 'st_len': 15, 'st_mult': 4.81808267126777, 'adx_len': 23, 'adx_threshold': 27.64913876785208, 'trend_confirmations': 1, 'macd_fast': 16, 'macd_slow': 45, 'macd_signal': 11, 'rsi_len': 12, 'tp_atr_mult': 9.326038252374957}
2025-07-16 23:20:26,642 - root - WARNING - Trial 48 failed - ValueError: Invalid parameters sampled: {'ema_fast': 12, 'ema_slow': 62, 'atr_len': 9, 'vol_sma_len': 31, 'sl_atr_mult': 2.972888756133687, 'trail_atr_mult': 3.705237532858181, 'risk_multiplier': 2.6202970138728716, 'min_bars_between_trades': 1, 'use_volume_filter': False, 'st_len': 5, 'st_mult': 3.9270470335858736, 'adx_len': 19, 'adx_threshold': 21.33050174786966, 'trend_confirmations': 2, 'macd_fast': 13, 'macd_slow': 27, 'macd_signal': 13, 'rsi_len': 11, 'tp_atr_mult': 8.143134918860788}
2025-07-16 23:20:26,705 - root - WARNING - Trial 49 failed - ValueError: Invalid parameters sampled: {'ema_fast': 18, 'ema_slow': 87, 'atr_len': 14, 'vol_sma_len': 35, 'sl_atr_mult': 2.494830510477002, 'trail_atr_mult': 1.3565524755512501, 'risk_multiplier': 2.7397850395210077, 'min_bars_between_trades': 2, 'use_volume_filter': True, 'st_len': 24, 'st_mult': 3.738850019451483, 'adx_len': 9, 'adx_threshold': 23.674584679852426, 'trend_confirmations': 1, 'macd_fast': 10, 'macd_slow': 33, 'macd_signal': 5, 'rsi_len': 23, 'tp_atr_mult': 6.893740344969845}
2025-07-16 23:20:26,783 - root - WARNING - Trial 50 failed - ValueError: Invalid parameters sampled: {'ema_fast': 28, 'ema_slow': 47, 'atr_len': 20, 'vol_sma_len': 12, 'sl_atr_mult': 2.8291286114991503, 'trail_atr_mult': 3.0185524450576637, 'risk_multiplier': 3.7064860128986643, 'min_bars_between_trades': 2, 'use_volume_filter': False, 'st_len': 28, 'st_mult': 4.326762285794194, 'adx_len': 25, 'adx_threshold': 38.49784903497788, 'trend_confirmations': 2, 'macd_fast': 19, 'macd_slow': 30, 'macd_signal': 9, 'rsi_len': 19, 'tp_atr_mult': 10.580613972163981}
2025-07-16 23:20:26,854 - root - WARNING - Trial 51 failed - ValueError: Invalid parameters sampled: {'ema_fast': 5, 'ema_slow': 56, 'atr_len': 11, 'vol_sma_len': 24, 'sl_atr_mult': 3.924332615401471, 'trail_atr_mult': 1.0337088765898585, 'risk_multiplier': 2.341817051754725, 'min_bars_between_trades': 2, 'use_volume_filter': True, 'st_len': 7, 'st_mult': 1.9380892459198023, 'adx_len': 20, 'adx_threshold': 19.839949316724823, 'trend_confirmations': 2, 'macd_fast': 20, 'macd_slow': 43, 'macd_signal': 6, 'rsi_len': 25, 'tp_atr_mult': 11.075957653276179}
2025-07-16 23:20:26,919 - root - WARNING - Trial 52 failed - ValueError: Invalid parameters sampled: {'ema_fast': 6, 'ema_slow': 66, 'atr_len': 14, 'vol_sma_len': 28, 'sl_atr_mult': 2.6246990372642767, 'trail_atr_mult': 1.3743092231356973, 'risk_multiplier': 2.148465377994274, 'min_bars_between_trades': 2, 'use_volume_filter': True, 'st_len': 14, 'st_mult': 2.1994260273639785, 'adx_len': 23, 'adx_threshold': 32.7148838041687, 'trend_confirmations': 2, 'macd_fast': 19, 'macd_slow': 48, 'macd_signal': 10, 'rsi_len': 22, 'tp_atr_mult': 11.44108337620446}
2025-07-16 23:20:26,992 - root - WARNING - Trial 53 failed - ValueError: Invalid parameters sampled: {'ema_fast': 7, 'ema_slow': 75, 'atr_len': 16, 'vol_sma_len': 26, 'sl_atr_mult': 3.8009530996364713, 'trail_atr_mult': 0.8534366225675734, 'risk_multiplier': 2.550922463536237, 'min_bars_between_trades': 2, 'use_volume_filter': True, 'st_len': 17, 'st_mult': 2.495858220940488, 'adx_len': 24, 'adx_threshold': 34.81490374378773, 'trend_confirmations': 2, 'macd_fast': 18, 'macd_slow': 40, 'macd_signal': 7, 'rsi_len': 24, 'tp_atr_mult': 10.21588367990051}
2025-07-16 23:20:27,071 - root - WARNING - Trial 54 failed - ValueError: Invalid parameters sampled: {'ema_fast': 8, 'ema_slow': 79, 'atr_len': 13, 'vol_sma_len': 22, 'sl_atr_mult': 3.535064928412813, 'trail_atr_mult': 3.1078964069703066, 'risk_multiplier': 2.7695737407343817, 'min_bars_between_trades': 2, 'use_volume_filter': True, 'st_len': 11, 'st_mult': 1.6631414487643552, 'adx_len': 22, 'adx_threshold': 37.399013867897736, 'trend_confirmations': 2, 'macd_fast': 17, 'macd_slow': 45, 'macd_signal': 8, 'rsi_len': 16, 'tp_atr_mult': 9.703587203708908}
2025-07-16 23:20:27,146 - root - WARNING - Trial 55 failed - ValueError: Invalid parameters sampled: {'ema_fast': 10, 'ema_slow': 62, 'atr_len': 17, 'vol_sma_len': 31, 'sl_atr_mult': 2.368854376459893, 'trail_atr_mult': 2.1282315053465792, 'risk_multiplier': 1.9614872231914326, 'min_bars_between_trades': 2, 'use_volume_filter': True, 'st_len': 20, 'st_mult': 1.885595197096963, 'adx_len': 23, 'adx_threshold': 30.622562514723136, 'trend_confirmations': 2, 'macd_fast': 15, 'macd_slow': 22, 'macd_signal': 6, 'rsi_len': 20, 'tp_atr_mult': 8.673351698293967}
2025-07-16 23:20:27,225 - root - WARNING - Trial 56 failed - ValueError: Invalid parameters sampled: {'ema_fast': 6, 'ema_slow': 69, 'atr_len': 10, 'vol_sma_len': 38, 'sl_atr_mult': 3.11493683343395, 'trail_atr_mult': 1.8484186661496858, 'risk_multiplier': 1.7603108853967344, 'min_bars_between_trades': 2, 'use_volume_filter': True, 'st_len': 34, 'st_mult': 4.633235914219126, 'adx_len': 25, 'adx_threshold': 35.47352191066603, 'trend_confirmations': 1, 'macd_fast': 7, 'macd_slow': 28, 'macd_signal': 5, 'rsi_len': 22, 'tp_atr_mult': 12.191479734495681}
2025-07-16 23:20:27,311 - root - WARNING - Trial 57 failed - ValueError: Invalid parameters sampled: {'ema_fast': 15, 'ema_slow': 39, 'atr_len': 11, 'vol_sma_len': 21, 'sl_atr_mult': 2.241082248409686, 'trail_atr_mult': 3.3916037816956903, 'risk_multiplier': 2.2018965849853083, 'min_bars_between_trades': 2, 'use_volume_filter': False, 'st_len': 9, 'st_mult': 3.2217477997266357, 'adx_len': 8, 'adx_threshold': 36.55177516690857, 'trend_confirmations': 2, 'macd_fast': 18, 'macd_slow': 47, 'macd_signal': 7, 'rsi_len': 18, 'tp_atr_mult': 10.785399629611703}
2025-07-16 23:20:27,381 - root - WARNING - Trial 58 failed - ValueError: Invalid parameters sampled: {'ema_fast': 20, 'ema_slow': 28, 'atr_len': 24, 'vol_sma_len': 18, 'sl_atr_mult': 3.6605088884189856, 'trail_atr_mult': 1.6413404937513771, 'risk_multiplier': 2.296393932846846, 'min_bars_between_trades': 1, 'use_volume_filter': True, 'st_len': 18, 'st_mult': 4.996874360864385, 'adx_len': 21, 'adx_threshold': 24.644443008029096, 'trend_confirmations': 2, 'macd_fast': 20, 'macd_slow': 43, 'macd_signal': 10, 'rsi_len': 21, 'tp_atr_mult': 9.171563698611873}
2025-07-16 23:20:27,454 - root - WARNING - Trial 59 failed - ValueError: Invalid parameters sampled: {'ema_fast': 14, 'ema_slow': 59, 'atr_len': 8, 'vol_sma_len': 29, 'sl_atr_mult': 3.2509101039287787, 'trail_atr_mult': 3.9662996584480643, 'risk_multiplier': 1.7714089707763776, 'min_bars_between_trades': 2, 'use_volume_filter': True, 'st_len': 5, 'st_mult': 3.56192219407724, 'adx_len': 16, 'adx_threshold': 38.72232917583658, 'trend_confirmations': 1, 'macd_fast': 19, 'macd_slow': 18, 'macd_signal': 15, 'rsi_len': 23, 'tp_atr_mult': 10.36084829782843}
2025-07-16 23:20:27,544 - root - WARNING - Trial 60 failed - ValueError: Invalid parameters sampled: {'ema_fast': 11, 'ema_slow': 33, 'atr_len': 12, 'vol_sma_len': 24, 'sl_atr_mult': 2.7812225674023296, 'trail_atr_mult': 2.813706876251615, 'risk_multiplier': 1.5911439789660786, 'min_bars_between_trades': 2, 'use_volume_filter': False, 'st_len': 7, 'st_mult': 2.2348112928259436, 'adx_len': 22, 'adx_threshold': 22.837683432119736, 'trend_confirmations': 2, 'macd_fast': 18, 'macd_slow': 50, 'macd_signal': 8, 'rsi_len': 14, 'tp_atr_mult': 12.900718147798415}
2025-07-16 23:20:27,613 - root - WARNING - Trial 61 failed - ValueError: Invalid parameters sampled: {'ema_fast': 27, 'ema_slow': 91, 'atr_len': 13, 'vol_sma_len': 10, 'sl_atr_mult': 2.560582873517671, 'trail_atr_mult': 2.225950582765805, 'risk_multiplier': 3.3152082561544765, 'min_bars_between_trades': 2, 'use_volume_filter': False, 'st_len': 26, 'st_mult': 2.0844189981883545, 'adx_len': 10, 'adx_threshold': 20.13638875536063, 'trend_confirmations': 2, 'macd_fast': 7, 'macd_slow': 36, 'macd_signal': 13, 'rsi_len': 17, 'tp_atr_mult': 14.072155963482766}
2025-07-16 23:20:27,682 - root - WARNING - Trial 62 failed - ValueError: Invalid parameters sampled: {'ema_fast': 29, 'ema_slow': 84, 'atr_len': 15, 'vol_sma_len': 15, 'sl_atr_mult': 3.9906037549765143, 'trail_atr_mult': 2.4197957994675736, 'risk_multiplier': 3.7026228379665485, 'min_bars_between_trades': 2, 'use_volume_filter': False, 'st_len': 16, 'st_mult': 2.3226928361750843, 'adx_len': 10, 'adx_threshold': 18.996501048666858, 'trend_confirmations': 2, 'macd_fast': 9, 'macd_slow': 39, 'macd_signal': 12, 'rsi_len': 16, 'tp_atr_mult': 14.953404922780754}
2025-07-16 23:20:27,758 - root - WARNING - Trial 63 failed - ValueError: Invalid parameters sampled: {'ema_fast': 25, 'ema_slow': 99, 'atr_len': 14, 'vol_sma_len': 10, 'sl_atr_mult': 2.408627276909166, 'trail_atr_mult': 2.061082486270357, 'risk_multiplier': 3.4039304099048096, 'min_bars_between_trades': 2, 'use_volume_filter': False, 'st_len': 20, 'st_mult': 2.690382972785167, 'adx_len': 9, 'adx_threshold': 26.793414962762544, 'trend_confirmations': 2, 'macd_fast': 10, 'macd_slow': 25, 'macd_signal': 14, 'rsi_len': 15, 'tp_atr_mult': 13.469213058438502}
2025-07-16 23:20:27,825 - root - WARNING - Trial 64 failed - ValueError: Invalid parameters sampled: {'ema_fast': 23, 'ema_slow': 96, 'atr_len': 18, 'vol_sma_len': 13, 'sl_atr_mult': 2.322142854033154, 'trail_atr_mult': 1.8958282515599785, 'risk_multiplier': 3.1501390645145055, 'min_bars_between_trades': 2, 'use_volume_filter': False, 'st_len': 12, 'st_mult': 2.92955590577232, 'adx_len': 8, 'adx_threshold': 22.010015710135786, 'trend_confirmations': 2, 'macd_fast': 12, 'macd_slow': 31, 'macd_signal': 12, 'rsi_len': 14, 'tp_atr_mult': 6.395922714744824}
2025-07-16 23:20:27,896 - root - WARNING - Trial 65 failed - ValueError: Invalid parameters sampled: {'ema_fast': 16, 'ema_slow': 90, 'atr_len': 21, 'vol_sma_len': 16, 'sl_atr_mult': 2.684855970984194, 'trail_atr_mult': 2.6871386695381365, 'risk_multiplier': 1.9985034754186375, 'min_bars_between_trades': 2, 'use_volume_filter': False, 'st_len': 23, 'st_mult': 1.7988452330221594, 'adx_len': 11, 'adx_threshold': 16.728877791865024, 'trend_confirmations': 2, 'macd_fast': 16, 'macd_slow': 33, 'macd_signal': 6, 'rsi_len': 12, 'tp_atr_mult': 9.879752769457431}
2025-07-16 23:20:27,961 - root - WARNING - Trial 66 failed - ValueError: Invalid parameters sampled: {'ema_fast': 24, 'ema_slow': 52, 'atr_len': 23, 'vol_sma_len': 23, 'sl_atr_mult': 2.459558208451971, 'trail_atr_mult': 2.3080827085894136, 'risk_multiplier': 2.109421810318744, 'min_bars_between_trades': 2, 'use_volume_filter': True, 'st_len': 40, 'st_mult': 2.4204813335364674, 'adx_len': 9, 'adx_threshold': 39.373536145123964, 'trend_confirmations': 2, 'macd_fast': 11, 'macd_slow': 41, 'macd_signal': 14, 'rsi_len': 10, 'tp_atr_mult': 6.959111801948973}
2025-07-16 23:20:28,040 - root - WARNING - Trial 67 failed - ValueError: Invalid parameters sampled: {'ema_fast': 22, 'ema_slow': 97, 'atr_len': 12, 'vol_sma_len': 9, 'sl_atr_mult': 2.871567239960076, 'trail_atr_mult': 3.2679195581672045, 'risk_multiplier': 1.8802645548959773, 'min_bars_between_trades': 2, 'use_volume_filter': False, 'st_len': 38, 'st_mult': 2.044532153198006, 'adx_len': 24, 'adx_threshold': 37.588858411075876, 'trend_confirmations': 2, 'macd_fast': 6, 'macd_slow': 26, 'macd_signal': 10, 'rsi_len': 13, 'tp_atr_mult': 8.314456762857521}
2025-07-16 23:20:28,110 - root - WARNING - Trial 68 failed - ValueError: Invalid parameters sampled: {'ema_fast': 27, 'ema_slow': 73, 'atr_len': 11, 'vol_sma_len': 26, 'sl_atr_mult': 3.0235686588593262, 'trail_atr_mult': 2.534816315685302, 'risk_multiplier': 3.515823171772781, 'min_bars_between_trades': 2, 'use_volume_filter': True, 'st_len': 21, 'st_mult': 2.2558002953698066, 'adx_len': 14, 'adx_threshold': 18.68817103200347, 'trend_confirmations': 2, 'macd_fast': 8, 'macd_slow': 35, 'macd_signal': 5, 'rsi_len': 8, 'tp_atr_mult': 12.350342073514541}
2025-07-16 23:20:28,176 - root - WARNING - Trial 69 failed - ValueError: Invalid parameters sampled: {'ema_fast': 17, 'ema_slow': 86, 'atr_len': 16, 'vol_sma_len': 12, 'sl_atr_mult': 2.5146780841583727, 'trail_atr_mult': 1.7551510563253951, 'risk_multiplier': 2.9275555332152607, 'min_bars_between_trades': 2, 'use_volume_filter': False, 'st_len': 35, 'st_mult': 4.09041702629284, 'adx_len': 13, 'adx_threshold': 35.980807243455764, 'trend_confirmations': 1, 'macd_fast': 14, 'macd_slow': 38, 'macd_signal': 9, 'rsi_len': 15, 'tp_atr_mult': 7.834843635568786}
2025-07-16 23:20:28,243 - root - WARNING - Trial 70 failed - ValueError: Invalid parameters sampled: {'ema_fast': 20, 'ema_slow': 78, 'atr_len': 22, 'vol_sma_len': 20, 'sl_atr_mult': 2.2402509033647364, 'trail_atr_mult': 1.5792235663146656, 'risk_multiplier': 2.4244195106706776, 'min_bars_between_trades': 2, 'use_volume_filter': True, 'st_len': 29, 'st_mult': 1.6276407568496076, 'adx_len': 25, 'adx_threshold': 34.370761572232986, 'trend_confirmations': 2, 'macd_fast': 17, 'macd_slow': 23, 'macd_signal': 11, 'rsi_len': 17, 'tp_atr_mult': 9.311152645602803}
2025-07-16 23:20:28,320 - root - WARNING - Trial 71 failed - ValueError: Invalid parameters sampled: {'ema_fast': 30, 'ema_slow': 45, 'atr_len': 19, 'vol_sma_len': 18, 'sl_atr_mult': 2.5802130384605007, 'trail_atr_mult': 0.9689199990318926, 'risk_multiplier': 3.0279504713046306, 'min_bars_between_trades': 2, 'use_volume_filter': False, 'st_len': 38, 'st_mult': 2.3525459200771257, 'adx_len': 10, 'adx_threshold': 27.820419186956272, 'trend_confirmations': 2, 'macd_fast': 9, 'macd_slow': 45, 'macd_signal': 13, 'rsi_len': 14, 'tp_atr_mult': 14.055541960723898}
2025-07-16 23:20:28,389 - root - WARNING - Trial 72 failed - ValueError: Invalid parameters sampled: {'ema_fast': 29, 'ema_slow': 40, 'atr_len': 13, 'vol_sma_len': 17, 'sl_atr_mult': 2.6578890891649745, 'trail_atr_mult': 1.4367904969270011, 'risk_multiplier': 3.6701659566661986, 'min_bars_between_trades': 2, 'use_volume_filter': False, 'st_len': 33, 'st_mult': 2.009444269654018, 'adx_len': 8, 'adx_threshold': 32.84206161308393, 'trend_confirmations': 2, 'macd_fast': 8, 'macd_slow': 42, 'macd_signal': 12, 'rsi_len': 9, 'tp_atr_mult': 7.5502888621045585}
2025-07-16 23:20:28,457 - root - WARNING - Trial 73 failed - ValueError: Invalid parameters sampled: {'ema_fast': 8, 'ema_slow': 34, 'atr_len': 17, 'vol_sma_len': 21, 'sl_atr_mult': 2.3691599473726694, 'trail_atr_mult': 1.2747097578197348, 'risk_multiplier': 3.6103885170746426, 'min_bars_between_trades': 2, 'use_volume_filter': False, 'st_len': 36, 'st_mult': 2.527465408225371, 'adx_len': 11, 'adx_threshold': 23.32749355303556, 'trend_confirmations': 2, 'macd_fast': 12, 'macd_slow': 40, 'macd_signal': 13, 'rsi_len': 11, 'tp_atr_mult': 13.239873140897434}
2025-07-16 23:20:28,530 - root - WARNING - Trial 74 failed - ValueError: Invalid parameters sampled: {'ema_fast': 27, 'ema_slow': 27, 'atr_len': 14, 'vol_sma_len': 15, 'sl_atr_mult': 2.717166191777667, 'trail_atr_mult': 1.1200950593000483, 'risk_multiplier': 2.8382456959273012, 'min_bars_between_trades': 2, 'use_volume_filter': False, 'st_len': 31, 'st_mult': 2.1254681922262706, 'adx_len': 18, 'adx_threshold': 26.657029521861457, 'trend_confirmations': 2, 'macd_fast': 9, 'macd_slow': 44, 'macd_signal': 14, 'rsi_len': 12, 'tp_atr_mult': 12.717318029948522}
2025-07-16 23:20:28,538 - __main__ - ERROR - Error optimizing BTCUSDT Trend: Indicator calculation failed: EMA calculation failed: Invalid EMA parameters: fast=14, slow=97 with params: {'ema_fast': 14, 'ema_slow': 97, 'atr_len': 21, 'vol_sma_len': 27, 'sl_atr_mult': 2.312037280884873, 'trail_atr_mult': 1.2991824650758486, 'risk_multiplier': 1.6452090304204987, 'min_bars_between_trades': 2, 'use_volume_filter': False, 'st_len': 5, 'st_mult': 4.89468448256698, 'adx_len': 22, 'adx_threshold': 20.308477766956905, 'trend_confirmations': 1, 'macd_fast': 8, 'macd_slow': 28, 'macd_signal': 10, 'rsi_len': 15, 'tp_atr_mult': 8.621062261782377}
2025-07-16 23:20:28,538 - __main__ - INFO - Running walk-forward validation for BTCUSDT Trend
2025-07-16 23:20:28,538 - __main__ - INFO - Total data available: 15.6 days (1500 bars)
2025-07-16 23:20:28,538 - __main__ - INFO - Walk-forward parameters: IS=7d, OOS=3d, step=1d
2025-07-16 23:20:28,541 - root - WARNING - Trial 0 failed - ValueError: Invalid parameters sampled: {'ema_fast': 14, 'ema_slow': 97, 'atr_len': 21, 'vol_sma_len': 27, 'sl_atr_mult': 2.312037280884873, 'trail_atr_mult': 1.2991824650758486, 'risk_multiplier': 1.6452090304204987, 'min_bars_between_trades': 2, 'use_volume_filter': False, 'st_len': 5, 'st_mult': 4.89468448256698, 'adx_len': 22, 'adx_threshold': 20.308477766956905, 'trend_confirmations': 1, 'macd_fast': 8, 'macd_slow': 28, 'macd_signal': 10, 'rsi_len': 15, 'tp_atr_mult': 8.621062261782377}
2025-07-16 23:20:28,544 - root - WARNING - Trial 1 failed - ValueError: Invalid parameters sampled: {'ema_fast': 20, 'ema_slow': 31, 'atr_len': 13, 'vol_sma_len': 20, 'sl_atr_mult': 2.912139968434072, 'trail_atr_mult': 3.3125630764576437, 'risk_multiplier': 1.9991844553958993, 'min_bars_between_trades': 2, 'use_volume_filter': True, 'st_len': 26, 'st_mult': 2.0968344329055206, 'adx_len': 9, 'adx_threshold': 38.722138431333335, 'trend_confirmations': 2, 'macd_fast': 18, 'macd_slow': 28, 'macd_signal': 6, 'rsi_len': 20, 'tp_atr_mult': 9.961372443656412}
2025-07-16 23:20:28,587 - root - WARNING - Trial 2 failed - ValueError: Invalid parameters sampled: {'ema_fast': 6, 'ema_slow': 95, 'atr_len': 25, 'vol_sma_len': 38, 'sl_atr_mult': 2.0307354746562085, 'trail_atr_mult': 0.9239206398757867, 'risk_multiplier': 1.5378605927568922, 'min_bars_between_trades': 1, 'use_volume_filter': False, 'st_len': 5, 'st_mult': 4.973414494021188, 'adx_len': 25, 'adx_threshold': 15.714227196361005, 'trend_confirmations': 1, 'macd_fast': 6, 'macd_slow': 25, 'macd_signal': 13, 'rsi_len': 9, 'tp_atr_mult': 6.111631183842814}
2025-07-16 23:20:28,631 - root - WARNING - Trial 3 failed - ValueError: Invalid parameters sampled: {'ema_fast': 11, 'ema_slow': 98, 'atr_len': 22, 'vol_sma_len': 30, 'sl_atr_mult': 2.2639220099054067, 'trail_atr_mult': 1.2388143135812206, 'risk_multiplier': 3.719448856348819, 'min_bars_between_trades': 2, 'use_volume_filter': False, 'st_len': 7, 'st_mult': 4.972065322786524, 'adx_len': 22, 'adx_threshold': 19.674832160620525, 'trend_confirmations': 1, 'macd_fast': 7, 'macd_slow': 48, 'macd_signal': 10, 'rsi_len': 13, 'tp_atr_mult': 10.074278943816457}
2025-07-16 23:20:28,677 - root - WARNING - Trial 4 failed - ValueError: Invalid parameters sampled: {'ema_fast': 29, 'ema_slow': 64, 'atr_len': 18, 'vol_sma_len': 9, 'sl_atr_mult': 3.8835220546268197, 'trail_atr_mult': 2.0799382380278884, 'risk_multiplier': 2.6916759274468838, 'min_bars_between_trades': 2, 'use_volume_filter': False, 'st_len': 39, 'st_mult': 3.504770550671643, 'adx_len': 18, 'adx_threshold': 27.854036052073187, 'trend_confirmations': 1, 'macd_fast': 12, 'macd_slow': 39, 'macd_signal': 9, 'rsi_len': 17, 'tp_atr_mult': 14.447545897877998}
2025-07-16 23:20:28,722 - root - WARNING - Trial 5 failed - ValueError: Invalid parameters sampled: {'ema_fast': 18, 'ema_slow': 70, 'atr_len': 10, 'vol_sma_len': 23, 'sl_atr_mult': 2.7701246095729855, 'trail_atr_mult': 2.1598961106078143, 'risk_multiplier': 2.4780606571406567, 'min_bars_between_trades': 1, 'use_volume_filter': True, 'st_len': 17, 'st_mult': 3.612394565366482, 'adx_len': 16, 'adx_threshold': 24.614156317347234, 'trend_confirmations': 2, 'macd_fast': 11, 'macd_slow': 19, 'macd_signal': 15, 'rsi_len': 25, 'tp_atr_mult': 6.896789748284}
2025-07-16 23:20:28,774 - root - WARNING - Trial 6 failed - ValueError: Invalid parameters sampled: {'ema_fast': 12, 'ema_slow': 32, 'atr_len': 18, 'vol_sma_len': 34, 'sl_atr_mult': 3.5581766786125755, 'trail_atr_mult': 3.881853560849456, 'risk_multiplier': 3.5615805638049527, 'min_bars_between_trades': 2, 'use_volume_filter': False, 'st_len': 18, 'st_mult': 1.6997582476765278, 'adx_len': 18, 'adx_threshold': 29.530950312333356, 'trend_confirmations': 1, 'macd_fast': 20, 'macd_slow': 36, 'macd_signal': 5, 'rsi_len': 13, 'tp_atr_mult': 13.23565167487688}
2025-07-16 23:20:28,825 - root - WARNING - Trial 7 failed - ValueError: Invalid parameters sampled: {'ema_fast': 24, 'ema_slow': 79, 'atr_len': 21, 'vol_sma_len': 15, 'sl_atr_mult': 2.4978895675504775, 'trail_atr_mult': 1.5412856124625889, 'risk_multiplier': 1.537857403933073, 'min_bars_between_trades': 1, 'use_volume_filter': False, 'st_len': 30, 'st_mult': 4.163102681001211, 'adx_len': 12, 'adx_threshold': 21.247143217204847, 'trend_confirmations': 2, 'macd_fast': 9, 'macd_slow': 29, 'macd_signal': 8, 'rsi_len': 21, 'tp_atr_mult': 8.440848362903594}
2025-07-16 23:20:31,458 - root - WARNING - Trial 0 failed - ValueError: Invalid parameters sampled: {'ema_fast': 14, 'ema_slow': 97, 'atr_len': 21, 'vol_sma_len': 27, 'sl_atr_mult': 2.312037280884873, 'trail_atr_mult': 1.2991824650758486, 'risk_multiplier': 1.6452090304204987, 'min_bars_between_trades': 2, 'use_volume_filter': False, 'st_len': 5, 'st_mult': 4.89468448256698, 'adx_len': 22, 'adx_threshold': 20.308477766956905, 'trend_confirmations': 1, 'macd_fast': 8, 'macd_slow': 28, 'macd_signal': 10, 'rsi_len': 15, 'tp_atr_mult': 8.621062261782377}
2025-07-16 23:20:31,460 - root - WARNING - Trial 1 failed - ValueError: Invalid parameters sampled: {'ema_fast': 20, 'ema_slow': 31, 'atr_len': 13, 'vol_sma_len': 20, 'sl_atr_mult': 2.912139968434072, 'trail_atr_mult': 3.3125630764576437, 'risk_multiplier': 1.9991844553958993, 'min_bars_between_trades': 2, 'use_volume_filter': True, 'st_len': 26, 'st_mult': 2.0968344329055206, 'adx_len': 9, 'adx_threshold': 38.722138431333335, 'trend_confirmations': 2, 'macd_fast': 18, 'macd_slow': 28, 'macd_signal': 6, 'rsi_len': 20, 'tp_atr_mult': 9.961372443656412}
2025-07-16 23:20:31,509 - root - WARNING - Trial 2 failed - ValueError: Invalid parameters sampled: {'ema_fast': 6, 'ema_slow': 95, 'atr_len': 25, 'vol_sma_len': 38, 'sl_atr_mult': 2.0307354746562085, 'trail_atr_mult': 0.9239206398757867, 'risk_multiplier': 1.5378605927568922, 'min_bars_between_trades': 1, 'use_volume_filter': False, 'st_len': 5, 'st_mult': 4.973414494021188, 'adx_len': 25, 'adx_threshold': 15.714227196361005, 'trend_confirmations': 1, 'macd_fast': 6, 'macd_slow': 25, 'macd_signal': 13, 'rsi_len': 9, 'tp_atr_mult': 6.111631183842814}
2025-07-16 23:20:31,559 - root - WARNING - Trial 3 failed - ValueError: Invalid parameters sampled: {'ema_fast': 11, 'ema_slow': 98, 'atr_len': 22, 'vol_sma_len': 30, 'sl_atr_mult': 2.2639220099054067, 'trail_atr_mult': 1.2388143135812206, 'risk_multiplier': 3.719448856348819, 'min_bars_between_trades': 2, 'use_volume_filter': False, 'st_len': 7, 'st_mult': 4.972065322786524, 'adx_len': 22, 'adx_threshold': 19.674832160620525, 'trend_confirmations': 1, 'macd_fast': 7, 'macd_slow': 48, 'macd_signal': 10, 'rsi_len': 13, 'tp_atr_mult': 10.074278943816457}
2025-07-16 23:20:31,614 - root - WARNING - Trial 4 failed - ValueError: Invalid parameters sampled: {'ema_fast': 29, 'ema_slow': 64, 'atr_len': 18, 'vol_sma_len': 9, 'sl_atr_mult': 3.8835220546268197, 'trail_atr_mult': 2.0799382380278884, 'risk_multiplier': 2.6916759274468838, 'min_bars_between_trades': 2, 'use_volume_filter': False, 'st_len': 39, 'st_mult': 3.504770550671643, 'adx_len': 18, 'adx_threshold': 27.854036052073187, 'trend_confirmations': 1, 'macd_fast': 12, 'macd_slow': 39, 'macd_signal': 9, 'rsi_len': 17, 'tp_atr_mult': 14.447545897877998}
2025-07-16 23:20:31,683 - root - WARNING - Trial 5 failed - ValueError: Invalid parameters sampled: {'ema_fast': 18, 'ema_slow': 70, 'atr_len': 10, 'vol_sma_len': 23, 'sl_atr_mult': 2.7701246095729855, 'trail_atr_mult': 2.1598961106078143, 'risk_multiplier': 2.4780606571406567, 'min_bars_between_trades': 1, 'use_volume_filter': True, 'st_len': 17, 'st_mult': 3.612394565366482, 'adx_len': 16, 'adx_threshold': 24.614156317347234, 'trend_confirmations': 2, 'macd_fast': 11, 'macd_slow': 19, 'macd_signal': 15, 'rsi_len': 25, 'tp_atr_mult': 6.896789748284}
2025-07-16 23:20:31,740 - root - WARNING - Trial 6 failed - ValueError: Invalid parameters sampled: {'ema_fast': 12, 'ema_slow': 32, 'atr_len': 18, 'vol_sma_len': 34, 'sl_atr_mult': 3.5581766786125755, 'trail_atr_mult': 3.881853560849456, 'risk_multiplier': 3.5615805638049527, 'min_bars_between_trades': 2, 'use_volume_filter': False, 'st_len': 18, 'st_mult': 1.6997582476765278, 'adx_len': 18, 'adx_threshold': 29.530950312333356, 'trend_confirmations': 1, 'macd_fast': 20, 'macd_slow': 36, 'macd_signal': 5, 'rsi_len': 13, 'tp_atr_mult': 13.23565167487688}
2025-07-16 23:20:31,793 - root - WARNING - Trial 7 failed - ValueError: Invalid parameters sampled: {'ema_fast': 24, 'ema_slow': 79, 'atr_len': 21, 'vol_sma_len': 15, 'sl_atr_mult': 2.4978895675504775, 'trail_atr_mult': 1.5412856124625889, 'risk_multiplier': 1.537857403933073, 'min_bars_between_trades': 1, 'use_volume_filter': False, 'st_len': 30, 'st_mult': 4.163102681001211, 'adx_len': 12, 'adx_threshold': 21.247143217204847, 'trend_confirmations': 2, 'macd_fast': 9, 'macd_slow': 29, 'macd_signal': 8, 'rsi_len': 21, 'tp_atr_mult': 8.440848362903594}
2025-07-16 23:20:31,846 - root - WARNING - Trial 0 failed - ValueError: Invalid parameters sampled: {'ema_fast': 14, 'ema_slow': 97, 'atr_len': 21, 'vol_sma_len': 27, 'sl_atr_mult': 2.312037280884873, 'trail_atr_mult': 1.2991824650758486, 'risk_multiplier': 1.6452090304204987, 'min_bars_between_trades': 2, 'use_volume_filter': False, 'st_len': 5, 'st_mult': 4.89468448256698, 'adx_len': 22, 'adx_threshold': 20.308477766956905, 'trend_confirmations': 1, 'macd_fast': 8, 'macd_slow': 28, 'macd_signal': 10, 'rsi_len': 15, 'tp_atr_mult': 8.621062261782377}
2025-07-16 23:20:31,850 - root - WARNING - Trial 1 failed - ValueError: Invalid parameters sampled: {'ema_fast': 20, 'ema_slow': 31, 'atr_len': 13, 'vol_sma_len': 20, 'sl_atr_mult': 2.912139968434072, 'trail_atr_mult': 3.3125630764576437, 'risk_multiplier': 1.9991844553958993, 'min_bars_between_trades': 2, 'use_volume_filter': True, 'st_len': 26, 'st_mult': 2.0968344329055206, 'adx_len': 9, 'adx_threshold': 38.722138431333335, 'trend_confirmations': 2, 'macd_fast': 18, 'macd_slow': 28, 'macd_signal': 6, 'rsi_len': 20, 'tp_atr_mult': 9.961372443656412}
2025-07-16 23:20:31,916 - root - WARNING - Trial 2 failed - ValueError: Invalid parameters sampled: {'ema_fast': 6, 'ema_slow': 95, 'atr_len': 25, 'vol_sma_len': 38, 'sl_atr_mult': 2.0307354746562085, 'trail_atr_mult': 0.9239206398757867, 'risk_multiplier': 1.5378605927568922, 'min_bars_between_trades': 1, 'use_volume_filter': False, 'st_len': 5, 'st_mult': 4.973414494021188, 'adx_len': 25, 'adx_threshold': 15.714227196361005, 'trend_confirmations': 1, 'macd_fast': 6, 'macd_slow': 25, 'macd_signal': 13, 'rsi_len': 9, 'tp_atr_mult': 6.111631183842814}
2025-07-16 23:20:31,963 - root - WARNING - Trial 3 failed - ValueError: Invalid parameters sampled: {'ema_fast': 11, 'ema_slow': 98, 'atr_len': 22, 'vol_sma_len': 30, 'sl_atr_mult': 2.2639220099054067, 'trail_atr_mult': 1.2388143135812206, 'risk_multiplier': 3.719448856348819, 'min_bars_between_trades': 2, 'use_volume_filter': False, 'st_len': 7, 'st_mult': 4.972065322786524, 'adx_len': 22, 'adx_threshold': 19.674832160620525, 'trend_confirmations': 1, 'macd_fast': 7, 'macd_slow': 48, 'macd_signal': 10, 'rsi_len': 13, 'tp_atr_mult': 10.074278943816457}
2025-07-16 23:20:32,010 - root - WARNING - Trial 4 failed - ValueError: Invalid parameters sampled: {'ema_fast': 29, 'ema_slow': 64, 'atr_len': 18, 'vol_sma_len': 9, 'sl_atr_mult': 3.8835220546268197, 'trail_atr_mult': 2.0799382380278884, 'risk_multiplier': 2.6916759274468838, 'min_bars_between_trades': 2, 'use_volume_filter': False, 'st_len': 39, 'st_mult': 3.504770550671643, 'adx_len': 18, 'adx_threshold': 27.854036052073187, 'trend_confirmations': 1, 'macd_fast': 12, 'macd_slow': 39, 'macd_signal': 9, 'rsi_len': 17, 'tp_atr_mult': 14.447545897877998}
2025-07-16 23:20:32,059 - root - WARNING - Trial 5 failed - ValueError: Invalid parameters sampled: {'ema_fast': 18, 'ema_slow': 70, 'atr_len': 10, 'vol_sma_len': 23, 'sl_atr_mult': 2.7701246095729855, 'trail_atr_mult': 2.1598961106078143, 'risk_multiplier': 2.4780606571406567, 'min_bars_between_trades': 1, 'use_volume_filter': True, 'st_len': 17, 'st_mult': 3.612394565366482, 'adx_len': 16, 'adx_threshold': 24.614156317347234, 'trend_confirmations': 2, 'macd_fast': 11, 'macd_slow': 19, 'macd_signal': 15, 'rsi_len': 25, 'tp_atr_mult': 6.896789748284}
2025-07-16 23:20:32,109 - root - WARNING - Trial 6 failed - ValueError: Invalid parameters sampled: {'ema_fast': 12, 'ema_slow': 32, 'atr_len': 18, 'vol_sma_len': 34, 'sl_atr_mult': 3.5581766786125755, 'trail_atr_mult': 3.881853560849456, 'risk_multiplier': 3.5615805638049527, 'min_bars_between_trades': 2, 'use_volume_filter': False, 'st_len': 18, 'st_mult': 1.6997582476765278, 'adx_len': 18, 'adx_threshold': 29.530950312333356, 'trend_confirmations': 1, 'macd_fast': 20, 'macd_slow': 36, 'macd_signal': 5, 'rsi_len': 13, 'tp_atr_mult': 13.23565167487688}
2025-07-16 23:20:32,171 - root - WARNING - Trial 7 failed - ValueError: Invalid parameters sampled: {'ema_fast': 24, 'ema_slow': 79, 'atr_len': 21, 'vol_sma_len': 15, 'sl_atr_mult': 2.4978895675504775, 'trail_atr_mult': 1.5412856124625889, 'risk_multiplier': 1.537857403933073, 'min_bars_between_trades': 1, 'use_volume_filter': False, 'st_len': 30, 'st_mult': 4.163102681001211, 'adx_len': 12, 'adx_threshold': 21.247143217204847, 'trend_confirmations': 2, 'macd_fast': 9, 'macd_slow': 29, 'macd_signal': 8, 'rsi_len': 21, 'tp_atr_mult': 8.440848362903594}
2025-07-16 23:20:32,220 - root - WARNING - Trial 0 failed - ValueError: Invalid parameters sampled: {'ema_fast': 14, 'ema_slow': 97, 'atr_len': 21, 'vol_sma_len': 27, 'sl_atr_mult': 2.312037280884873, 'trail_atr_mult': 1.2991824650758486, 'risk_multiplier': 1.6452090304204987, 'min_bars_between_trades': 2, 'use_volume_filter': False, 'st_len': 5, 'st_mult': 4.89468448256698, 'adx_len': 22, 'adx_threshold': 20.308477766956905, 'trend_confirmations': 1, 'macd_fast': 8, 'macd_slow': 28, 'macd_signal': 10, 'rsi_len': 15, 'tp_atr_mult': 8.621062261782377}
2025-07-16 23:20:32,222 - root - WARNING - Trial 1 failed - ValueError: Invalid parameters sampled: {'ema_fast': 20, 'ema_slow': 31, 'atr_len': 13, 'vol_sma_len': 20, 'sl_atr_mult': 2.912139968434072, 'trail_atr_mult': 3.3125630764576437, 'risk_multiplier': 1.9991844553958993, 'min_bars_between_trades': 2, 'use_volume_filter': True, 'st_len': 26, 'st_mult': 2.0968344329055206, 'adx_len': 9, 'adx_threshold': 38.722138431333335, 'trend_confirmations': 2, 'macd_fast': 18, 'macd_slow': 28, 'macd_signal': 6, 'rsi_len': 20, 'tp_atr_mult': 9.961372443656412}
2025-07-16 23:20:32,265 - root - WARNING - Trial 2 failed - ValueError: Invalid parameters sampled: {'ema_fast': 6, 'ema_slow': 95, 'atr_len': 25, 'vol_sma_len': 38, 'sl_atr_mult': 2.0307354746562085, 'trail_atr_mult': 0.9239206398757867, 'risk_multiplier': 1.5378605927568922, 'min_bars_between_trades': 1, 'use_volume_filter': False, 'st_len': 5, 'st_mult': 4.973414494021188, 'adx_len': 25, 'adx_threshold': 15.714227196361005, 'trend_confirmations': 1, 'macd_fast': 6, 'macd_slow': 25, 'macd_signal': 13, 'rsi_len': 9, 'tp_atr_mult': 6.111631183842814}
2025-07-16 23:20:32,311 - root - WARNING - Trial 3 failed - ValueError: Invalid parameters sampled: {'ema_fast': 11, 'ema_slow': 98, 'atr_len': 22, 'vol_sma_len': 30, 'sl_atr_mult': 2.2639220099054067, 'trail_atr_mult': 1.2388143135812206, 'risk_multiplier': 3.719448856348819, 'min_bars_between_trades': 2, 'use_volume_filter': False, 'st_len': 7, 'st_mult': 4.972065322786524, 'adx_len': 22, 'adx_threshold': 19.674832160620525, 'trend_confirmations': 1, 'macd_fast': 7, 'macd_slow': 48, 'macd_signal': 10, 'rsi_len': 13, 'tp_atr_mult': 10.074278943816457}
2025-07-16 23:20:32,357 - root - WARNING - Trial 4 failed - ValueError: Invalid parameters sampled: {'ema_fast': 29, 'ema_slow': 64, 'atr_len': 18, 'vol_sma_len': 9, 'sl_atr_mult': 3.8835220546268197, 'trail_atr_mult': 2.0799382380278884, 'risk_multiplier': 2.6916759274468838, 'min_bars_between_trades': 2, 'use_volume_filter': False, 'st_len': 39, 'st_mult': 3.504770550671643, 'adx_len': 18, 'adx_threshold': 27.854036052073187, 'trend_confirmations': 1, 'macd_fast': 12, 'macd_slow': 39, 'macd_signal': 9, 'rsi_len': 17, 'tp_atr_mult': 14.447545897877998}
2025-07-16 23:20:32,415 - root - WARNING - Trial 5 failed - ValueError: Invalid parameters sampled: {'ema_fast': 18, 'ema_slow': 70, 'atr_len': 10, 'vol_sma_len': 23, 'sl_atr_mult': 2.7701246095729855, 'trail_atr_mult': 2.1598961106078143, 'risk_multiplier': 2.4780606571406567, 'min_bars_between_trades': 1, 'use_volume_filter': True, 'st_len': 17, 'st_mult': 3.612394565366482, 'adx_len': 16, 'adx_threshold': 24.614156317347234, 'trend_confirmations': 2, 'macd_fast': 11, 'macd_slow': 19, 'macd_signal': 15, 'rsi_len': 25, 'tp_atr_mult': 6.896789748284}
2025-07-16 23:20:32,474 - root - WARNING - Trial 6 failed - ValueError: Invalid parameters sampled: {'ema_fast': 12, 'ema_slow': 32, 'atr_len': 18, 'vol_sma_len': 34, 'sl_atr_mult': 3.5581766786125755, 'trail_atr_mult': 3.881853560849456, 'risk_multiplier': 3.5615805638049527, 'min_bars_between_trades': 2, 'use_volume_filter': False, 'st_len': 18, 'st_mult': 1.6997582476765278, 'adx_len': 18, 'adx_threshold': 29.530950312333356, 'trend_confirmations': 1, 'macd_fast': 20, 'macd_slow': 36, 'macd_signal': 5, 'rsi_len': 13, 'tp_atr_mult': 13.23565167487688}
2025-07-16 23:20:32,528 - root - WARNING - Trial 7 failed - ValueError: Invalid parameters sampled: {'ema_fast': 24, 'ema_slow': 79, 'atr_len': 21, 'vol_sma_len': 15, 'sl_atr_mult': 2.4978895675504775, 'trail_atr_mult': 1.5412856124625889, 'risk_multiplier': 1.537857403933073, 'min_bars_between_trades': 1, 'use_volume_filter': False, 'st_len': 30, 'st_mult': 4.163102681001211, 'adx_len': 12, 'adx_threshold': 21.247143217204847, 'trend_confirmations': 2, 'macd_fast': 9, 'macd_slow': 29, 'macd_signal': 8, 'rsi_len': 21, 'tp_atr_mult': 8.440848362903594}
2025-07-16 23:20:32,579 - root - WARNING - Trial 0 failed - ValueError: Invalid parameters sampled: {'ema_fast': 14, 'ema_slow': 97, 'atr_len': 21, 'vol_sma_len': 27, 'sl_atr_mult': 2.312037280884873, 'trail_atr_mult': 1.2991824650758486, 'risk_multiplier': 1.6452090304204987, 'min_bars_between_trades': 2, 'use_volume_filter': False, 'st_len': 5, 'st_mult': 4.89468448256698, 'adx_len': 22, 'adx_threshold': 20.308477766956905, 'trend_confirmations': 1, 'macd_fast': 8, 'macd_slow': 28, 'macd_signal': 10, 'rsi_len': 15, 'tp_atr_mult': 8.621062261782377}
2025-07-16 23:20:32,581 - root - WARNING - Trial 1 failed - ValueError: Invalid parameters sampled: {'ema_fast': 20, 'ema_slow': 31, 'atr_len': 13, 'vol_sma_len': 20, 'sl_atr_mult': 2.912139968434072, 'trail_atr_mult': 3.3125630764576437, 'risk_multiplier': 1.9991844553958993, 'min_bars_between_trades': 2, 'use_volume_filter': True, 'st_len': 26, 'st_mult': 2.0968344329055206, 'adx_len': 9, 'adx_threshold': 38.722138431333335, 'trend_confirmations': 2, 'macd_fast': 18, 'macd_slow': 28, 'macd_signal': 6, 'rsi_len': 20, 'tp_atr_mult': 9.961372443656412}
2025-07-16 23:20:32,624 - root - WARNING - Trial 2 failed - ValueError: Invalid parameters sampled: {'ema_fast': 6, 'ema_slow': 95, 'atr_len': 25, 'vol_sma_len': 38, 'sl_atr_mult': 2.0307354746562085, 'trail_atr_mult': 0.9239206398757867, 'risk_multiplier': 1.5378605927568922, 'min_bars_between_trades': 1, 'use_volume_filter': False, 'st_len': 5, 'st_mult': 4.973414494021188, 'adx_len': 25, 'adx_threshold': 15.714227196361005, 'trend_confirmations': 1, 'macd_fast': 6, 'macd_slow': 25, 'macd_signal': 13, 'rsi_len': 9, 'tp_atr_mult': 6.111631183842814}
2025-07-16 23:20:32,679 - root - WARNING - Trial 3 failed - ValueError: Invalid parameters sampled: {'ema_fast': 11, 'ema_slow': 98, 'atr_len': 22, 'vol_sma_len': 30, 'sl_atr_mult': 2.2639220099054067, 'trail_atr_mult': 1.2388143135812206, 'risk_multiplier': 3.719448856348819, 'min_bars_between_trades': 2, 'use_volume_filter': False, 'st_len': 7, 'st_mult': 4.972065322786524, 'adx_len': 22, 'adx_threshold': 19.674832160620525, 'trend_confirmations': 1, 'macd_fast': 7, 'macd_slow': 48, 'macd_signal': 10, 'rsi_len': 13, 'tp_atr_mult': 10.074278943816457}
2025-07-16 23:20:32,726 - root - WARNING - Trial 4 failed - ValueError: Invalid parameters sampled: {'ema_fast': 29, 'ema_slow': 64, 'atr_len': 18, 'vol_sma_len': 9, 'sl_atr_mult': 3.8835220546268197, 'trail_atr_mult': 2.0799382380278884, 'risk_multiplier': 2.6916759274468838, 'min_bars_between_trades': 2, 'use_volume_filter': False, 'st_len': 39, 'st_mult': 3.504770550671643, 'adx_len': 18, 'adx_threshold': 27.854036052073187, 'trend_confirmations': 1, 'macd_fast': 12, 'macd_slow': 39, 'macd_signal': 9, 'rsi_len': 17, 'tp_atr_mult': 14.447545897877998}
2025-07-16 23:20:32,774 - root - WARNING - Trial 5 failed - ValueError: Invalid parameters sampled: {'ema_fast': 18, 'ema_slow': 70, 'atr_len': 10, 'vol_sma_len': 23, 'sl_atr_mult': 2.7701246095729855, 'trail_atr_mult': 2.1598961106078143, 'risk_multiplier': 2.4780606571406567, 'min_bars_between_trades': 1, 'use_volume_filter': True, 'st_len': 17, 'st_mult': 3.612394565366482, 'adx_len': 16, 'adx_threshold': 24.614156317347234, 'trend_confirmations': 2, 'macd_fast': 11, 'macd_slow': 19, 'macd_signal': 15, 'rsi_len': 25, 'tp_atr_mult': 6.896789748284}
2025-07-16 23:20:32,825 - root - WARNING - Trial 6 failed - ValueError: Invalid parameters sampled: {'ema_fast': 12, 'ema_slow': 32, 'atr_len': 18, 'vol_sma_len': 34, 'sl_atr_mult': 3.5581766786125755, 'trail_atr_mult': 3.881853560849456, 'risk_multiplier': 3.5615805638049527, 'min_bars_between_trades': 2, 'use_volume_filter': False, 'st_len': 18, 'st_mult': 1.6997582476765278, 'adx_len': 18, 'adx_threshold': 29.530950312333356, 'trend_confirmations': 1, 'macd_fast': 20, 'macd_slow': 36, 'macd_signal': 5, 'rsi_len': 13, 'tp_atr_mult': 13.23565167487688}
2025-07-16 23:20:32,873 - root - WARNING - Trial 7 failed - ValueError: Invalid parameters sampled: {'ema_fast': 24, 'ema_slow': 79, 'atr_len': 21, 'vol_sma_len': 15, 'sl_atr_mult': 2.4978895675504775, 'trail_atr_mult': 1.5412856124625889, 'risk_multiplier': 1.537857403933073, 'min_bars_between_trades': 1, 'use_volume_filter': False, 'st_len': 30, 'st_mult': 4.163102681001211, 'adx_len': 12, 'adx_threshold': 21.247143217204847, 'trend_confirmations': 2, 'macd_fast': 9, 'macd_slow': 29, 'macd_signal': 8, 'rsi_len': 21, 'tp_atr_mult': 8.440848362903594}
2025-07-16 23:20:32,926 - root - WARNING - Trial 0 failed - ValueError: Invalid parameters sampled: {'ema_fast': 14, 'ema_slow': 97, 'atr_len': 21, 'vol_sma_len': 27, 'sl_atr_mult': 2.312037280884873, 'trail_atr_mult': 1.2991824650758486, 'risk_multiplier': 1.6452090304204987, 'min_bars_between_trades': 2, 'use_volume_filter': False, 'st_len': 5, 'st_mult': 4.89468448256698, 'adx_len': 22, 'adx_threshold': 20.308477766956905, 'trend_confirmations': 1, 'macd_fast': 8, 'macd_slow': 28, 'macd_signal': 10, 'rsi_len': 15, 'tp_atr_mult': 8.621062261782377}
2025-07-16 23:20:32,928 - root - WARNING - Trial 1 failed - ValueError: Invalid parameters sampled: {'ema_fast': 20, 'ema_slow': 31, 'atr_len': 13, 'vol_sma_len': 20, 'sl_atr_mult': 2.912139968434072, 'trail_atr_mult': 3.3125630764576437, 'risk_multiplier': 1.9991844553958993, 'min_bars_between_trades': 2, 'use_volume_filter': True, 'st_len': 26, 'st_mult': 2.0968344329055206, 'adx_len': 9, 'adx_threshold': 38.722138431333335, 'trend_confirmations': 2, 'macd_fast': 18, 'macd_slow': 28, 'macd_signal': 6, 'rsi_len': 20, 'tp_atr_mult': 9.961372443656412}
2025-07-16 23:20:32,974 - root - WARNING - Trial 2 failed - ValueError: Invalid parameters sampled: {'ema_fast': 6, 'ema_slow': 95, 'atr_len': 25, 'vol_sma_len': 38, 'sl_atr_mult': 2.0307354746562085, 'trail_atr_mult': 0.9239206398757867, 'risk_multiplier': 1.5378605927568922, 'min_bars_between_trades': 1, 'use_volume_filter': False, 'st_len': 5, 'st_mult': 4.973414494021188, 'adx_len': 25, 'adx_threshold': 15.714227196361005, 'trend_confirmations': 1, 'macd_fast': 6, 'macd_slow': 25, 'macd_signal': 13, 'rsi_len': 9, 'tp_atr_mult': 6.111631183842814}
2025-07-16 23:20:33,018 - root - WARNING - Trial 3 failed - ValueError: Invalid parameters sampled: {'ema_fast': 11, 'ema_slow': 98, 'atr_len': 22, 'vol_sma_len': 30, 'sl_atr_mult': 2.2639220099054067, 'trail_atr_mult': 1.2388143135812206, 'risk_multiplier': 3.719448856348819, 'min_bars_between_trades': 2, 'use_volume_filter': False, 'st_len': 7, 'st_mult': 4.972065322786524, 'adx_len': 22, 'adx_threshold': 19.674832160620525, 'trend_confirmations': 1, 'macd_fast': 7, 'macd_slow': 48, 'macd_signal': 10, 'rsi_len': 13, 'tp_atr_mult': 10.074278943816457}
2025-07-16 23:20:33,064 - root - WARNING - Trial 4 failed - ValueError: Invalid parameters sampled: {'ema_fast': 29, 'ema_slow': 64, 'atr_len': 18, 'vol_sma_len': 9, 'sl_atr_mult': 3.8835220546268197, 'trail_atr_mult': 2.0799382380278884, 'risk_multiplier': 2.6916759274468838, 'min_bars_between_trades': 2, 'use_volume_filter': False, 'st_len': 39, 'st_mult': 3.504770550671643, 'adx_len': 18, 'adx_threshold': 27.854036052073187, 'trend_confirmations': 1, 'macd_fast': 12, 'macd_slow': 39, 'macd_signal': 9, 'rsi_len': 17, 'tp_atr_mult': 14.447545897877998}
2025-07-16 23:20:33,111 - root - WARNING - Trial 5 failed - ValueError: Invalid parameters sampled: {'ema_fast': 18, 'ema_slow': 70, 'atr_len': 10, 'vol_sma_len': 23, 'sl_atr_mult': 2.7701246095729855, 'trail_atr_mult': 2.1598961106078143, 'risk_multiplier': 2.4780606571406567, 'min_bars_between_trades': 1, 'use_volume_filter': True, 'st_len': 17, 'st_mult': 3.612394565366482, 'adx_len': 16, 'adx_threshold': 24.614156317347234, 'trend_confirmations': 2, 'macd_fast': 11, 'macd_slow': 19, 'macd_signal': 15, 'rsi_len': 25, 'tp_atr_mult': 6.896789748284}
2025-07-16 23:20:33,163 - root - WARNING - Trial 6 failed - ValueError: Invalid parameters sampled: {'ema_fast': 12, 'ema_slow': 32, 'atr_len': 18, 'vol_sma_len': 34, 'sl_atr_mult': 3.5581766786125755, 'trail_atr_mult': 3.881853560849456, 'risk_multiplier': 3.5615805638049527, 'min_bars_between_trades': 2, 'use_volume_filter': False, 'st_len': 18, 'st_mult': 1.6997582476765278, 'adx_len': 18, 'adx_threshold': 29.530950312333356, 'trend_confirmations': 1, 'macd_fast': 20, 'macd_slow': 36, 'macd_signal': 5, 'rsi_len': 13, 'tp_atr_mult': 13.23565167487688}
2025-07-16 23:20:33,222 - root - WARNING - Trial 7 failed - ValueError: Invalid parameters sampled: {'ema_fast': 24, 'ema_slow': 79, 'atr_len': 21, 'vol_sma_len': 15, 'sl_atr_mult': 2.4978895675504775, 'trail_atr_mult': 1.5412856124625889, 'risk_multiplier': 1.537857403933073, 'min_bars_between_trades': 1, 'use_volume_filter': False, 'st_len': 30, 'st_mult': 4.163102681001211, 'adx_len': 12, 'adx_threshold': 21.247143217204847, 'trend_confirmations': 2, 'macd_fast': 9, 'macd_slow': 29, 'macd_signal': 8, 'rsi_len': 21, 'tp_atr_mult': 8.440848362903594}
2025-07-16 23:20:33,267 - __main__ - INFO - BTCUSDT Trend: 6 valid WF windows, WF score = 0.600
2025-07-16 23:20:33,267 - __main__ - INFO - Optimizing Reversion strategy for BTCUSDT
2025-07-16 23:20:33,270 - root - WARNING - Trial 0 failed - ValueError: Invalid parameters sampled: {'ema_fast': 14, 'ema_slow': 97, 'atr_len': 21, 'vol_sma_len': 27, 'sl_atr_mult': 2.312037280884873, 'trail_atr_mult': 1.2991824650758486, 'risk_multiplier': 1.6452090304204987, 'min_bars_between_trades': 2, 'use_volume_filter': False, 'bb_len': 10, 'bb_std': 3.430792659972587, 'bb_oversold': 0.211461807384097, 'bb_overbought': 0.7988379954560035, 'mfi_len': 12, 'rsi_len': 11, 'rsi_oversold': 22.60605607398844, 'rsi_overbought': 73.11891079080594, 'stoch_k': 15, 'stoch_d': 5, 'willr_len': 22, 'cci_len': 12, 'tp_atr_mult': 8.629301836816964}
2025-07-16 23:20:33,272 - root - WARNING - Trial 1 failed - ValueError: Invalid parameters sampled: {'ema_fast': 14, 'ema_slow': 56, 'atr_len': 22, 'vol_sma_len': 14, 'sl_atr_mult': 3.0284688768272234, 'trail_atr_mult': 2.6957266203585357, 'risk_multiplier': 1.6161260317999944, 'min_bars_between_trades': 2, 'use_volume_filter': True, 'bb_len': 39, 'bb_std': 3.4209536760714867, 'bb_oversold': 0.20593139006678607, 'bb_overbought': 0.8200611669098753, 'mfi_len': 10, 'rsi_len': 20, 'rsi_oversold': 26.003812343490033, 'rsi_overbought': 63.05095587111947, 'stoch_k': 16, 'stoch_d': 3, 'willr_len': 28, 'cci_len': 15, 'tp_atr_mult': 11.962700559185837}
2025-07-16 23:20:33,274 - root - WARNING - Trial 2 failed - ValueError: Invalid parameters sampled: {'ema_fast': 13, 'ema_slow': 62, 'atr_len': 17, 'vol_sma_len': 14, 'sl_atr_mult': 3.939169255529117, 'trail_atr_mult': 3.2804250347555666, 'risk_multiplier': 3.848747353910473, 'min_bars_between_trades': 2, 'use_volume_filter': False, 'bb_len': 12, 'bb_std': 1.650760583564034, 'bb_oversold': 0.030402276449423754, 'bb_overbought': 0.8248259760755507, 'mfi_len': 16, 'rsi_len': 12, 'rsi_oversold': 35.718437728798236, 'rsi_overbought': 68.91883316733973, 'stoch_k': 13, 'stoch_d': 7, 'willr_len': 11, 'cci_len': 26, 'tp_atr_mult': 6.670955793117938}
2025-07-16 23:20:33,276 - root - WARNING - Trial 3 failed - ValueError: Invalid parameters sampled: {'ema_fast': 30, 'ema_slow': 82, 'atr_len': 11, 'vol_sma_len': 8, 'sl_atr_mult': 3.6309228569096685, 'trail_atr_mult': 3.061943500312375, 'risk_multiplier': 3.3225179201024684, 'min_bars_between_trades': 2, 'use_volume_filter': False, 'bb_len': 13, 'bb_std': 3.185137879513865, 'bb_oversold': 0.1633585691703383, 'bb_overbought': 0.8261065457161093, 'mfi_len': 9, 'rsi_len': 13, 'rsi_oversold': 23.12958305066868, 'rsi_overbought': 78.2401544584516, 'stoch_k': 19, 'stoch_d': 10, 'willr_len': 18, 'cci_len': 12, 'tp_atr_mult': 12.419203085006956}
2025-07-16 23:20:33,278 - root - WARNING - Trial 4 failed - ValueError: Invalid parameters sampled: {'ema_fast': 24, 'ema_slow': 65, 'atr_len': 21, 'vol_sma_len': 24, 'sl_atr_mult': 3.0454656587639883, 'trail_atr_mult': 2.168131258747359, 'risk_multiplier': 1.563547816860238, 'min_bars_between_trades': 1, 'use_volume_filter': False, 'bb_len': 19, 'bb_std': 2.3697125896788163, 'bb_oversold': 0.2287402890030014, 'bb_overbought': 0.8073372127042412, 'mfi_len': 17, 'rsi_len': 21, 'rsi_oversold': 20.71995413729056, 'rsi_overbought': 61.924497745719826, 'stoch_k': 13, 'stoch_d': 4, 'willr_len': 29, 'cci_len': 26, 'tp_atr_mult': 11.70063380859381}
2025-07-16 23:20:33,280 - root - WARNING - Trial 5 failed - ValueError: Invalid parameters sampled: {'ema_fast': 27, 'ema_slow': 85, 'atr_len': 11, 'vol_sma_len': 37, 'sl_atr_mult': 3.078684483831301, 'trail_atr_mult': 3.383808496525, 'risk_multiplier': 3.740228249808733, 'min_bars_between_trades': 1, 'use_volume_filter': False, 'bb_len': 23, 'bb_std': 3.081433961621734, 'bb_oversold': 0.217968034148959, 'bb_overbought': 0.7515989900221739, 'mfi_len': 19, 'rsi_len': 15, 'rsi_oversold': 20.552695261768257, 'rsi_overbought': 62.99663418334207, 'stoch_k': 14, 'stoch_d': 10, 'willr_len': 15, 'cci_len': 20, 'tp_atr_mult': 12.3271706300566}
2025-07-16 23:20:33,281 - root - WARNING - Trial 6 failed - ValueError: Invalid parameters sampled: {'ema_fast': 14, 'ema_slow': 98, 'atr_len': 25, 'vol_sma_len': 16, 'sl_atr_mult': 2.994497011784771, 'trail_atr_mult': 1.762810591413663, 'risk_multiplier': 2.212101235943669, 'min_bars_between_trades': 1, 'use_volume_filter': True, 'bb_len': 11, 'bb_std': 1.840886867744206, 'bb_oversold': 0.22890115377233036, 'bb_overbought': 0.8050992348534036, 'mfi_len': 11, 'rsi_len': 16, 'rsi_oversold': 39.641261352765014, 'rsi_overbought': 66.0513817877875, 'stoch_k': 20, 'stoch_d': 9, 'willr_len': 13, 'cci_len': 25, 'tp_atr_mult': 9.310048194473278}
2025-07-16 23:20:33,283 - root - WARNING - Trial 7 failed - ValueError: Invalid parameters sampled: {'ema_fast': 21, 'ema_slow': 71, 'atr_len': 17, 'vol_sma_len': 10, 'sl_atr_mult': 3.670604991178476, 'trail_atr_mult': 1.8264962079095548, 'risk_multiplier': 1.9662962759996356, 'min_bars_between_trades': 1, 'use_volume_filter': False, 'bb_len': 10, 'bb_std': 2.3778140340883462, 'bb_oversold': 0.07209402829552573, 'bb_overbought': 0.8983897417941735, 'mfi_len': 12, 'rsi_len': 20, 'rsi_oversold': 24.668383657513434, 'rsi_overbought': 83.41824971841837, 'stoch_k': 10, 'stoch_d': 5, 'willr_len': 10, 'cci_len': 29, 'tp_atr_mult': 13.896054180428829}
2025-07-16 23:20:33,286 - root - WARNING - Trial 8 failed - ValueError: Invalid parameters sampled: {'ema_fast': 11, 'ema_slow': 73, 'atr_len': 22, 'vol_sma_len': 26, 'sl_atr_mult': 3.0593011567120127, 'trail_atr_mult': 1.5739273308814454, 'risk_multiplier': 1.732756919514748, 'min_bars_between_trades': 2, 'use_volume_filter': True, 'bb_len': 20, 'bb_std': 2.00318202160912, 'bb_oversold': 0.18696980614015504, 'bb_overbought': 0.9563353597890927, 'mfi_len': 28, 'rsi_len': 22, 'rsi_oversold': 31.050791153857194, 'rsi_overbought': 62.10349912487622, 'stoch_k': 10, 'stoch_d': 10, 'willr_len': 21, 'cci_len': 10, 'tp_atr_mult': 6.913243885794289}
2025-07-16 23:20:33,289 - root - WARNING - Trial 9 failed - ValueError: Invalid parameters sampled: {'ema_fast': 22, 'ema_slow': 20, 'atr_len': 10, 'vol_sma_len': 26, 'sl_atr_mult': 3.3837903953853865, 'trail_atr_mult': 2.8862760304083217, 'risk_multiplier': 2.0606732736513997, 'min_bars_between_trades': 2, 'use_volume_filter': False, 'bb_len': 33, 'bb_std': 2.6941556678085936, 'bb_oversold': 0.21532138441366092, 'bb_overbought': 0.901250965229079, 'mfi_len': 21, 'rsi_len': 9, 'rsi_oversold': 24.19289507648584, 'rsi_overbought': 66.63005919204313, 'stoch_k': 12, 'stoch_d': 10, 'willr_len': 17, 'cci_len': 28, 'tp_atr_mult': 11.680247633975366}
2025-07-16 23:20:33,291 - root - WARNING - Trial 10 failed - ValueError: Invalid parameters sampled: {'ema_fast': 25, 'ema_slow': 60, 'atr_len': 18, 'vol_sma_len': 24, 'sl_atr_mult': 2.390485975596089, 'trail_atr_mult': 3.111846768836817, 'risk_multiplier': 2.2019309061021395, 'min_bars_between_trades': 1, 'use_volume_filter': True, 'bb_len': 39, 'bb_std': 3.394035727105951, 'bb_oversold': 0.23041880975070317, 'bb_overbought': 0.8351365010587521, 'mfi_len': 8, 'rsi_len': 24, 'rsi_oversold': 25.704603707932858, 'rsi_overbought': 84.16637047609174, 'stoch_k': 25, 'stoch_d': 9, 'willr_len': 14, 'cci_len': 18, 'tp_atr_mult': 13.660230043651712}
2025-07-16 23:20:33,293 - root - WARNING - Trial 11 failed - ValueError: Invalid parameters sampled: {'ema_fast': 13, 'ema_slow': 33, 'atr_len': 18, 'vol_sma_len': 38, 'sl_atr_mult': 3.392059593349946, 'trail_atr_mult': 2.6241957442859682, 'risk_multiplier': 1.7429412344269213, 'min_bars_between_trades': 2, 'use_volume_filter': True, 'bb_len': 26, 'bb_std': 3.2179580654342974, 'bb_oversold': 0.190376782083467, 'bb_overbought': 0.9103136204289116, 'mfi_len': 24, 'rsi_len': 14, 'rsi_oversold': 22.339796106612333, 'rsi_overbought': 80.23402888696285, 'stoch_k': 22, 'stoch_d': 9, 'willr_len': 29, 'cci_len': 20, 'tp_atr_mult': 10.513646652184796}
2025-07-16 23:20:33,295 - root - WARNING - Trial 12 failed - ValueError: Invalid parameters sampled: {'ema_fast': 25, 'ema_slow': 72, 'atr_len': 20, 'vol_sma_len': 34, 'sl_atr_mult': 3.7800106836351324, 'trail_atr_mult': 1.8815845019249147, 'risk_multiplier': 2.43895738159986, 'min_bars_between_trades': 1, 'use_volume_filter': True, 'bb_len': 24, 'bb_std': 2.448082659827426, 'bb_oversold': 0.08590448798950542, 'bb_overbought': 0.8858916499308724, 'mfi_len': 8, 'rsi_len': 8, 'rsi_oversold': 35.56501401649146, 'rsi_overbought': 69.00476603528158, 'stoch_k': 10, 'stoch_d': 7, 'willr_len': 25, 'cci_len': 14, 'tp_atr_mult': 11.606014282371003}
2025-07-16 23:20:33,297 - root - WARNING - Trial 13 failed - ValueError: Invalid parameters sampled: {'ema_fast': 7, 'ema_slow': 24, 'atr_len': 17, 'vol_sma_len': 25, 'sl_atr_mult': 3.274859802996413, 'trail_atr_mult': 3.1234922679125168, 'risk_multiplier': 3.9396301986563365, 'min_bars_between_trades': 2, 'use_volume_filter': False, 'bb_len': 18, 'bb_std': 2.209634267622963, 'bb_oversold': 0.03804496770872117, 'bb_overbought': 0.7558306709855552, 'mfi_len': 30, 'rsi_len': 23, 'rsi_oversold': 32.39935515234245, 'rsi_overbought': 70.22382361035675, 'stoch_k': 11, 'stoch_d': 4, 'willr_len': 13, 'cci_len': 21, 'tp_atr_mult': 12.431363304300561}
2025-07-16 23:20:33,299 - root - WARNING - Trial 14 failed - ValueError: Invalid parameters sampled: {'ema_fast': 22, 'ema_slow': 42, 'atr_len': 25, 'vol_sma_len': 32, 'sl_atr_mult': 3.108708105022801, 'trail_atr_mult': 2.7575063879499275, 'risk_multiplier': 2.549000156069475, 'min_bars_between_trades': 1, 'use_volume_filter': False, 'bb_len': 10, 'bb_std': 1.4669670731659072, 'bb_oversold': 0.03058060766500313, 'bb_overbought': 0.7593676245333632, 'mfi_len': 27, 'rsi_len': 20, 'rsi_oversold': 26.85434572718313, 'rsi_overbought': 62.445854016275035, 'stoch_k': 16, 'stoch_d': 6, 'willr_len': 11, 'cci_len': 19, 'tp_atr_mult': 9.58654260957636}
2025-07-16 23:20:33,302 - root - WARNING - Trial 15 failed - ValueError: Invalid parameters sampled: {'ema_fast': 21, 'ema_slow': 71, 'atr_len': 8, 'vol_sma_len': 20, 'sl_atr_mult': 3.2517198314284728, 'trail_atr_mult': 2.4100360274562806, 'risk_multiplier': 3.6412246029708055, 'min_bars_between_trades': 2, 'use_volume_filter': True, 'bb_len': 29, 'bb_std': 1.26097601424573, 'bb_oversold': 0.15472838369289654, 'bb_overbought': 0.9662529555277403, 'mfi_len': 21, 'rsi_len': 14, 'rsi_oversold': 31.08220546105883, 'rsi_overbought': 71.45632226228791, 'stoch_k': 17, 'stoch_d': 10, 'willr_len': 16, 'cci_len': 30, 'tp_atr_mult': 14.148155777604574}
2025-07-16 23:20:33,304 - root - WARNING - Trial 16 failed - ValueError: Invalid parameters sampled: {'ema_fast': 10, 'ema_slow': 25, 'atr_len': 9, 'vol_sma_len': 8, 'sl_atr_mult': 2.188885921511857, 'trail_atr_mult': 2.985621674932342, 'risk_multiplier': 1.6779716211505724, 'min_bars_between_trades': 1, 'use_volume_filter': True, 'bb_len': 35, 'bb_std': 1.8482659819788196, 'bb_oversold': 0.04717791035298094, 'bb_overbought': 0.9102495480337547, 'mfi_len': 22, 'rsi_len': 23, 'rsi_oversold': 33.37677609509714, 'rsi_overbought': 80.08702325962122, 'stoch_k': 13, 'stoch_d': 4, 'willr_len': 25, 'cci_len': 26, 'tp_atr_mult': 14.91454627800606}
2025-07-16 23:20:33,306 - root - WARNING - Trial 17 failed - ValueError: Invalid parameters sampled: {'ema_fast': 15, 'ema_slow': 50, 'atr_len': 21, 'vol_sma_len': 19, 'sl_atr_mult': 3.8615146512071297, 'trail_atr_mult': 3.5469208058976376, 'risk_multiplier': 2.5724850684375458, 'min_bars_between_trades': 2, 'use_volume_filter': True, 'bb_len': 37, 'bb_std': 2.3620804566300713, 'bb_oversold': 0.21008521720478057, 'bb_overbought': 0.8236114082370407, 'mfi_len': 28, 'rsi_len': 15, 'rsi_oversold': 15.270941287007458, 'rsi_overbought': 82.63454941048158, 'stoch_k': 9, 'stoch_d': 5, 'willr_len': 29, 'cci_len': 29, 'tp_atr_mult': 11.160940993109575}
2025-07-16 23:20:33,367 - root - WARNING - Trial 18 failed - ValueError: Invalid parameters sampled: {'ema_fast': 17, 'ema_slow': 50, 'atr_len': 23, 'vol_sma_len': 30, 'sl_atr_mult': 2.693468584978894, 'trail_atr_mult': 0.9239206398757867, 'risk_multiplier': 3.033780049781191, 'min_bars_between_trades': 2, 'use_volume_filter': False, 'bb_len': 31, 'bb_std': 2.8522748789260306, 'bb_oversold': 0.11651069143325121, 'bb_overbought': 0.7845311121234045, 'mfi_len': 14, 'rsi_len': 11, 'rsi_oversold': 16.824131093563597, 'rsi_overbought': 74.66994746638082, 'stoch_k': 16, 'stoch_d': 3, 'willr_len': 22, 'cci_len': 15, 'tp_atr_mult': 8.167271437508191}
2025-07-16 23:20:33,435 - root - WARNING - Trial 19 failed - ValueError: Invalid parameters sampled: {'ema_fast': 7, 'ema_slow': 93, 'atr_len': 14, 'vol_sma_len': 20, 'sl_atr_mult': 2.6705036367006976, 'trail_atr_mult': 3.9872529015917944, 'risk_multiplier': 2.9490282516514323, 'min_bars_between_trades': 2, 'use_volume_filter': True, 'bb_len': 15, 'bb_std': 2.9337411682351933, 'bb_oversold': 0.24921243053904785, 'bb_overbought': 0.8539136419367664, 'mfi_len': 12, 'rsi_len': 18, 'rsi_oversold': 28.689872757094946, 'rsi_overbought': 73.70817882847965, 'stoch_k': 18, 'stoch_d': 3, 'willr_len': 25, 'cci_len': 16, 'tp_atr_mult': 8.052835816396861}
2025-07-16 23:20:33,494 - root - WARNING - Trial 20 failed - ValueError: Invalid parameters sampled: {'ema_fast': 18, 'ema_slow': 51, 'atr_len': 14, 'vol_sma_len': 13, 'sl_atr_mult': 2.0219783784875425, 'trail_atr_mult': 1.2229459648010328, 'risk_multiplier': 1.904216527098098, 'min_bars_between_trades': 2, 'use_volume_filter': True, 'bb_len': 40, 'bb_std': 3.473061706382957, 'bb_oversold': 0.13226074976365043, 'bb_overbought': 0.7813840432672322, 'mfi_len': 15, 'rsi_len': 18, 'rsi_oversold': 19.460365524317258, 'rsi_overbought': 76.3488550152348, 'stoch_k': 15, 'stoch_d': 6, 'willr_len': 21, 'cci_len': 10, 'tp_atr_mult': 9.074884216247845}
2025-07-16 23:20:33,557 - root - WARNING - Trial 21 failed - ValueError: Invalid parameters sampled: {'ema_fast': 10, 'ema_slow': 82, 'atr_len': 15, 'vol_sma_len': 15, 'sl_atr_mult': 3.995517288660878, 'trail_atr_mult': 2.2361718588318125, 'risk_multiplier': 3.3247571014264676, 'min_bars_between_trades': 2, 'use_volume_filter': False, 'bb_len': 15, 'bb_std': 1.6169687282008445, 'bb_oversold': 0.17468328316962464, 'bb_overbought': 0.8613735584462783, 'mfi_len': 15, 'rsi_len': 11, 'rsi_oversold': 28.100861940232623, 'rsi_overbought': 66.27879493139142, 'stoch_k': 14, 'stoch_d': 7, 'willr_len': 8, 'cci_len': 23, 'tp_atr_mult': 6.1854633453375625}
2025-07-16 23:20:33,625 - root - WARNING - Trial 22 failed - ValueError: Invalid parameters sampled: {'ema_fast': 16, 'ema_slow': 40, 'atr_len': 19, 'vol_sma_len': 29, 'sl_atr_mult': 2.732360103033714, 'trail_atr_mult': 3.576993743355888, 'risk_multiplier': 1.537553029064218, 'min_bars_between_trades': 2, 'use_volume_filter': False, 'bb_len': 15, 'bb_std': 2.6603319727866688, 'bb_oversold': 0.10775284495474417, 'bb_overbought': 0.7950429080841851, 'mfi_len': 17, 'rsi_len': 11, 'rsi_oversold': 39.335728525712305, 'rsi_overbought': 68.31690006486315, 'stoch_k': 8, 'stoch_d': 7, 'willr_len': 26, 'cci_len': 13, 'tp_atr_mult': 7.323150511125416}
2025-07-16 23:20:33,698 - root - WARNING - Trial 23 failed - ValueError: Invalid parameters sampled: {'ema_fast': 13, 'ema_slow': 58, 'atr_len': 23, 'vol_sma_len': 12, 'sl_atr_mult': 2.4471452751259317, 'trail_atr_mult': 3.991903451139116, 'risk_multiplier': 2.759066118052397, 'min_bars_between_trades': 2, 'use_volume_filter': False, 'bb_len': 28, 'bb_std': 3.498374997292677, 'bb_oversold': 0.140087312106495, 'bb_overbought': 0.8325252583916192, 'mfi_len': 10, 'rsi_len': 12, 'rsi_oversold': 36.48677566717737, 'rsi_overbought': 64.44837292184452, 'stoch_k': 15, 'stoch_d': 8, 'willr_len': 20, 'cci_len': 17, 'tp_atr_mult': 6.284594898129229}
2025-07-16 23:20:33,762 - root - WARNING - Trial 24 failed - ValueError: Invalid parameters sampled: {'ema_fast': 5, 'ema_slow': 91, 'atr_len': 23, 'vol_sma_len': 17, 'sl_atr_mult': 2.822571266226635, 'trail_atr_mult': 2.531645013747506, 'risk_multiplier': 3.342057835146702, 'min_bars_between_trades': 2, 'use_volume_filter': False, 'bb_len': 22, 'bb_std': 3.278870428223886, 'bb_oversold': 0.19813029968635146, 'bb_overbought': 0.8476000204136998, 'mfi_len': 13, 'rsi_len': 9, 'rsi_oversold': 28.917912187853148, 'rsi_overbought': 72.60644098840736, 'stoch_k': 21, 'stoch_d': 5, 'willr_len': 23, 'cci_len': 12, 'tp_atr_mult': 8.3318400094807}
2025-07-16 23:20:33,826 - root - WARNING - Trial 25 failed - ValueError: Invalid parameters sampled: {'ema_fast': 12, 'ema_slow': 61, 'atr_len': 20, 'vol_sma_len': 13, 'sl_atr_mult': 2.469171623752448, 'trail_atr_mult': 0.9834273120905179, 'risk_multiplier': 2.341118707322151, 'min_bars_between_trades': 2, 'use_volume_filter': False, 'bb_len': 13, 'bb_std': 3.0126727487987504, 'bb_oversold': 0.06364783926456187, 'bb_overbought': 0.7756763038077731, 'mfi_len': 18, 'rsi_len': 17, 'rsi_oversold': 17.734225008912293, 'rsi_overbought': 60.751368895632794, 'stoch_k': 18, 'stoch_d': 6, 'willr_len': 27, 'cci_len': 15, 'tp_atr_mult': 9.890126461853193}
2025-07-16 23:20:33,891 - root - WARNING - Trial 26 failed - ValueError: Invalid parameters sampled: {'ema_fast': 19, 'ema_slow': 54, 'atr_len': 15, 'vol_sma_len': 22, 'sl_atr_mult': 2.237784658578076, 'trail_atr_mult': 1.2810911047361537, 'risk_multiplier': 1.8800970875960044, 'min_bars_between_trades': 2, 'use_volume_filter': False, 'bb_len': 17, 'bb_std': 2.6951956302556113, 'bb_oversold': 0.09592493177560499, 'bb_overbought': 0.8773748029935142, 'mfi_len': 15, 'rsi_len': 25, 'rsi_oversold': 26.130018925722844, 'rsi_overbought': 75.71317236004289, 'stoch_k': 12, 'stoch_d': 3, 'willr_len': 23, 'cci_len': 23, 'tp_atr_mult': 7.480380501924416}
2025-07-16 23:20:33,966 - root - WARNING - Trial 27 failed - ValueError: Invalid parameters sampled: {'ema_fast': 8, 'ema_slow': 45, 'atr_len': 19, 'vol_sma_len': 28, 'sl_atr_mult': 2.011882487189464, 'trail_atr_mult': 2.158277910706618, 'risk_multiplier': 3.9932902108871873, 'min_bars_between_trades': 2, 'use_volume_filter': False, 'bb_len': 13, 'bb_std': 2.166281599127755, 'bb_oversold': 0.2466696812399938, 'bb_overbought': 0.8064044571230189, 'mfi_len': 10, 'rsi_len': 10, 'rsi_oversold': 36.66076480262071, 'rsi_overbought': 70.63650289377743, 'stoch_k': 17, 'stoch_d': 8, 'willr_len': 18, 'cci_len': 11, 'tp_atr_mult': 8.721201914112367}
2025-07-16 23:20:34,029 - root - WARNING - Trial 28 failed - ValueError: Invalid parameters sampled: {'ema_fast': 15, 'ema_slow': 34, 'atr_len': 22, 'vol_sma_len': 22, 'sl_atr_mult': 3.5637174228999706, 'trail_atr_mult': 3.38679240889646, 'risk_multiplier': 3.100008593515032, 'min_bars_between_trades': 2, 'use_volume_filter': True, 'bb_len': 27, 'bb_std': 1.2518554593307851, 'bb_oversold': 0.17362210593080957, 'bb_overbought': 0.8490668439648866, 'mfi_len': 13, 'rsi_len': 19, 'rsi_oversold': 29.876105256394194, 'rsi_overbought': 64.45618670304589, 'stoch_k': 15, 'stoch_d': 4, 'willr_len': 27, 'cci_len': 22, 'tp_atr_mult': 10.305246851119037}
2025-07-16 23:20:34,094 - root - WARNING - Trial 29 failed - ValueError: Invalid parameters sampled: {'ema_fast': 30, 'ema_slow': 80, 'atr_len': 24, 'vol_sma_len': 10, 'sl_atr_mult': 3.6123514587561845, 'trail_atr_mult': 3.772235792818482, 'risk_multiplier': 3.6306083961656506, 'min_bars_between_trades': 2, 'use_volume_filter': False, 'bb_len': 12, 'bb_std': 3.172818854476268, 'bb_oversold': 0.15561863236894002, 'bb_overbought': 0.8248569004611103, 'mfi_len': 9, 'rsi_len': 14, 'rsi_oversold': 22.987002402671223, 'rsi_overbought': 72.66979113189907, 'stoch_k': 19, 'stoch_d': 5, 'willr_len': 19, 'cci_len': 13, 'tp_atr_mult': 6.8417312651844435}
2025-07-16 23:20:34,163 - root - WARNING - Trial 30 failed - ValueError: Invalid parameters sampled: {'ema_fast': 19, 'ema_slow': 64, 'atr_len': 12, 'vol_sma_len': 17, 'sl_atr_mult': 3.486521777089921, 'trail_atr_mult': 2.7240698328306068, 'risk_multiplier': 2.1086276288073744, 'min_bars_between_trades': 2, 'use_volume_filter': False, 'bb_len': 21, 'bb_std': 3.3426655510535457, 'bb_oversold': 0.13977868691713957, 'bb_overbought': 0.8173298331425217, 'mfi_len': 16, 'rsi_len': 13, 'rsi_oversold': 21.72739203300676, 'rsi_overbought': 76.90877021090006, 'stoch_k': 13, 'stoch_d': 8, 'willr_len': 30, 'cci_len': 17, 'tp_atr_mult': 12.979842248074556}
2025-07-16 23:20:34,242 - root - WARNING - Trial 31 failed - ValueError: Invalid parameters sampled: {'ema_fast': 30, 'ema_slow': 98, 'atr_len': 12, 'vol_sma_len': 11, 'sl_atr_mult': 3.9678957515635602, 'trail_atr_mult': 3.261955829667656, 'risk_multiplier': 3.4327771606229205, 'min_bars_between_trades': 2, 'use_volume_filter': False, 'bb_len': 17, 'bb_std': 3.1702892103798495, 'bb_oversold': 0.1684545785037106, 'bb_overbought': 0.7961253178243748, 'mfi_len': 10, 'rsi_len': 12, 'rsi_oversold': 24.181847345624497, 'rsi_overbought': 78.18026483398494, 'stoch_k': 22, 'stoch_d': 3, 'willr_len': 8, 'cci_len': 12, 'tp_atr_mult': 11.109308104253383}
2025-07-16 23:20:34,307 - root - WARNING - Trial 32 failed - ValueError: Invalid parameters sampled: {'ema_fast': 16, 'ema_slow': 80, 'atr_len': 21, 'vol_sma_len': 8, 'sl_atr_mult': 3.750811667298548, 'trail_atr_mult': 2.17616597456301, 'risk_multiplier': 1.5056080122355049, 'min_bars_between_trades': 2, 'use_volume_filter': False, 'bb_len': 14, 'bb_std': 3.0889492843941992, 'bb_oversold': 0.20291155948597753, 'bb_overbought': 0.814330354780049, 'mfi_len': 12, 'rsi_len': 13, 'rsi_oversold': 19.06037258848141, 'rsi_overbought': 78.59013819949963, 'stoch_k': 19, 'stoch_d': 4, 'willr_len': 18, 'cci_len': 14, 'tp_atr_mult': 12.322639499353567}
2025-07-16 23:20:34,375 - root - WARNING - Trial 33 failed - ValueError: Invalid parameters sampled: {'ema_fast': 14, 'ema_slow': 87, 'atr_len': 15, 'vol_sma_len': 14, 'sl_atr_mult': 3.8465520875346337, 'trail_atr_mult': 2.937285404881813, 'risk_multiplier': 3.8458365509680887, 'min_bars_between_trades': 2, 'use_volume_filter': False, 'bb_len': 11, 'bb_std': 2.8608536436150747, 'bb_oversold': 0.2268697398558153, 'bb_overbought': 0.8404899742494935, 'mfi_len': 11, 'rsi_len': 16, 'rsi_oversold': 20.121376499136126, 'rsi_overbought': 67.93098449238732, 'stoch_k': 15, 'stoch_d': 6, 'willr_len': 11, 'cci_len': 12, 'tp_atr_mult': 13.096021269989835}
2025-07-16 23:20:34,451 - root - WARNING - Trial 34 failed - ValueError: Invalid parameters sampled: {'ema_fast': 9, 'ema_slow': 93, 'atr_len': 12, 'vol_sma_len': 10, 'sl_atr_mult': 2.893511657455603, 'trail_atr_mult': 3.280438528301229, 'risk_multiplier': 3.43791577263251, 'min_bars_between_trades': 2, 'use_volume_filter': False, 'bb_len': 10, 'bb_std': 3.333233558864575, 'bb_oversold': 0.18021580645505766, 'bb_overbought': 0.7684710382312732, 'mfi_len': 8, 'rsi_len': 10, 'rsi_oversold': 21.183113614872475, 'rsi_overbought': 81.09745637619918, 'stoch_k': 14, 'stoch_d': 8, 'willr_len': 13, 'cci_len': 26, 'tp_atr_mult': 12.361328882203019}
2025-07-16 23:20:34,523 - root - WARNING - Trial 35 failed - ValueError: Invalid parameters sampled: {'ema_fast': 27, 'ema_slow': 87, 'atr_len': 16, 'vol_sma_len': 40, 'sl_atr_mult': 3.147696816815974, 'trail_atr_mult': 2.435440117508891, 'risk_multiplier': 3.6263938908183193, 'min_bars_between_trades': 2, 'use_volume_filter': False, 'bb_len': 12, 'bb_std': 1.7121265788272229, 'bb_oversold': 0.15116161313970214, 'bb_overbought': 0.7917324157062148, 'mfi_len': 11, 'rsi_len': 21, 'rsi_oversold': 23.35302049622258, 'rsi_overbought': 60.10664121492091, 'stoch_k': 20, 'stoch_d': 9, 'willr_len': 23, 'cci_len': 10, 'tp_atr_mult': 11.134823072664926}
2025-07-16 23:20:34,592 - root - WARNING - Trial 36 failed - ValueError: Invalid parameters sampled: {'ema_fast': 12, 'ema_slow': 67, 'atr_len': 21, 'vol_sma_len': 16, 'sl_atr_mult': 2.5872529223680485, 'trail_atr_mult': 1.9580716704107863, 'risk_multiplier': 3.2044679960462545, 'min_bars_between_trades': 2, 'use_volume_filter': False, 'bb_len': 19, 'bb_std': 3.0544962447290964, 'bb_oversold': 0.22807721946746531, 'bb_overbought': 0.8059982445471151, 'mfi_len': 18, 'rsi_len': 15, 'rsi_oversold': 24.665090364625417, 'rsi_overbought': 64.21320993219526, 'stoch_k': 18, 'stoch_d': 4, 'willr_len': 16, 'cci_len': 15, 'tp_atr_mult': 10.34624313025794}
2025-07-16 23:20:34,658 - root - WARNING - Trial 37 failed - ValueError: Invalid parameters sampled: {'ema_fast': 28, 'ema_slow': 76, 'atr_len': 18, 'vol_sma_len': 22, 'sl_atr_mult': 3.6978717629166895, 'trail_atr_mult': 1.6351184301286812, 'risk_multiplier': 2.9299173771813876, 'min_bars_between_trades': 1, 'use_volume_filter': True, 'bb_len': 16, 'bb_std': 2.5626713291370296, 'bb_oversold': 0.12228657090002223, 'bb_overbought': 0.8667097217730689, 'mfi_len': 13, 'rsi_len': 8, 'rsi_oversold': 26.43572113601687, 'rsi_overbought': 71.20367838660084, 'stoch_k': 24, 'stoch_d': 5, 'willr_len': 15, 'cci_len': 24, 'tp_atr_mult': 11.842204511574497}
2025-07-16 23:20:34,741 - root - WARNING - Trial 38 failed - ValueError: Invalid parameters sampled: {'ema_fast': 11, 'ema_slow': 98, 'atr_len': 10, 'vol_sma_len': 8, 'sl_atr_mult': 3.4854250557138555, 'trail_atr_mult': 3.526288273338189, 'risk_multiplier': 1.6844415233096286, 'min_bars_between_trades': 2, 'use_volume_filter': False, 'bb_len': 32, 'bb_std': 3.427111017434851, 'bb_oversold': 0.19334933674358584, 'bb_overbought': 0.941108955956156, 'mfi_len': 9, 'rsi_len': 12, 'rsi_oversold': 25.325934735822916, 'rsi_overbought': 74.86361466495424, 'stoch_k': 12, 'stoch_d': 7, 'willr_len': 20, 'cci_len': 11, 'tp_atr_mult': 7.583371707926913}
2025-07-16 23:20:34,809 - root - WARNING - Trial 39 failed - ValueError: Invalid parameters sampled: {'ema_fast': 14, 'ema_slow': 67, 'atr_len': 13, 'vol_sma_len': 34, 'sl_atr_mult': 2.977082992360157, 'trail_atr_mult': 3.0752869344410607, 'risk_multiplier': 2.757305974067794, 'min_bars_between_trades': 2, 'use_volume_filter': True, 'bb_len': 24, 'bb_std': 2.189453464438978, 'bb_oversold': 0.21350487649485603, 'bb_overbought': 0.8319298446124732, 'mfi_len': 14, 'rsi_len': 13, 'rsi_oversold': 27.43674387414493, 'rsi_overbought': 69.7812832228854, 'stoch_k': 13, 'stoch_d': 9, 'willr_len': 10, 'cci_len': 19, 'tp_atr_mult': 12.996859048394306}
2025-07-16 23:20:34,911 - root - WARNING - Trial 40 failed - ValueError: Invalid parameters sampled: {'ema_fast': 19, 'ema_slow': 76, 'atr_len': 19, 'vol_sma_len': 18, 'sl_atr_mult': 3.898585381785307, 'trail_atr_mult': 2.805876418108966, 'risk_multiplier': 3.7943475915623894, 'min_bars_between_trades': 1, 'use_volume_filter': False, 'bb_len': 36, 'bb_std': 2.0007267362585357, 'bb_oversold': 0.16573353604745575, 'bb_overbought': 0.8180524230939367, 'mfi_len': 9, 'rsi_len': 10, 'rsi_oversold': 34.16815392928371, 'rsi_overbought': 77.7833082534523, 'stoch_k': 16, 'stoch_d': 10, 'willr_len': 27, 'cci_len': 27, 'tp_atr_mult': 6.604276048499387}
2025-07-16 23:20:35,005 - root - WARNING - Trial 41 failed - ValueError: Invalid parameters sampled: {'ema_fast': 24, 'ema_slow': 67, 'atr_len': 22, 'vol_sma_len': 26, 'sl_atr_mult': 3.2752037737875517, 'trail_atr_mult': 1.3823991927185044, 'risk_multiplier': 1.8019323117325283, 'min_bars_between_trades': 1, 'use_volume_filter': False, 'bb_len': 19, 'bb_std': 3.2022370103457, 'bb_oversold': 0.23829321422247515, 'bb_overbought': 0.8039380468731826, 'mfi_len': 19, 'rsi_len': 20, 'rsi_oversold': 22.247261409657007, 'rsi_overbought': 62.26715791695324, 'stoch_k': 11, 'stoch_d': 4, 'willr_len': 30, 'cci_len': 27, 'tp_atr_mult': 11.64874977555374}
2025-07-16 23:20:35,074 - root - WARNING - Trial 42 failed - ValueError: Invalid parameters sampled: {'ema_fast': 24, 'ema_slow': 55, 'atr_len': 24, 'vol_sma_len': 28, 'sl_atr_mult': 3.0381899982167067, 'trail_atr_mult': 2.6460178906226246, 'risk_multiplier': 2.010536014849184, 'min_bars_between_trades': 1, 'use_volume_filter': False, 'bb_len': 10, 'bb_std': 2.496507131325048, 'bb_oversold': 0.22034464433339992, 'bb_overbought': 0.8411840799124334, 'mfi_len': 17, 'rsi_len': 21, 'rsi_oversold': 20.947162016550493, 'rsi_overbought': 63.44422698230174, 'stoch_k': 14, 'stoch_d': 3, 'willr_len': 28, 'cci_len': 25, 'tp_atr_mult': 13.60320302482962}
2025-07-16 23:20:35,143 - root - WARNING - Trial 43 failed - ValueError: Invalid parameters sampled: {'ema_fast': 26, 'ema_slow': 62, 'atr_len': 20, 'vol_sma_len': 24, 'sl_atr_mult': 3.1712652173390086, 'trail_atr_mult': 1.9932344727744695, 'risk_multiplier': 2.259308422740113, 'min_bars_between_trades': 1, 'use_volume_filter': False, 'bb_len': 14, 'bb_std': 1.4621980924853828, 'bb_oversold': 0.2035538885120328, 'bb_overbought': 0.8079652716772745, 'mfi_len': 20, 'rsi_len': 23, 'rsi_oversold': 23.891498039195817, 'rsi_overbought': 60.80541613117858, 'stoch_k': 11, 'stoch_d': 4, 'willr_len': 24, 'cci_len': 21, 'tp_atr_mult': 10.722886056091152}
2025-07-16 23:20:35,224 - root - WARNING - Trial 44 failed - ValueError: Invalid parameters sampled: {'ema_fast': 28, 'ema_slow': 57, 'atr_len': 17, 'vol_sma_len': 31, 'sl_atr_mult': 2.919422848049901, 'trail_atr_mult': 1.7270721970077738, 'risk_multiplier': 1.5827169174529763, 'min_bars_between_trades': 1, 'use_volume_filter': True, 'bb_len': 12, 'bb_std': 2.032871330862074, 'bb_oversold': 0.23659034074231627, 'bb_overbought': 0.7694493652303829, 'mfi_len': 23, 'rsi_len': 22, 'rsi_oversold': 17.93667267974581, 'rsi_overbought': 66.997121341195, 'stoch_k': 17, 'stoch_d': 5, 'willr_len': 29, 'cci_len': 30, 'tp_atr_mult': 12.038292433207282}
2025-07-16 23:20:35,302 - root - WARNING - Trial 45 failed - ValueError: Invalid parameters sampled: {'ema_fast': 22, 'ema_slow': 76, 'atr_len': 20, 'vol_sma_len': 35, 'sl_atr_mult': 3.4330704215453927, 'trail_atr_mult': 3.190409011305184, 'risk_multiplier': 1.8061641619963755, 'min_bars_between_trades': 1, 'use_volume_filter': False, 'bb_len': 21, 'bb_std': 3.292759374209548, 'bb_oversold': 0.18436399567529071, 'bb_overbought': 0.7918905985028716, 'mfi_len': 12, 'rsi_len': 17, 'rsi_oversold': 22.620585873339518, 'rsi_overbought': 65.78391974268673, 'stoch_k': 14, 'stoch_d': 3, 'willr_len': 12, 'cci_len': 28, 'tp_atr_mult': 12.694202222601078}
2025-07-16 23:20:35,376 - root - WARNING - Trial 46 failed - ValueError: Invalid parameters sampled: {'ema_fast': 17, 'ema_slow': 46, 'atr_len': 24, 'vol_sma_len': 15, 'sl_atr_mult': 3.3035041720685725, 'trail_atr_mult': 2.8977195745530113, 'risk_multiplier': 1.59790770417279, 'min_bars_between_trades': 2, 'use_volume_filter': True, 'bb_len': 11, 'bb_std': 1.8344635479936633, 'bb_oversold': 0.21899856405069235, 'bb_overbought': 0.8269325976954176, 'mfi_len': 8, 'rsi_len': 19, 'rsi_oversold': 30.70203582695102, 'rsi_overbought': 61.437202293241576, 'stoch_k': 13, 'stoch_d': 4, 'willr_len': 14, 'cci_len': 13, 'tp_atr_mult': 9.852187004489899}
2025-07-16 23:20:35,447 - root - WARNING - Trial 47 failed - ValueError: Invalid parameters sampled: {'ema_fast': 13, 'ema_slow': 71, 'atr_len': 25, 'vol_sma_len': 20, 'sl_atr_mult': 2.250152231127167, 'trail_atr_mult': 2.5656238844755306, 'risk_multiplier': 2.1262610674528606, 'min_bars_between_trades': 2, 'use_volume_filter': False, 'bb_len': 17, 'bb_std': 2.737328683285252, 'bb_oversold': 0.021302873923254896, 'bb_overbought': 0.8861175923240778, 'mfi_len': 11, 'rsi_len': 14, 'rsi_oversold': 25.119447739543155, 'rsi_overbought': 65.6099593267886, 'stoch_k': 16, 'stoch_d': 5, 'willr_len': 21, 'cci_len': 14, 'tp_atr_mult': 9.140546859527664}
2025-07-16 23:20:35,533 - root - WARNING - Trial 48 failed - ValueError: Invalid parameters sampled: {'ema_fast': 11, 'ema_slow': 83, 'atr_len': 18, 'vol_sma_len': 11, 'sl_atr_mult': 2.794410215615477, 'trail_atr_mult': 2.297485945715791, 'risk_multiplier': 1.6532391920043576, 'min_bars_between_trades': 1, 'use_volume_filter': True, 'bb_len': 25, 'bb_std': 3.391468010362741, 'bb_oversold': 0.060436973405289016, 'bb_overbought': 0.7522793171465936, 'mfi_len': 17, 'rsi_len': 15, 'rsi_oversold': 38.76588195403831, 'rsi_overbought': 63.071966996701, 'stoch_k': 10, 'stoch_d': 6, 'willr_len': 28, 'cci_len': 25, 'tp_atr_mult': 11.470937839672033}
2025-07-16 23:20:35,608 - root - WARNING - Trial 49 failed - ValueError: Invalid parameters sampled: {'ema_fast': 21, 'ema_slow': 64, 'atr_len': 8, 'vol_sma_len': 25, 'sl_atr_mult': 3.3547799068799646, 'trail_atr_mult': 3.37201202790906, 'risk_multiplier': 1.9440621788909587, 'min_bars_between_trades': 2, 'use_volume_filter': False, 'bb_len': 18, 'bb_std': 2.2881738132340605, 'bb_oversold': 0.08732185753186002, 'bb_overbought': 0.8616831839805109, 'mfi_len': 14, 'rsi_len': 22, 'rsi_oversold': 19.761205629701422, 'rsi_overbought': 84.7594860715258, 'stoch_k': 19, 'stoch_d': 3, 'willr_len': 26, 'cci_len': 11, 'tp_atr_mult': 10.707646058801076}
2025-07-16 23:20:35,679 - root - WARNING - Trial 50 failed - ValueError: Invalid parameters sampled: {'ema_fast': 15, 'ema_slow': 90, 'atr_len': 22, 'vol_sma_len': 27, 'sl_atr_mult': 2.567389910400113, 'trail_atr_mult': 1.0559820692947286, 'risk_multiplier': 1.7788688811456668, 'min_bars_between_trades': 2, 'use_volume_filter': False, 'bb_len': 30, 'bb_std': 1.3931500238074905, 'bb_oversold': 0.18923463741000074, 'bb_overbought': 0.7867813401244745, 'mfi_len': 20, 'rsi_len': 24, 'rsi_oversold': 15.86524586097298, 'rsi_overbought': 81.77374824184639, 'stoch_k': 12, 'stoch_d': 9, 'willr_len': 17, 'cci_len': 16, 'tp_atr_mult': 14.377708600923121}
2025-07-16 23:20:35,765 - root - WARNING - Trial 51 failed - ValueError: Invalid parameters sampled: {'ema_fast': 29, 'ema_slow': 95, 'atr_len': 10, 'vol_sma_len': 37, 'sl_atr_mult': 3.1795215916551514, 'trail_atr_mult': 1.4795051671508133, 'risk_multiplier': 3.790165755222445, 'min_bars_between_trades': 1, 'use_volume_filter': False, 'bb_len': 23, 'bb_std': 2.965069725587484, 'bb_oversold': 0.21172542596070532, 'bb_overbought': 0.8018346029529102, 'mfi_len': 25, 'rsi_len': 15, 'rsi_oversold': 20.715049161832265, 'rsi_overbought': 67.55654661533796, 'stoch_k': 14, 'stoch_d': 10, 'willr_len': 15, 'cci_len': 19, 'tp_atr_mult': 11.898427625462155}
2025-07-16 23:20:35,840 - root - WARNING - Trial 52 failed - ValueError: Invalid parameters sampled: {'ema_fast': 26, 'ema_slow': 100, 'atr_len': 11, 'vol_sma_len': 34, 'sl_atr_mult': 3.089705064905621, 'trail_atr_mult': 3.0811411490113874, 'risk_multiplier': 3.512197632068616, 'min_bars_between_trades': 1, 'use_volume_filter': False, 'bb_len': 34, 'bb_std': 3.121492372374814, 'bb_oversold': 0.23387939779951014, 'bb_overbought': 0.7610506512099707, 'mfi_len': 16, 'rsi_len': 12, 'rsi_oversold': 18.899440849675774, 'rsi_overbought': 69.44305409232282, 'stoch_k': 15, 'stoch_d': 10, 'willr_len': 17, 'cci_len': 20, 'tp_atr_mult': 12.654950652130102}
2025-07-16 23:20:35,923 - root - WARNING - Trial 53 failed - ValueError: Invalid parameters sampled: {'ema_fast': 28, 'ema_slow': 83, 'atr_len': 11, 'vol_sma_len': 32, 'sl_atr_mult': 3.0014250862404, 'trail_atr_mult': 3.6366348245863582, 'risk_multiplier': 3.8680730097095464, 'min_bars_between_trades': 1, 'use_volume_filter': False, 'bb_len': 20, 'bb_std': 3.2448995930032583, 'bb_oversold': 0.20173917107404127, 'bb_overbought': 0.813807000952312, 'mfi_len': 19, 'rsi_len': 13, 'rsi_oversold': 22.184708692433365, 'rsi_overbought': 65.06127385931147, 'stoch_k': 17, 'stoch_d': 10, 'willr_len': 9, 'cci_len': 18, 'tp_atr_mult': 11.368941917192627}
2025-07-16 23:20:36,024 - root - WARNING - Trial 54 failed - ValueError: Invalid parameters sampled: {'ema_fast': 25, 'ema_slow': 88, 'atr_len': 9, 'vol_sma_len': 40, 'sl_atr_mult': 2.893323040660532, 'trail_atr_mult': 3.7111292267139104, 'risk_multiplier': 3.6890485070248027, 'min_bars_between_trades': 1, 'use_volume_filter': True, 'bb_len': 39, 'bb_std': 2.888497323345044, 'bb_oversold': 0.22253470131821915, 'bb_overbought': 0.78089026971879, 'mfi_len': 21, 'rsi_len': 11, 'rsi_oversold': 23.206267513754817, 'rsi_overbought': 63.20696122431191, 'stoch_k': 16, 'stoch_d': 9, 'willr_len': 12, 'cci_len': 29, 'tp_atr_mult': 13.409198192387038}
2025-07-16 23:20:36,097 - root - WARNING - Trial 55 failed - ValueError: Invalid parameters sampled: {'ema_fast': 30, 'ema_slow': 52, 'atr_len': 13, 'vol_sma_len': 36, 'sl_atr_mult': 3.612192891881174, 'trail_atr_mult': 0.8307835349728323, 'risk_multiplier': 3.7101466357278117, 'min_bars_between_trades': 1, 'use_volume_filter': False, 'bb_len': 25, 'bb_std': 3.467611959706475, 'bb_oversold': 0.24268053645063428, 'bb_overbought': 0.835394922337205, 'mfi_len': 16, 'rsi_len': 18, 'rsi_oversold': 27.12483109683935, 'rsi_overbought': 61.48298548832879, 'stoch_k': 11, 'stoch_d': 10, 'willr_len': 19, 'cci_len': 23, 'tp_atr_mult': 12.07582602871835}
2025-07-16 23:20:36,170 - root - WARNING - Trial 56 failed - ValueError: Invalid parameters sampled: {'ema_fast': 27, 'ema_slow': 74, 'atr_len': 21, 'vol_sma_len': 23, 'sl_atr_mult': 3.2209560534201764, 'trail_atr_mult': 3.4436568739583646, 'risk_multiplier': 2.60461105029571, 'min_bars_between_trades': 2, 'use_volume_filter': False, 'bb_len': 14, 'bb_std': 2.788163900362444, 'bb_oversold': 0.2070794864307887, 'bb_overbought': 0.9271296370541122, 'mfi_len': 18, 'rsi_len': 14, 'rsi_oversold': 17.85713984913338, 'rsi_overbought': 73.78950294161903, 'stoch_k': 13, 'stoch_d': 7, 'willr_len': 14, 'cci_len': 21, 'tp_atr_mult': 7.791395073852094}
2025-07-16 23:20:36,260 - root - WARNING - Trial 57 failed - ValueError: Invalid parameters sampled: {'ema_fast': 16, 'ema_slow': 59, 'atr_len': 16, 'vol_sma_len': 13, 'sl_atr_mult': 2.113136206828994, 'trail_atr_mult': 3.03474434744041, 'risk_multiplier': 3.5574790475121425, 'min_bars_between_trades': 2, 'use_volume_filter': True, 'bb_len': 27, 'bb_std': 2.5975651259490378, 'bb_oversold': 0.19590958673272363, 'bb_overbought': 0.7737117939213686, 'mfi_len': 22, 'rsi_len': 16, 'rsi_oversold': 32.306592137577844, 'rsi_overbought': 68.86585636033787, 'stoch_k': 15, 'stoch_d': 3, 'willr_len': 22, 'cci_len': 24, 'tp_atr_mult': 14.10261571680721}
2025-07-16 23:20:36,332 - root - WARNING - Trial 58 failed - ValueError: Invalid parameters sampled: {'ema_fast': 12, 'ema_slow': 69, 'atr_len': 18, 'vol_sma_len': 19, 'sl_atr_mult': 3.8037904654027512, 'trail_atr_mult': 3.8173347560130675, 'risk_multiplier': 3.933039672061188, 'min_bars_between_trades': 2, 'use_volume_filter': False, 'bb_len': 22, 'bb_std': 2.423873797941864, 'bb_oversold': 0.047827584987495356, 'bb_overbought': 0.824339881076838, 'mfi_len': 15, 'rsi_len': 21, 'rsi_oversold': 21.50614039137859, 'rsi_overbought': 79.50904146847417, 'stoch_k': 18, 'stoch_d': 8, 'willr_len': 15, 'cci_len': 26, 'tp_atr_mult': 8.481723306257047}
2025-07-16 23:20:36,409 - root - WARNING - Trial 59 failed - ValueError: Invalid parameters sampled: {'ema_fast': 29, 'ema_slow': 46, 'atr_len': 23, 'vol_sma_len': 9, 'sl_atr_mult': 2.633308718879991, 'trail_atr_mult': 2.811474376121261, 'risk_multiplier': 3.1500995055279204, 'min_bars_between_trades': 1, 'use_volume_filter': False, 'bb_len': 16, 'bb_std': 3.030952118349867, 'bb_oversold': 0.15874551188347805, 'bb_overbought': 0.8538120175719444, 'mfi_len': 12, 'rsi_len': 9, 'rsi_oversold': 24.167247893390655, 'rsi_overbought': 83.39055267439029, 'stoch_k': 14, 'stoch_d': 5, 'willr_len': 16, 'cci_len': 16, 'tp_atr_mult': 9.562127740108005}
2025-07-16 23:20:36,494 - root - WARNING - Trial 60 failed - ValueError: Invalid parameters sampled: {'ema_fast': 23, 'ema_slow': 85, 'atr_len': 19, 'vol_sma_len': 30, 'sl_atr_mult': 2.77632423616296, 'trail_atr_mult': 3.2251622429057907, 'risk_multiplier': 3.334222934718259, 'min_bars_between_trades': 2, 'use_volume_filter': False, 'bb_len': 13, 'bb_std': 3.3898914959208777, 'bb_oversold': 0.1013164246706151, 'bb_overbought': 0.764599568015469, 'mfi_len': 20, 'rsi_len': 20, 'rsi_oversold': 25.874119085042764, 'rsi_overbought': 72.00801954938014, 'stoch_k': 20, 'stoch_d': 9, 'willr_len': 20, 'cci_len': 13, 'tp_atr_mult': 7.179324018088023}
2025-07-16 23:20:36,588 - root - WARNING - Trial 61 failed - ValueError: Invalid parameters sampled: {'ema_fast': 14, 'ema_slow': 95, 'atr_len': 25, 'vol_sma_len': 15, 'sl_atr_mult': 2.351073392966863, 'trail_atr_mult': 1.7948357930793555, 'risk_multiplier': 2.5012504500613355, 'min_bars_between_trades': 1, 'use_volume_filter': True, 'bb_len': 11, 'bb_std': 1.7801494123018715, 'bb_oversold': 0.2290758410494576, 'bb_overbought': 0.799178399475899, 'mfi_len': 10, 'rsi_len': 16, 'rsi_oversold': 39.7104246794626, 'rsi_overbought': 66.35420641543733, 'stoch_k': 22, 'stoch_d': 10, 'willr_len': 12, 'cci_len': 27, 'tp_atr_mult': 8.893690772310118}
2025-07-16 23:20:36,670 - root - WARNING - Trial 62 failed - ValueError: Invalid parameters sampled: {'ema_fast': 13, 'ema_slow': 96, 'atr_len': 24, 'vol_sma_len': 12, 'sl_atr_mult': 3.9201619619112043, 'trail_atr_mult': 2.072756302085719, 'risk_multiplier': 2.658569986440332, 'min_bars_between_trades': 1, 'use_volume_filter': True, 'bb_len': 11, 'bb_std': 1.5929044145967046, 'bb_oversold': 0.21433369256346044, 'bb_overbought': 0.8143978462213807, 'mfi_len': 9, 'rsi_len': 17, 'rsi_oversold': 37.40712129342152, 'rsi_overbought': 64.09889489303683, 'stoch_k': 21, 'stoch_d': 9, 'willr_len': 10, 'cci_len': 28, 'tp_atr_mult': 9.406816877142543}
2025-07-16 23:20:36,760 - root - WARNING - Trial 63 failed - ValueError: Invalid parameters sampled: {'ema_fast': 18, 'ema_slow': 90, 'atr_len': 23, 'vol_sma_len': 21, 'sl_atr_mult': 3.0863721571269935, 'trail_atr_mult': 2.302234208084645, 'risk_multiplier': 2.31516085262615, 'min_bars_between_trades': 1, 'use_volume_filter': True, 'bb_len': 10, 'bb_std': 1.5807527501612517, 'bb_oversold': 0.24431899909350938, 'bb_overbought': 0.785998227678501, 'mfi_len': 13, 'rsi_len': 19, 'rsi_oversold': 34.48717747805974, 'rsi_overbought': 62.75000282101459, 'stoch_k': 24, 'stoch_d': 10, 'willr_len': 13, 'cci_len': 25, 'tp_atr_mult': 9.997381970678754}
2025-07-16 23:20:36,847 - root - WARNING - Trial 64 failed - ValueError: Invalid parameters sampled: {'ema_fast': 10, 'ema_slow': 100, 'atr_len': 21, 'vol_sma_len': 17, 'sl_atr_mult': 2.9520087709630003, 'trail_atr_mult': 1.13308111093288, 'risk_multiplier': 1.8890496900708962, 'min_bars_between_trades': 1, 'use_volume_filter': True, 'bb_len': 15, 'bb_std': 1.3758271065532774, 'bb_oversold': 0.180986720973108, 'bb_overbought': 0.8401132934169927, 'mfi_len': 11, 'rsi_len': 14, 'rsi_oversold': 37.60227514017956, 'rsi_overbought': 61.76826873259718, 'stoch_k': 20, 'stoch_d': 9, 'willr_len': 11, 'cci_len': 10, 'tp_atr_mult': 12.559310448940938}
2025-07-16 23:20:36,926 - root - WARNING - Trial 65 failed - ValueError: Invalid parameters sampled: {'ema_fast': 15, 'ema_slow': 92, 'atr_len': 14, 'vol_sma_len': 12, 'sl_atr_mult': 3.6979781355040284, 'trail_atr_mult': 1.5794788401773683, 'risk_multiplier': 1.686839651161943, 'min_bars_between_trades': 2, 'use_volume_filter': True, 'bb_len': 13, 'bb_std': 2.0302484705774924, 'bb_oversold': 0.23226896990290974, 'bb_overbought': 0.8216670846762016, 'mfi_len': 10, 'rsi_len': 12, 'rsi_oversold': 38.48136658162488, 'rsi_overbought': 65.14479144000968, 'stoch_k': 18, 'stoch_d': 6, 'willr_len': 18, 'cci_len': 24, 'tp_atr_mult': 10.93554051770057}
2025-07-16 23:20:37,013 - root - WARNING - Trial 66 failed - ValueError: Invalid parameters sampled: {'ema_fast': 14, 'ema_slow': 80, 'atr_len': 22, 'vol_sma_len': 25, 'sl_atr_mult': 2.868607110914447, 'trail_atr_mult': 2.687484903478772, 'risk_multiplier': 2.8404963511861223, 'min_bars_between_trades': 2, 'use_volume_filter': False, 'bb_len': 12, 'bb_std': 1.9128148478679214, 'bb_oversold': 0.22277103818411328, 'bb_overbought': 0.8097123761308208, 'mfi_len': 8, 'rsi_len': 13, 'rsi_oversold': 29.125586187281012, 'rsi_overbought': 67.33683579052118, 'stoch_k': 13, 'stoch_d': 8, 'willr_len': 28, 'cci_len': 14, 'tp_atr_mult': 6.033179984004007}
2025-07-16 23:20:37,091 - root - WARNING - Trial 67 failed - ValueError: Invalid parameters sampled: {'ema_fast': 17, 'ema_slow': 56, 'atr_len': 17, 'vol_sma_len': 14, 'sl_atr_mult': 3.3506299861677116, 'trail_atr_mult': 2.4943701634308377, 'risk_multiplier': 1.592385358412493, 'min_bars_between_trades': 1, 'use_volume_filter': True, 'bb_len': 29, 'bb_std': 1.704993849542179, 'bb_oversold': 0.14890402833134514, 'bb_overbought': 0.9757504956960107, 'mfi_len': 11, 'rsi_len': 15, 'rsi_oversold': 20.404457206574254, 'rsi_overbought': 68.42637015536435, 'stoch_k': 15, 'stoch_d': 10, 'willr_len': 29, 'cci_len': 22, 'tp_atr_mult': 12.217157131450143}
2025-07-16 23:20:37,166 - root - WARNING - Trial 68 failed - ValueError: Invalid parameters sampled: {'ema_fast': 11, 'ema_slow': 39, 'atr_len': 20, 'vol_sma_len': 18, 'sl_atr_mult': 2.4862984922840248, 'trail_atr_mult': 1.9009076308980735, 'risk_multiplier': 2.4143248361083307, 'min_bars_between_trades': 2, 'use_volume_filter': False, 'bb_len': 10, 'bb_std': 3.3112553406502396, 'bb_oversold': 0.2499642194879366, 'bb_overbought': 0.8702532634981726, 'mfi_len': 14, 'rsi_len': 18, 'rsi_oversold': 35.5340432736245, 'rsi_overbought': 70.5226083416882, 'stoch_k': 21, 'stoch_d': 4, 'willr_len': 26, 'cci_len': 26, 'tp_atr_mult': 7.820952514150295}
2025-07-16 23:20:37,247 - root - WARNING - Trial 69 failed - ValueError: Invalid parameters sampled: {'ema_fast': 12, 'ema_slow': 62, 'atr_len': 9, 'vol_sma_len': 16, 'sl_atr_mult': 3.523285476858523, 'trail_atr_mult': 1.2678495847160807, 'risk_multiplier': 1.5051739026769466, 'min_bars_between_trades': 2, 'use_volume_filter': False, 'bb_len': 16, 'bb_std': 2.2920630003481395, 'bb_oversold': 0.13135298486947808, 'bb_overbought': 0.7772213583102299, 'mfi_len': 30, 'rsi_len': 11, 'rsi_oversold': 23.56918072056166, 'rsi_overbought': 63.58295710257695, 'stoch_k': 17, 'stoch_d': 7, 'willr_len': 13, 'cci_len': 12, 'tp_atr_mult': 8.596335460070426}
2025-07-16 23:20:37,333 - root - WARNING - Trial 70 failed - ValueError: Invalid parameters sampled: {'ema_fast': 20, 'ema_slow': 53, 'atr_len': 13, 'vol_sma_len': 21, 'sl_atr_mult': 3.431982658835922, 'trail_atr_mult': 2.9481030324429844, 'risk_multiplier': 3.9931644939353443, 'min_bars_between_trades': 1, 'use_volume_filter': True, 'bb_len': 14, 'bb_std': 2.1153051039128252, 'bb_oversold': 0.07711692927686122, 'bb_overbought': 0.8293311105652589, 'mfi_len': 13, 'rsi_len': 16, 'rsi_oversold': 32.204411560434984, 'rsi_overbought': 73.186952633168, 'stoch_k': 16, 'stoch_d': 4, 'willr_len': 16, 'cci_len': 17, 'tp_atr_mult': 13.236573017818522}
2025-07-16 23:20:37,411 - root - WARNING - Trial 71 failed - ValueError: Invalid parameters sampled: {'ema_fast': 23, 'ema_slow': 65, 'atr_len': 16, 'vol_sma_len': 10, 'sl_atr_mult': 3.7481170886858655, 'trail_atr_mult': 1.7485283839171228, 'risk_multiplier': 1.9974840776967175, 'min_bars_between_trades': 1, 'use_volume_filter': False, 'bb_len': 12, 'bb_std': 2.355755040041579, 'bb_oversold': 0.0515947606204243, 'bb_overbought': 0.8983765307665809, 'mfi_len': 9, 'rsi_len': 19, 'rsi_oversold': 28.21706299645661, 'rsi_overbought': 76.41715861192853, 'stoch_k': 8, 'stoch_d': 5, 'willr_len': 9, 'cci_len': 29, 'tp_atr_mult': 13.841697799787902}
2025-07-16 23:20:37,491 - root - WARNING - Trial 72 failed - ValueError: Invalid parameters sampled: {'ema_fast': 16, 'ema_slow': 72, 'atr_len': 19, 'vol_sma_len': 9, 'sl_atr_mult': 3.610730370939822, 'trail_atr_mult': 1.4315397289357288, 'risk_multiplier': 2.1766551334248723, 'min_bars_between_trades': 1, 'use_volume_filter': False, 'bb_len': 11, 'bb_std': 3.166139253926728, 'bb_oversold': 0.034487412999836625, 'bb_overbought': 0.847495474347504, 'mfi_len': 12, 'rsi_len': 20, 'rsi_oversold': 24.821152043746505, 'rsi_overbought': 80.59160484920355, 'stoch_k': 9, 'stoch_d': 6, 'willr_len': 11, 'cci_len': 28, 'tp_atr_mult': 14.611143222855478}
2025-07-16 23:20:37,587 - root - WARNING - Trial 73 failed - ValueError: Invalid parameters sampled: {'ema_fast': 26, 'ema_slow': 78, 'atr_len': 17, 'vol_sma_len': 11, 'sl_atr_mult': 3.958447424626338, 'trail_atr_mult': 2.0677506665983354, 'risk_multiplier': 1.742193612851188, 'min_bars_between_trades': 1, 'use_volume_filter': False, 'bb_len': 10, 'bb_std': 2.456452239090824, 'bb_oversold': 0.07744697154815586, 'bb_overbought': 0.8940147182754044, 'mfi_len': 17, 'rsi_len': 21, 'rsi_oversold': 22.307242313845816, 'rsi_overbought': 82.5300125188045, 'stoch_k': 12, 'stoch_d': 4, 'willr_len': 10, 'cci_len': 30, 'tp_atr_mult': 14.997940171286958}
2025-07-16 23:20:37,675 - root - WARNING - Trial 74 failed - ValueError: Invalid parameters sampled: {'ema_fast': 21, 'ema_slow': 49, 'atr_len': 15, 'vol_sma_len': 9, 'sl_atr_mult': 3.80914444444946, 'trail_atr_mult': 1.8381129785376995, 'risk_multiplier': 1.8200981746329554, 'min_bars_between_trades': 1, 'use_volume_filter': False, 'bb_len': 13, 'bb_std': 1.942573670839798, 'bb_oversold': 0.12058674173869717, 'bb_overbought': 0.9111526598525498, 'mfi_len': 10, 'rsi_len': 22, 'rsi_oversold': 26.484766778909947, 'rsi_overbought': 75.10893165230448, 'stoch_k': 10, 'stoch_d': 5, 'willr_len': 9, 'cci_len': 15, 'tp_atr_mult': 12.883673441322602}
2025-07-16 23:20:37,677 - __main__ - ERROR - Error optimizing BTCUSDT Reversion: Indicator calculation failed: EMA calculation failed: Invalid EMA parameters: fast=14, slow=97 with params: {'ema_fast': 14, 'ema_slow': 97, 'atr_len': 21, 'vol_sma_len': 27, 'sl_atr_mult': 2.312037280884873, 'trail_atr_mult': 1.2991824650758486, 'risk_multiplier': 1.6452090304204987, 'min_bars_between_trades': 2, 'use_volume_filter': False, 'bb_len': 10, 'bb_std': 3.430792659972587, 'bb_oversold': 0.211461807384097, 'bb_overbought': 0.7988379954560035, 'mfi_len': 12, 'rsi_len': 11, 'rsi_oversold': 22.60605607398844, 'rsi_overbought': 73.11891079080594, 'stoch_k': 15, 'stoch_d': 5, 'willr_len': 22, 'cci_len': 12, 'tp_atr_mult': 8.629301836816964}
2025-07-16 23:20:37,677 - __main__ - INFO - Running walk-forward validation for BTCUSDT Reversion
2025-07-16 23:20:37,677 - __main__ - INFO - Total data available: 15.6 days (1500 bars)
2025-07-16 23:20:37,677 - __main__ - INFO - Walk-forward parameters: IS=7d, OOS=3d, step=1d
2025-07-16 23:20:37,681 - root - WARNING - Trial 0 failed - ValueError: Invalid parameters sampled: {'ema_fast': 14, 'ema_slow': 97, 'atr_len': 21, 'vol_sma_len': 27, 'sl_atr_mult': 2.312037280884873, 'trail_atr_mult': 1.2991824650758486, 'risk_multiplier': 1.6452090304204987, 'min_bars_between_trades': 2, 'use_volume_filter': False, 'bb_len': 10, 'bb_std': 3.430792659972587, 'bb_oversold': 0.211461807384097, 'bb_overbought': 0.7988379954560035, 'mfi_len': 12, 'rsi_len': 11, 'rsi_oversold': 22.60605607398844, 'rsi_overbought': 73.11891079080594, 'stoch_k': 15, 'stoch_d': 5, 'willr_len': 22, 'cci_len': 12, 'tp_atr_mult': 8.629301836816964}
2025-07-16 23:20:37,684 - root - WARNING - Trial 1 failed - ValueError: Invalid parameters sampled: {'ema_fast': 14, 'ema_slow': 56, 'atr_len': 22, 'vol_sma_len': 14, 'sl_atr_mult': 3.0284688768272234, 'trail_atr_mult': 2.6957266203585357, 'risk_multiplier': 1.6161260317999944, 'min_bars_between_trades': 2, 'use_volume_filter': True, 'bb_len': 39, 'bb_std': 3.4209536760714867, 'bb_oversold': 0.20593139006678607, 'bb_overbought': 0.8200611669098753, 'mfi_len': 10, 'rsi_len': 20, 'rsi_oversold': 26.003812343490033, 'rsi_overbought': 63.05095587111947, 'stoch_k': 16, 'stoch_d': 3, 'willr_len': 28, 'cci_len': 15, 'tp_atr_mult': 11.962700559185837}
2025-07-16 23:20:37,745 - root - WARNING - Trial 2 failed - ValueError: Invalid parameters sampled: {'ema_fast': 14, 'ema_slow': 95, 'atr_len': 13, 'vol_sma_len': 36, 'sl_atr_mult': 2.0307354746562085, 'trail_atr_mult': 0.9239206398757867, 'risk_multiplier': 2.14481738150373, 'min_bars_between_trades': 1, 'use_volume_filter': False, 'bb_len': 10, 'bb_std': 1.2655000696219345, 'bb_oversold': 0.2375362355939541, 'bb_overbought': 0.7565708902065212, 'mfi_len': 17, 'rsi_len': 8, 'rsi_oversold': 15.814746942728803, 'rsi_overbought': 80.44843192745908, 'stoch_k': 8, 'stoch_d': 7, 'willr_len': 15, 'cci_len': 11, 'tp_atr_mult': 6.1813892914110165}
2025-07-16 23:20:37,802 - root - WARNING - Trial 3 failed - ValueError: Invalid parameters sampled: {'ema_fast': 30, 'ema_slow': 93, 'atr_len': 25, 'vol_sma_len': 29, 'sl_atr_mult': 2.0852934657773803, 'trail_atr_mult': 0.943544912827019, 'risk_multiplier': 3.6937640035674715, 'min_bars_between_trades': 2, 'use_volume_filter': False, 'bb_len': 10, 'bb_std': 3.1972742641305176, 'bb_oversold': 0.0755576345854782, 'bb_overbought': 0.9780423950855321, 'mfi_len': 30, 'rsi_len': 10, 'rsi_oversold': 39.133448651636385, 'rsi_overbought': 71.5528689600282, 'stoch_k': 19, 'stoch_d': 4, 'willr_len': 25, 'cci_len': 29, 'tp_atr_mult': 7.889676577726006}
2025-07-16 23:20:37,862 - root - WARNING - Trial 4 failed - ValueError: Invalid parameters sampled: {'ema_fast': 7, 'ema_slow': 23, 'atr_len': 18, 'vol_sma_len': 19, 'sl_atr_mult': 3.8751223187320454, 'trail_atr_mult': 3.7158658335092807, 'risk_multiplier': 2.8493889497162175, 'min_bars_between_trades': 2, 'use_volume_filter': False, 'bb_len': 24, 'bb_std': 2.1014844925477307, 'bb_oversold': 0.1529886310453433, 'bb_overbought': 0.8614846278645221, 'mfi_len': 8, 'rsi_len': 15, 'rsi_oversold': 22.242215239394206, 'rsi_overbought': 73.32464569145274, 'stoch_k': 13, 'stoch_d': 10, 'willr_len': 9, 'cci_len': 20, 'tp_atr_mult': 14.386351381323008}
2025-07-16 23:20:37,916 - root - WARNING - Trial 5 failed - ValueError: Invalid parameters sampled: {'ema_fast': 24, 'ema_slow': 69, 'atr_len': 9, 'vol_sma_len': 28, 'sl_atr_mult': 2.6674579122027464, 'trail_atr_mult': 1.9529094950277204, 'risk_multiplier': 1.5448278953567365, 'min_bars_between_trades': 1, 'use_volume_filter': True, 'bb_len': 22, 'bb_std': 2.5895646638527587, 'bb_oversold': 0.1560490643290169, 'bb_overbought': 0.7518409991760163, 'mfi_len': 18, 'rsi_len': 15, 'rsi_oversold': 34.38411026886829, 'rsi_overbought': 84.29253187901087, 'stoch_k': 23, 'stoch_d': 6, 'willr_len': 21, 'cci_len': 10, 'tp_atr_mult': 9.542368523815373}
2025-07-16 23:20:37,971 - root - WARNING - Trial 6 failed - ValueError: Invalid parameters sampled: {'ema_fast': 6, 'ema_slow': 67, 'atr_len': 19, 'vol_sma_len': 8, 'sl_atr_mult': 2.6954254354164697, 'trail_atr_mult': 1.9266637712806292, 'risk_multiplier': 2.6213483390545034, 'min_bars_between_trades': 2, 'use_volume_filter': False, 'bb_len': 35, 'bb_std': 2.658246099809679, 'bb_oversold': 0.020149556193761423, 'bb_overbought': 0.9271302422560082, 'mfi_len': 13, 'rsi_len': 25, 'rsi_oversold': 18.31596953777182, 'rsi_overbought': 71.74381125270375, 'stoch_k': 11, 'stoch_d': 6, 'willr_len': 17, 'cci_len': 19, 'tp_atr_mult': 10.23567830176411}
2025-07-16 23:20:38,034 - root - WARNING - Trial 7 failed - ValueError: Invalid parameters sampled: {'ema_fast': 17, 'ema_slow': 39, 'atr_len': 14, 'vol_sma_len': 40, 'sl_atr_mult': 3.401244889339499, 'trail_atr_mult': 1.527786991428475, 'risk_multiplier': 3.9259495122545127, 'min_bars_between_trades': 1, 'use_volume_filter': False, 'bb_len': 17, 'bb_std': 1.904423734458597, 'bb_oversold': 0.1996039033070688, 'bb_overbought': 0.8183029808990523, 'mfi_len': 26, 'rsi_len': 13, 'rsi_oversold': 30.46176168808627, 'rsi_overbought': 60.57185930263159, 'stoch_k': 25, 'stoch_d': 9, 'willr_len': 22, 'cci_len': 25, 'tp_atr_mult': 12.446097577797852}
2025-07-16 23:20:38,075 - root - WARNING - Trial 0 failed - ValueError: Invalid parameters sampled: {'ema_fast': 14, 'ema_slow': 97, 'atr_len': 21, 'vol_sma_len': 27, 'sl_atr_mult': 2.312037280884873, 'trail_atr_mult': 1.2991824650758486, 'risk_multiplier': 1.6452090304204987, 'min_bars_between_trades': 2, 'use_volume_filter': False, 'bb_len': 10, 'bb_std': 3.430792659972587, 'bb_oversold': 0.211461807384097, 'bb_overbought': 0.7988379954560035, 'mfi_len': 12, 'rsi_len': 11, 'rsi_oversold': 22.60605607398844, 'rsi_overbought': 73.11891079080594, 'stoch_k': 15, 'stoch_d': 5, 'willr_len': 22, 'cci_len': 12, 'tp_atr_mult': 8.629301836816964}
2025-07-16 23:20:38,077 - root - WARNING - Trial 1 failed - ValueError: Invalid parameters sampled: {'ema_fast': 14, 'ema_slow': 56, 'atr_len': 22, 'vol_sma_len': 14, 'sl_atr_mult': 3.0284688768272234, 'trail_atr_mult': 2.6957266203585357, 'risk_multiplier': 1.6161260317999944, 'min_bars_between_trades': 2, 'use_volume_filter': True, 'bb_len': 39, 'bb_std': 3.4209536760714867, 'bb_oversold': 0.20593139006678607, 'bb_overbought': 0.8200611669098753, 'mfi_len': 10, 'rsi_len': 20, 'rsi_oversold': 26.003812343490033, 'rsi_overbought': 63.05095587111947, 'stoch_k': 16, 'stoch_d': 3, 'willr_len': 28, 'cci_len': 15, 'tp_atr_mult': 11.962700559185837}
2025-07-16 23:20:38,125 - root - WARNING - Trial 2 failed - ValueError: Invalid parameters sampled: {'ema_fast': 14, 'ema_slow': 95, 'atr_len': 13, 'vol_sma_len': 36, 'sl_atr_mult': 2.0307354746562085, 'trail_atr_mult': 0.9239206398757867, 'risk_multiplier': 2.14481738150373, 'min_bars_between_trades': 1, 'use_volume_filter': False, 'bb_len': 10, 'bb_std': 1.2655000696219345, 'bb_oversold': 0.2375362355939541, 'bb_overbought': 0.7565708902065212, 'mfi_len': 17, 'rsi_len': 8, 'rsi_oversold': 15.814746942728803, 'rsi_overbought': 80.44843192745908, 'stoch_k': 8, 'stoch_d': 7, 'willr_len': 15, 'cci_len': 11, 'tp_atr_mult': 6.1813892914110165}
2025-07-16 23:20:38,177 - root - WARNING - Trial 3 failed - ValueError: Invalid parameters sampled: {'ema_fast': 30, 'ema_slow': 93, 'atr_len': 25, 'vol_sma_len': 29, 'sl_atr_mult': 2.0852934657773803, 'trail_atr_mult': 0.943544912827019, 'risk_multiplier': 3.6937640035674715, 'min_bars_between_trades': 2, 'use_volume_filter': False, 'bb_len': 10, 'bb_std': 3.1972742641305176, 'bb_oversold': 0.0755576345854782, 'bb_overbought': 0.9780423950855321, 'mfi_len': 30, 'rsi_len': 10, 'rsi_oversold': 39.133448651636385, 'rsi_overbought': 71.5528689600282, 'stoch_k': 19, 'stoch_d': 4, 'willr_len': 25, 'cci_len': 29, 'tp_atr_mult': 7.889676577726006}
2025-07-16 23:20:38,230 - root - WARNING - Trial 4 failed - ValueError: Invalid parameters sampled: {'ema_fast': 7, 'ema_slow': 23, 'atr_len': 18, 'vol_sma_len': 19, 'sl_atr_mult': 3.8751223187320454, 'trail_atr_mult': 3.7158658335092807, 'risk_multiplier': 2.8493889497162175, 'min_bars_between_trades': 2, 'use_volume_filter': False, 'bb_len': 24, 'bb_std': 2.1014844925477307, 'bb_oversold': 0.1529886310453433, 'bb_overbought': 0.8614846278645221, 'mfi_len': 8, 'rsi_len': 15, 'rsi_oversold': 22.242215239394206, 'rsi_overbought': 73.32464569145274, 'stoch_k': 13, 'stoch_d': 10, 'willr_len': 9, 'cci_len': 20, 'tp_atr_mult': 14.386351381323008}
2025-07-16 23:20:38,290 - root - WARNING - Trial 5 failed - ValueError: Invalid parameters sampled: {'ema_fast': 24, 'ema_slow': 69, 'atr_len': 9, 'vol_sma_len': 28, 'sl_atr_mult': 2.6674579122027464, 'trail_atr_mult': 1.9529094950277204, 'risk_multiplier': 1.5448278953567365, 'min_bars_between_trades': 1, 'use_volume_filter': True, 'bb_len': 22, 'bb_std': 2.5895646638527587, 'bb_oversold': 0.1560490643290169, 'bb_overbought': 0.7518409991760163, 'mfi_len': 18, 'rsi_len': 15, 'rsi_oversold': 34.38411026886829, 'rsi_overbought': 84.29253187901087, 'stoch_k': 23, 'stoch_d': 6, 'willr_len': 21, 'cci_len': 10, 'tp_atr_mult': 9.542368523815373}
2025-07-16 23:20:38,346 - root - WARNING - Trial 6 failed - ValueError: Invalid parameters sampled: {'ema_fast': 6, 'ema_slow': 67, 'atr_len': 19, 'vol_sma_len': 8, 'sl_atr_mult': 2.6954254354164697, 'trail_atr_mult': 1.9266637712806292, 'risk_multiplier': 2.6213483390545034, 'min_bars_between_trades': 2, 'use_volume_filter': False, 'bb_len': 35, 'bb_std': 2.658246099809679, 'bb_oversold': 0.020149556193761423, 'bb_overbought': 0.9271302422560082, 'mfi_len': 13, 'rsi_len': 25, 'rsi_oversold': 18.31596953777182, 'rsi_overbought': 71.74381125270375, 'stoch_k': 11, 'stoch_d': 6, 'willr_len': 17, 'cci_len': 19, 'tp_atr_mult': 10.23567830176411}
2025-07-16 23:20:38,400 - root - WARNING - Trial 7 failed - ValueError: Invalid parameters sampled: {'ema_fast': 17, 'ema_slow': 39, 'atr_len': 14, 'vol_sma_len': 40, 'sl_atr_mult': 3.401244889339499, 'trail_atr_mult': 1.527786991428475, 'risk_multiplier': 3.9259495122545127, 'min_bars_between_trades': 1, 'use_volume_filter': False, 'bb_len': 17, 'bb_std': 1.904423734458597, 'bb_oversold': 0.1996039033070688, 'bb_overbought': 0.8183029808990523, 'mfi_len': 26, 'rsi_len': 13, 'rsi_oversold': 30.46176168808627, 'rsi_overbought': 60.57185930263159, 'stoch_k': 25, 'stoch_d': 9, 'willr_len': 22, 'cci_len': 25, 'tp_atr_mult': 12.446097577797852}
2025-07-16 23:20:38,439 - root - WARNING - Trial 0 failed - ValueError: Invalid parameters sampled: {'ema_fast': 14, 'ema_slow': 97, 'atr_len': 21, 'vol_sma_len': 27, 'sl_atr_mult': 2.312037280884873, 'trail_atr_mult': 1.2991824650758486, 'risk_multiplier': 1.6452090304204987, 'min_bars_between_trades': 2, 'use_volume_filter': False, 'bb_len': 10, 'bb_std': 3.430792659972587, 'bb_oversold': 0.211461807384097, 'bb_overbought': 0.7988379954560035, 'mfi_len': 12, 'rsi_len': 11, 'rsi_oversold': 22.60605607398844, 'rsi_overbought': 73.11891079080594, 'stoch_k': 15, 'stoch_d': 5, 'willr_len': 22, 'cci_len': 12, 'tp_atr_mult': 8.629301836816964}
2025-07-16 23:20:38,442 - root - WARNING - Trial 1 failed - ValueError: Invalid parameters sampled: {'ema_fast': 14, 'ema_slow': 56, 'atr_len': 22, 'vol_sma_len': 14, 'sl_atr_mult': 3.0284688768272234, 'trail_atr_mult': 2.6957266203585357, 'risk_multiplier': 1.6161260317999944, 'min_bars_between_trades': 2, 'use_volume_filter': True, 'bb_len': 39, 'bb_std': 3.4209536760714867, 'bb_oversold': 0.20593139006678607, 'bb_overbought': 0.8200611669098753, 'mfi_len': 10, 'rsi_len': 20, 'rsi_oversold': 26.003812343490033, 'rsi_overbought': 63.05095587111947, 'stoch_k': 16, 'stoch_d': 3, 'willr_len': 28, 'cci_len': 15, 'tp_atr_mult': 11.962700559185837}
2025-07-16 23:20:38,497 - root - WARNING - Trial 2 failed - ValueError: Invalid parameters sampled: {'ema_fast': 14, 'ema_slow': 95, 'atr_len': 13, 'vol_sma_len': 36, 'sl_atr_mult': 2.0307354746562085, 'trail_atr_mult': 0.9239206398757867, 'risk_multiplier': 2.14481738150373, 'min_bars_between_trades': 1, 'use_volume_filter': False, 'bb_len': 10, 'bb_std': 1.2655000696219345, 'bb_oversold': 0.2375362355939541, 'bb_overbought': 0.7565708902065212, 'mfi_len': 17, 'rsi_len': 8, 'rsi_oversold': 15.814746942728803, 'rsi_overbought': 80.44843192745908, 'stoch_k': 8, 'stoch_d': 7, 'willr_len': 15, 'cci_len': 11, 'tp_atr_mult': 6.1813892914110165}
2025-07-16 23:20:38,559 - root - WARNING - Trial 3 failed - ValueError: Invalid parameters sampled: {'ema_fast': 30, 'ema_slow': 93, 'atr_len': 25, 'vol_sma_len': 29, 'sl_atr_mult': 2.0852934657773803, 'trail_atr_mult': 0.943544912827019, 'risk_multiplier': 3.6937640035674715, 'min_bars_between_trades': 2, 'use_volume_filter': False, 'bb_len': 10, 'bb_std': 3.1972742641305176, 'bb_oversold': 0.0755576345854782, 'bb_overbought': 0.9780423950855321, 'mfi_len': 30, 'rsi_len': 10, 'rsi_oversold': 39.133448651636385, 'rsi_overbought': 71.5528689600282, 'stoch_k': 19, 'stoch_d': 4, 'willr_len': 25, 'cci_len': 29, 'tp_atr_mult': 7.889676577726006}
2025-07-16 23:20:38,610 - root - WARNING - Trial 4 failed - ValueError: Invalid parameters sampled: {'ema_fast': 7, 'ema_slow': 23, 'atr_len': 18, 'vol_sma_len': 19, 'sl_atr_mult': 3.8751223187320454, 'trail_atr_mult': 3.7158658335092807, 'risk_multiplier': 2.8493889497162175, 'min_bars_between_trades': 2, 'use_volume_filter': False, 'bb_len': 24, 'bb_std': 2.1014844925477307, 'bb_oversold': 0.1529886310453433, 'bb_overbought': 0.8614846278645221, 'mfi_len': 8, 'rsi_len': 15, 'rsi_oversold': 22.242215239394206, 'rsi_overbought': 73.32464569145274, 'stoch_k': 13, 'stoch_d': 10, 'willr_len': 9, 'cci_len': 20, 'tp_atr_mult': 14.386351381323008}
2025-07-16 23:20:38,662 - root - WARNING - Trial 5 failed - ValueError: Invalid parameters sampled: {'ema_fast': 24, 'ema_slow': 69, 'atr_len': 9, 'vol_sma_len': 28, 'sl_atr_mult': 2.6674579122027464, 'trail_atr_mult': 1.9529094950277204, 'risk_multiplier': 1.5448278953567365, 'min_bars_between_trades': 1, 'use_volume_filter': True, 'bb_len': 22, 'bb_std': 2.5895646638527587, 'bb_oversold': 0.1560490643290169, 'bb_overbought': 0.7518409991760163, 'mfi_len': 18, 'rsi_len': 15, 'rsi_oversold': 34.38411026886829, 'rsi_overbought': 84.29253187901087, 'stoch_k': 23, 'stoch_d': 6, 'willr_len': 21, 'cci_len': 10, 'tp_atr_mult': 9.542368523815373}
2025-07-16 23:20:38,718 - root - WARNING - Trial 6 failed - ValueError: Invalid parameters sampled: {'ema_fast': 6, 'ema_slow': 67, 'atr_len': 19, 'vol_sma_len': 8, 'sl_atr_mult': 2.6954254354164697, 'trail_atr_mult': 1.9266637712806292, 'risk_multiplier': 2.6213483390545034, 'min_bars_between_trades': 2, 'use_volume_filter': False, 'bb_len': 35, 'bb_std': 2.658246099809679, 'bb_oversold': 0.020149556193761423, 'bb_overbought': 0.9271302422560082, 'mfi_len': 13, 'rsi_len': 25, 'rsi_oversold': 18.31596953777182, 'rsi_overbought': 71.74381125270375, 'stoch_k': 11, 'stoch_d': 6, 'willr_len': 17, 'cci_len': 19, 'tp_atr_mult': 10.23567830176411}
2025-07-16 23:20:38,773 - root - WARNING - Trial 7 failed - ValueError: Invalid parameters sampled: {'ema_fast': 17, 'ema_slow': 39, 'atr_len': 14, 'vol_sma_len': 40, 'sl_atr_mult': 3.401244889339499, 'trail_atr_mult': 1.527786991428475, 'risk_multiplier': 3.9259495122545127, 'min_bars_between_trades': 1, 'use_volume_filter': False, 'bb_len': 17, 'bb_std': 1.904423734458597, 'bb_oversold': 0.1996039033070688, 'bb_overbought': 0.8183029808990523, 'mfi_len': 26, 'rsi_len': 13, 'rsi_oversold': 30.46176168808627, 'rsi_overbought': 60.57185930263159, 'stoch_k': 25, 'stoch_d': 9, 'willr_len': 22, 'cci_len': 25, 'tp_atr_mult': 12.446097577797852}
2025-07-16 23:20:38,819 - root - WARNING - Trial 0 failed - ValueError: Invalid parameters sampled: {'ema_fast': 14, 'ema_slow': 97, 'atr_len': 21, 'vol_sma_len': 27, 'sl_atr_mult': 2.312037280884873, 'trail_atr_mult': 1.2991824650758486, 'risk_multiplier': 1.6452090304204987, 'min_bars_between_trades': 2, 'use_volume_filter': False, 'bb_len': 10, 'bb_std': 3.430792659972587, 'bb_oversold': 0.211461807384097, 'bb_overbought': 0.7988379954560035, 'mfi_len': 12, 'rsi_len': 11, 'rsi_oversold': 22.60605607398844, 'rsi_overbought': 73.11891079080594, 'stoch_k': 15, 'stoch_d': 5, 'willr_len': 22, 'cci_len': 12, 'tp_atr_mult': 8.629301836816964}
2025-07-16 23:20:38,823 - root - WARNING - Trial 1 failed - ValueError: Invalid parameters sampled: {'ema_fast': 14, 'ema_slow': 56, 'atr_len': 22, 'vol_sma_len': 14, 'sl_atr_mult': 3.0284688768272234, 'trail_atr_mult': 2.6957266203585357, 'risk_multiplier': 1.6161260317999944, 'min_bars_between_trades': 2, 'use_volume_filter': True, 'bb_len': 39, 'bb_std': 3.4209536760714867, 'bb_oversold': 0.20593139006678607, 'bb_overbought': 0.8200611669098753, 'mfi_len': 10, 'rsi_len': 20, 'rsi_oversold': 26.003812343490033, 'rsi_overbought': 63.05095587111947, 'stoch_k': 16, 'stoch_d': 3, 'willr_len': 28, 'cci_len': 15, 'tp_atr_mult': 11.962700559185837}
2025-07-16 23:20:38,873 - root - WARNING - Trial 2 failed - ValueError: Invalid parameters sampled: {'ema_fast': 14, 'ema_slow': 95, 'atr_len': 13, 'vol_sma_len': 36, 'sl_atr_mult': 2.0307354746562085, 'trail_atr_mult': 0.9239206398757867, 'risk_multiplier': 2.14481738150373, 'min_bars_between_trades': 1, 'use_volume_filter': False, 'bb_len': 10, 'bb_std': 1.2655000696219345, 'bb_oversold': 0.2375362355939541, 'bb_overbought': 0.7565708902065212, 'mfi_len': 17, 'rsi_len': 8, 'rsi_oversold': 15.814746942728803, 'rsi_overbought': 80.44843192745908, 'stoch_k': 8, 'stoch_d': 7, 'willr_len': 15, 'cci_len': 11, 'tp_atr_mult': 6.1813892914110165}
2025-07-16 23:20:38,925 - root - WARNING - Trial 3 failed - ValueError: Invalid parameters sampled: {'ema_fast': 30, 'ema_slow': 93, 'atr_len': 25, 'vol_sma_len': 29, 'sl_atr_mult': 2.0852934657773803, 'trail_atr_mult': 0.943544912827019, 'risk_multiplier': 3.6937640035674715, 'min_bars_between_trades': 2, 'use_volume_filter': False, 'bb_len': 10, 'bb_std': 3.1972742641305176, 'bb_oversold': 0.0755576345854782, 'bb_overbought': 0.9780423950855321, 'mfi_len': 30, 'rsi_len': 10, 'rsi_oversold': 39.133448651636385, 'rsi_overbought': 71.5528689600282, 'stoch_k': 19, 'stoch_d': 4, 'willr_len': 25, 'cci_len': 29, 'tp_atr_mult': 7.889676577726006}
2025-07-16 23:20:38,978 - root - WARNING - Trial 4 failed - ValueError: Invalid parameters sampled: {'ema_fast': 7, 'ema_slow': 23, 'atr_len': 18, 'vol_sma_len': 19, 'sl_atr_mult': 3.8751223187320454, 'trail_atr_mult': 3.7158658335092807, 'risk_multiplier': 2.8493889497162175, 'min_bars_between_trades': 2, 'use_volume_filter': False, 'bb_len': 24, 'bb_std': 2.1014844925477307, 'bb_oversold': 0.1529886310453433, 'bb_overbought': 0.8614846278645221, 'mfi_len': 8, 'rsi_len': 15, 'rsi_oversold': 22.242215239394206, 'rsi_overbought': 73.32464569145274, 'stoch_k': 13, 'stoch_d': 10, 'willr_len': 9, 'cci_len': 20, 'tp_atr_mult': 14.386351381323008}
2025-07-16 23:20:39,031 - root - WARNING - Trial 5 failed - ValueError: Invalid parameters sampled: {'ema_fast': 24, 'ema_slow': 69, 'atr_len': 9, 'vol_sma_len': 28, 'sl_atr_mult': 2.6674579122027464, 'trail_atr_mult': 1.9529094950277204, 'risk_multiplier': 1.5448278953567365, 'min_bars_between_trades': 1, 'use_volume_filter': True, 'bb_len': 22, 'bb_std': 2.5895646638527587, 'bb_oversold': 0.1560490643290169, 'bb_overbought': 0.7518409991760163, 'mfi_len': 18, 'rsi_len': 15, 'rsi_oversold': 34.38411026886829, 'rsi_overbought': 84.29253187901087, 'stoch_k': 23, 'stoch_d': 6, 'willr_len': 21, 'cci_len': 10, 'tp_atr_mult': 9.542368523815373}
2025-07-16 23:20:39,096 - root - WARNING - Trial 6 failed - ValueError: Invalid parameters sampled: {'ema_fast': 6, 'ema_slow': 67, 'atr_len': 19, 'vol_sma_len': 8, 'sl_atr_mult': 2.6954254354164697, 'trail_atr_mult': 1.9266637712806292, 'risk_multiplier': 2.6213483390545034, 'min_bars_between_trades': 2, 'use_volume_filter': False, 'bb_len': 35, 'bb_std': 2.658246099809679, 'bb_oversold': 0.020149556193761423, 'bb_overbought': 0.9271302422560082, 'mfi_len': 13, 'rsi_len': 25, 'rsi_oversold': 18.31596953777182, 'rsi_overbought': 71.74381125270375, 'stoch_k': 11, 'stoch_d': 6, 'willr_len': 17, 'cci_len': 19, 'tp_atr_mult': 10.23567830176411}
2025-07-16 23:20:39,151 - root - WARNING - Trial 7 failed - ValueError: Invalid parameters sampled: {'ema_fast': 17, 'ema_slow': 39, 'atr_len': 14, 'vol_sma_len': 40, 'sl_atr_mult': 3.401244889339499, 'trail_atr_mult': 1.527786991428475, 'risk_multiplier': 3.9259495122545127, 'min_bars_between_trades': 1, 'use_volume_filter': False, 'bb_len': 17, 'bb_std': 1.904423734458597, 'bb_oversold': 0.1996039033070688, 'bb_overbought': 0.8183029808990523, 'mfi_len': 26, 'rsi_len': 13, 'rsi_oversold': 30.46176168808627, 'rsi_overbought': 60.57185930263159, 'stoch_k': 25, 'stoch_d': 9, 'willr_len': 22, 'cci_len': 25, 'tp_atr_mult': 12.446097577797852}
2025-07-16 23:20:39,192 - root - WARNING - Trial 0 failed - ValueError: Invalid parameters sampled: {'ema_fast': 14, 'ema_slow': 97, 'atr_len': 21, 'vol_sma_len': 27, 'sl_atr_mult': 2.312037280884873, 'trail_atr_mult': 1.2991824650758486, 'risk_multiplier': 1.6452090304204987, 'min_bars_between_trades': 2, 'use_volume_filter': False, 'bb_len': 10, 'bb_std': 3.430792659972587, 'bb_oversold': 0.211461807384097, 'bb_overbought': 0.7988379954560035, 'mfi_len': 12, 'rsi_len': 11, 'rsi_oversold': 22.60605607398844, 'rsi_overbought': 73.11891079080594, 'stoch_k': 15, 'stoch_d': 5, 'willr_len': 22, 'cci_len': 12, 'tp_atr_mult': 8.629301836816964}
2025-07-16 23:20:39,193 - root - WARNING - Trial 1 failed - ValueError: Invalid parameters sampled: {'ema_fast': 14, 'ema_slow': 56, 'atr_len': 22, 'vol_sma_len': 14, 'sl_atr_mult': 3.0284688768272234, 'trail_atr_mult': 2.6957266203585357, 'risk_multiplier': 1.6161260317999944, 'min_bars_between_trades': 2, 'use_volume_filter': True, 'bb_len': 39, 'bb_std': 3.4209536760714867, 'bb_oversold': 0.20593139006678607, 'bb_overbought': 0.8200611669098753, 'mfi_len': 10, 'rsi_len': 20, 'rsi_oversold': 26.003812343490033, 'rsi_overbought': 63.05095587111947, 'stoch_k': 16, 'stoch_d': 3, 'willr_len': 28, 'cci_len': 15, 'tp_atr_mult': 11.962700559185837}
2025-07-16 23:20:39,245 - root - WARNING - Trial 2 failed - ValueError: Invalid parameters sampled: {'ema_fast': 14, 'ema_slow': 95, 'atr_len': 13, 'vol_sma_len': 36, 'sl_atr_mult': 2.0307354746562085, 'trail_atr_mult': 0.9239206398757867, 'risk_multiplier': 2.14481738150373, 'min_bars_between_trades': 1, 'use_volume_filter': False, 'bb_len': 10, 'bb_std': 1.2655000696219345, 'bb_oversold': 0.2375362355939541, 'bb_overbought': 0.7565708902065212, 'mfi_len': 17, 'rsi_len': 8, 'rsi_oversold': 15.814746942728803, 'rsi_overbought': 80.44843192745908, 'stoch_k': 8, 'stoch_d': 7, 'willr_len': 15, 'cci_len': 11, 'tp_atr_mult': 6.1813892914110165}
2025-07-16 23:20:39,296 - root - WARNING - Trial 3 failed - ValueError: Invalid parameters sampled: {'ema_fast': 30, 'ema_slow': 93, 'atr_len': 25, 'vol_sma_len': 29, 'sl_atr_mult': 2.0852934657773803, 'trail_atr_mult': 0.943544912827019, 'risk_multiplier': 3.6937640035674715, 'min_bars_between_trades': 2, 'use_volume_filter': False, 'bb_len': 10, 'bb_std': 3.1972742641305176, 'bb_oversold': 0.0755576345854782, 'bb_overbought': 0.9780423950855321, 'mfi_len': 30, 'rsi_len': 10, 'rsi_oversold': 39.133448651636385, 'rsi_overbought': 71.5528689600282, 'stoch_k': 19, 'stoch_d': 4, 'willr_len': 25, 'cci_len': 29, 'tp_atr_mult': 7.889676577726006}
2025-07-16 23:20:39,361 - root - WARNING - Trial 4 failed - ValueError: Invalid parameters sampled: {'ema_fast': 7, 'ema_slow': 23, 'atr_len': 18, 'vol_sma_len': 19, 'sl_atr_mult': 3.8751223187320454, 'trail_atr_mult': 3.7158658335092807, 'risk_multiplier': 2.8493889497162175, 'min_bars_between_trades': 2, 'use_volume_filter': False, 'bb_len': 24, 'bb_std': 2.1014844925477307, 'bb_oversold': 0.1529886310453433, 'bb_overbought': 0.8614846278645221, 'mfi_len': 8, 'rsi_len': 15, 'rsi_oversold': 22.242215239394206, 'rsi_overbought': 73.32464569145274, 'stoch_k': 13, 'stoch_d': 10, 'willr_len': 9, 'cci_len': 20, 'tp_atr_mult': 14.386351381323008}
2025-07-16 23:20:39,424 - root - WARNING - Trial 5 failed - ValueError: Invalid parameters sampled: {'ema_fast': 24, 'ema_slow': 69, 'atr_len': 9, 'vol_sma_len': 28, 'sl_atr_mult': 2.6674579122027464, 'trail_atr_mult': 1.9529094950277204, 'risk_multiplier': 1.5448278953567365, 'min_bars_between_trades': 1, 'use_volume_filter': True, 'bb_len': 22, 'bb_std': 2.5895646638527587, 'bb_oversold': 0.1560490643290169, 'bb_overbought': 0.7518409991760163, 'mfi_len': 18, 'rsi_len': 15, 'rsi_oversold': 34.38411026886829, 'rsi_overbought': 84.29253187901087, 'stoch_k': 23, 'stoch_d': 6, 'willr_len': 21, 'cci_len': 10, 'tp_atr_mult': 9.542368523815373}
2025-07-16 23:20:39,482 - root - WARNING - Trial 6 failed - ValueError: Invalid parameters sampled: {'ema_fast': 6, 'ema_slow': 67, 'atr_len': 19, 'vol_sma_len': 8, 'sl_atr_mult': 2.6954254354164697, 'trail_atr_mult': 1.9266637712806292, 'risk_multiplier': 2.6213483390545034, 'min_bars_between_trades': 2, 'use_volume_filter': False, 'bb_len': 35, 'bb_std': 2.658246099809679, 'bb_oversold': 0.020149556193761423, 'bb_overbought': 0.9271302422560082, 'mfi_len': 13, 'rsi_len': 25, 'rsi_oversold': 18.31596953777182, 'rsi_overbought': 71.74381125270375, 'stoch_k': 11, 'stoch_d': 6, 'willr_len': 17, 'cci_len': 19, 'tp_atr_mult': 10.23567830176411}
2025-07-16 23:20:39,539 - root - WARNING - Trial 7 failed - ValueError: Invalid parameters sampled: {'ema_fast': 17, 'ema_slow': 39, 'atr_len': 14, 'vol_sma_len': 40, 'sl_atr_mult': 3.401244889339499, 'trail_atr_mult': 1.527786991428475, 'risk_multiplier': 3.9259495122545127, 'min_bars_between_trades': 1, 'use_volume_filter': False, 'bb_len': 17, 'bb_std': 1.904423734458597, 'bb_oversold': 0.1996039033070688, 'bb_overbought': 0.8183029808990523, 'mfi_len': 26, 'rsi_len': 13, 'rsi_oversold': 30.46176168808627, 'rsi_overbought': 60.57185930263159, 'stoch_k': 25, 'stoch_d': 9, 'willr_len': 22, 'cci_len': 25, 'tp_atr_mult': 12.446097577797852}
2025-07-16 23:20:39,586 - root - WARNING - Trial 0 failed - ValueError: Invalid parameters sampled: {'ema_fast': 14, 'ema_slow': 97, 'atr_len': 21, 'vol_sma_len': 27, 'sl_atr_mult': 2.312037280884873, 'trail_atr_mult': 1.2991824650758486, 'risk_multiplier': 1.6452090304204987, 'min_bars_between_trades': 2, 'use_volume_filter': False, 'bb_len': 10, 'bb_std': 3.430792659972587, 'bb_oversold': 0.211461807384097, 'bb_overbought': 0.7988379954560035, 'mfi_len': 12, 'rsi_len': 11, 'rsi_oversold': 22.60605607398844, 'rsi_overbought': 73.11891079080594, 'stoch_k': 15, 'stoch_d': 5, 'willr_len': 22, 'cci_len': 12, 'tp_atr_mult': 8.629301836816964}
2025-07-16 23:20:39,589 - root - WARNING - Trial 1 failed - ValueError: Invalid parameters sampled: {'ema_fast': 14, 'ema_slow': 56, 'atr_len': 22, 'vol_sma_len': 14, 'sl_atr_mult': 3.0284688768272234, 'trail_atr_mult': 2.6957266203585357, 'risk_multiplier': 1.6161260317999944, 'min_bars_between_trades': 2, 'use_volume_filter': True, 'bb_len': 39, 'bb_std': 3.4209536760714867, 'bb_oversold': 0.20593139006678607, 'bb_overbought': 0.8200611669098753, 'mfi_len': 10, 'rsi_len': 20, 'rsi_oversold': 26.003812343490033, 'rsi_overbought': 63.05095587111947, 'stoch_k': 16, 'stoch_d': 3, 'willr_len': 28, 'cci_len': 15, 'tp_atr_mult': 11.962700559185837}
2025-07-16 23:20:39,644 - root - WARNING - Trial 2 failed - ValueError: Invalid parameters sampled: {'ema_fast': 14, 'ema_slow': 95, 'atr_len': 13, 'vol_sma_len': 36, 'sl_atr_mult': 2.0307354746562085, 'trail_atr_mult': 0.9239206398757867, 'risk_multiplier': 2.14481738150373, 'min_bars_between_trades': 1, 'use_volume_filter': False, 'bb_len': 10, 'bb_std': 1.2655000696219345, 'bb_oversold': 0.2375362355939541, 'bb_overbought': 0.7565708902065212, 'mfi_len': 17, 'rsi_len': 8, 'rsi_oversold': 15.814746942728803, 'rsi_overbought': 80.44843192745908, 'stoch_k': 8, 'stoch_d': 7, 'willr_len': 15, 'cci_len': 11, 'tp_atr_mult': 6.1813892914110165}
2025-07-16 23:20:39,694 - root - WARNING - Trial 3 failed - ValueError: Invalid parameters sampled: {'ema_fast': 30, 'ema_slow': 93, 'atr_len': 25, 'vol_sma_len': 29, 'sl_atr_mult': 2.0852934657773803, 'trail_atr_mult': 0.943544912827019, 'risk_multiplier': 3.6937640035674715, 'min_bars_between_trades': 2, 'use_volume_filter': False, 'bb_len': 10, 'bb_std': 3.1972742641305176, 'bb_oversold': 0.0755576345854782, 'bb_overbought': 0.9780423950855321, 'mfi_len': 30, 'rsi_len': 10, 'rsi_oversold': 39.133448651636385, 'rsi_overbought': 71.5528689600282, 'stoch_k': 19, 'stoch_d': 4, 'willr_len': 25, 'cci_len': 29, 'tp_atr_mult': 7.889676577726006}
2025-07-16 23:20:39,746 - root - WARNING - Trial 4 failed - ValueError: Invalid parameters sampled: {'ema_fast': 7, 'ema_slow': 23, 'atr_len': 18, 'vol_sma_len': 19, 'sl_atr_mult': 3.8751223187320454, 'trail_atr_mult': 3.7158658335092807, 'risk_multiplier': 2.8493889497162175, 'min_bars_between_trades': 2, 'use_volume_filter': False, 'bb_len': 24, 'bb_std': 2.1014844925477307, 'bb_oversold': 0.1529886310453433, 'bb_overbought': 0.8614846278645221, 'mfi_len': 8, 'rsi_len': 15, 'rsi_oversold': 22.242215239394206, 'rsi_overbought': 73.32464569145274, 'stoch_k': 13, 'stoch_d': 10, 'willr_len': 9, 'cci_len': 20, 'tp_atr_mult': 14.386351381323008}
2025-07-16 23:20:39,801 - root - WARNING - Trial 5 failed - ValueError: Invalid parameters sampled: {'ema_fast': 24, 'ema_slow': 69, 'atr_len': 9, 'vol_sma_len': 28, 'sl_atr_mult': 2.6674579122027464, 'trail_atr_mult': 1.9529094950277204, 'risk_multiplier': 1.5448278953567365, 'min_bars_between_trades': 1, 'use_volume_filter': True, 'bb_len': 22, 'bb_std': 2.5895646638527587, 'bb_oversold': 0.1560490643290169, 'bb_overbought': 0.7518409991760163, 'mfi_len': 18, 'rsi_len': 15, 'rsi_oversold': 34.38411026886829, 'rsi_overbought': 84.29253187901087, 'stoch_k': 23, 'stoch_d': 6, 'willr_len': 21, 'cci_len': 10, 'tp_atr_mult': 9.542368523815373}
2025-07-16 23:20:39,861 - root - WARNING - Trial 6 failed - ValueError: Invalid parameters sampled: {'ema_fast': 6, 'ema_slow': 67, 'atr_len': 19, 'vol_sma_len': 8, 'sl_atr_mult': 2.6954254354164697, 'trail_atr_mult': 1.9266637712806292, 'risk_multiplier': 2.6213483390545034, 'min_bars_between_trades': 2, 'use_volume_filter': False, 'bb_len': 35, 'bb_std': 2.658246099809679, 'bb_oversold': 0.020149556193761423, 'bb_overbought': 0.9271302422560082, 'mfi_len': 13, 'rsi_len': 25, 'rsi_oversold': 18.31596953777182, 'rsi_overbought': 71.74381125270375, 'stoch_k': 11, 'stoch_d': 6, 'willr_len': 17, 'cci_len': 19, 'tp_atr_mult': 10.23567830176411}
2025-07-16 23:20:39,914 - root - WARNING - Trial 7 failed - ValueError: Invalid parameters sampled: {'ema_fast': 17, 'ema_slow': 39, 'atr_len': 14, 'vol_sma_len': 40, 'sl_atr_mult': 3.401244889339499, 'trail_atr_mult': 1.527786991428475, 'risk_multiplier': 3.9259495122545127, 'min_bars_between_trades': 1, 'use_volume_filter': False, 'bb_len': 17, 'bb_std': 1.904423734458597, 'bb_oversold': 0.1996039033070688, 'bb_overbought': 0.8183029808990523, 'mfi_len': 26, 'rsi_len': 13, 'rsi_oversold': 30.46176168808627, 'rsi_overbought': 60.57185930263159, 'stoch_k': 25, 'stoch_d': 9, 'willr_len': 22, 'cci_len': 25, 'tp_atr_mult': 12.446097577797852}
2025-07-16 23:20:39,957 - __main__ - INFO - BTCUSDT Reversion: 6 valid WF windows, WF score = 0.200
2025-07-16 23:20:39,957 - __main__ - INFO - Optimizing Adaptive strategy for BTCUSDT
2025-07-16 23:20:39,973 - root - WARNING - Trial 0 failed - ValueError: Indicator calculation failed: EMA calculation failed: Invalid EMA parameters: fast=14, slow=97 with params: {'ema_fast': 14, 'ema_slow': 97, 'atr_len': 21, 'vol_sma_len': 27, 'sl_atr_mult': 2.312037280884873, 'trail_atr_mult': 1.2991824650758486, 'risk_multiplier': 1.6452090304204987, 'min_bars_between_trades': 2, 'use_volume_filter': False, 'st_len': 5, 'st_mult': 4.89468448256698, 'adx_len': 22, 'adx_threshold': 20.308477766956905, 'trend_confirmations': 1, 'macd_fast': 8, 'macd_slow': 28, 'macd_signal': 10, 'rsi_len': 15, 'bb_len': 19, 'bb_std': 2.6072616578614727, 'bb_oversold': 0.05208358794996962, 'bb_overbought': 0.8171932691631002, 'mfi_len': 16, 'rsi_oversold': 26.4017496054259, 'rsi_overbought': 79.62939903482534, 'stoch_k': 11, 'stoch_d': 7, 'willr_len': 21, 'cci_len': 10, 'tp_atr_mult': 11.467903667112946}
2025-07-16 23:20:40,041 - root - WARNING - Trial 3 failed - ValueError: Indicator calculation failed: EMA calculation failed: Invalid EMA parameters: fast=19, slow=82 with params: {'ema_fast': 19, 'ema_slow': 82, 'atr_len': 16, 'vol_sma_len': 25, 'sl_atr_mult': 2.855082036717099, 'trail_atr_mult': 0.8813412055811046, 'risk_multiplier': 1.769728567483261, 'min_bars_between_trades': 1, 'use_volume_filter': True, 'st_len': 23, 'st_mult': 4.6764826587413255, 'adx_len': 12, 'adx_threshold': 25.259573075890742, 'trend_confirmations': 2, 'macd_fast': 9, 'macd_slow': 20, 'macd_signal': 8, 'rsi_len': 10, 'bb_len': 38, 'bb_std': 3.058676872998159, 'bb_oversold': 0.1656828639973974, 'bb_overbought': 0.9504359357431751, 'mfi_len': 26, 'rsi_oversold': 19.664251472150895, 'rsi_overbought': 82.31397496224945, 'stoch_k': 17, 'stoch_d': 9, 'willr_len': 28, 'cci_len': 16, 'tp_atr_mult': 6.990467320749091}
2025-07-16 23:20:40,071 - root - WARNING - Trial 5 failed - ValueError: Indicator calculation failed: EMA calculation failed: Invalid EMA parameters: fast=11, slow=74 with params: {'ema_fast': 11, 'ema_slow': 74, 'atr_len': 21, 'vol_sma_len': 15, 'sl_atr_mult': 3.456432697223719, 'trail_atr_mult': 1.9769060247016104, 'risk_multiplier': 3.080764576483949, 'min_bars_between_trades': 2, 'use_volume_filter': True, 'st_len': 35, 'st_mult': 2.6227302274010755, 'adx_len': 11, 'adx_threshold': 16.0193785388691, 'trend_confirmations': 2, 'macd_fast': 16, 'macd_slow': 18, 'macd_signal': 10, 'rsi_len': 12, 'bb_len': 30, 'bb_std': 1.6010427867114803, 'bb_oversold': 0.17891567976356718, 'bb_overbought': 0.8389491296491236, 'mfi_len': 29, 'rsi_oversold': 18.43802360364983, 'rsi_overbought': 68.52665877625647, 'stoch_k': 10, 'stoch_d': 10, 'willr_len': 28, 'cci_len': 15, 'tp_atr_mult': 11.939856414307611}
2025-07-16 23:20:40,077 - root - WARNING - Trial 6 failed - ValueError: Indicator calculation failed: EMA calculation failed: Invalid EMA parameters: fast=26, slow=64 with params: {'ema_fast': 26, 'ema_slow': 64, 'atr_len': 17, 'vol_sma_len': 15, 'sl_atr_mult': 2.1862055356117986, 'trail_atr_mult': 3.671090425450646, 'risk_multiplier': 3.751045142908326, 'min_bars_between_trades': 2, 'use_volume_filter': False, 'st_len': 31, 'st_mult': 4.639885909834019, 'adx_len': 23, 'adx_threshold': 34.496888646440595, 'trend_confirmations': 2, 'macd_fast': 7, 'macd_slow': 23, 'macd_signal': 14, 'rsi_len': 18, 'bb_len': 10, 'bb_std': 1.4333845485918737, 'bb_oversold': 0.17260540689485282, 'bb_overbought': 0.7511641642846303, 'mfi_len': 11, 'rsi_oversold': 28.718344734164653, 'rsi_overbought': 77.29737994231733, 'stoch_k': 19, 'stoch_d': 4, 'willr_len': 24, 'cci_len': 14, 'tp_atr_mult': 8.92859728343341}
2025-07-16 23:20:40,084 - root - WARNING - Trial 7 failed - ValueError: Indicator calculation failed: EMA calculation failed: Invalid EMA parameters: fast=24, slow=72 with params: {'ema_fast': 24, 'ema_slow': 72, 'atr_len': 23, 'vol_sma_len': 29, 'sl_atr_mult': 3.1366172066709432, 'trail_atr_mult': 1.099759257049896, 'risk_multiplier': 2.4192895076485836, 'min_bars_between_trades': 1, 'use_volume_filter': False, 'st_len': 19, 'st_mult': 4.622162943119896, 'adx_len': 19, 'adx_threshold': 34.870282588541215, 'trend_confirmations': 2, 'macd_fast': 14, 'macd_slow': 34, 'macd_signal': 7, 'rsi_len': 21, 'bb_len': 18, 'bb_std': 1.2559267227923439, 'bb_oversold': 0.16845862805864859, 'bb_overbought': 0.7907354562636213, 'mfi_len': 29, 'rsi_oversold': 38.84821442506468, 'rsi_overbought': 82.87160975551122, 'stoch_k': 14, 'stoch_d': 3, 'willr_len': 29, 'cci_len': 18, 'tp_atr_mult': 14.699893371393026}
2025-07-16 23:20:40,098 - root - WARNING - Trial 8 failed - ValueError: Indicator calculation failed: EMA calculation failed: Invalid EMA parameters: fast=30, slow=89 with params: {'ema_fast': 30, 'ema_slow': 89, 'atr_len': 13, 'vol_sma_len': 20, 'sl_atr_mult': 3.702273343033714, 'trail_atr_mult': 1.8141504165000886, 'risk_multiplier': 1.9237318667152312, 'min_bars_between_trades': 2, 'use_volume_filter': True, 'st_len': 25, 'st_mult': 1.84011772819769, 'adx_len': 19, 'adx_threshold': 39.75134625260658, 'trend_confirmations': 1, 'macd_fast': 13, 'macd_slow': 46, 'macd_signal': 13, 'rsi_len': 20, 'bb_len': 31, 'bb_std': 2.0268296478054366, 'bb_oversold': 0.08752612418083348, 'bb_overbought': 0.9361530657600581, 'mfi_len': 26, 'rsi_oversold': 36.67680796450259, 'rsi_overbought': 82.83101381391178, 'stoch_k': 17, 'stoch_d': 7, 'willr_len': 26, 'cci_len': 23, 'tp_atr_mult': 12.317701895319331}
2025-07-16 23:20:40,107 - root - WARNING - Trial 9 failed - ValueError: Indicator calculation failed: EMA calculation failed: Invalid EMA parameters: fast=25, slow=92 with params: {'ema_fast': 25, 'ema_slow': 92, 'atr_len': 14, 'vol_sma_len': 20, 'sl_atr_mult': 2.187963879681738, 'trail_atr_mult': 2.650496451187757, 'risk_multiplier': 1.5898556844918552, 'min_bars_between_trades': 1, 'use_volume_filter': True, 'st_len': 26, 'st_mult': 1.606750874786673, 'adx_len': 8, 'adx_threshold': 35.56501401649146, 'trend_confirmations': 1, 'macd_fast': 7, 'macd_slow': 35, 'macd_signal': 13, 'rsi_len': 11, 'bb_len': 29, 'bb_std': 1.3962991694856663, 'bb_oversold': 0.03188679586877977, 'bb_overbought': 0.872211565260674, 'mfi_len': 20, 'rsi_oversold': 30.935747537455164, 'rsi_overbought': 78.15228334306654, 'stoch_k': 25, 'stoch_d': 7, 'willr_len': 15, 'cci_len': 26, 'tp_atr_mult': 8.437490261358668}
2025-07-16 23:20:42,039 - root - WARNING - Trial 29 failed - ValueError: Indicator calculation failed: EMA calculation failed: Invalid EMA parameters: fast=13, slow=63 with params: {'ema_fast': 13, 'ema_slow': 63, 'atr_len': 14, 'vol_sma_len': 27, 'sl_atr_mult': 3.524603771330079, 'trail_atr_mult': 2.941148560530836, 'risk_multiplier': 1.693854606499485, 'min_bars_between_trades': 1, 'use_volume_filter': False, 'st_len': 28, 'st_mult': 4.866912803633598, 'adx_len': 23, 'adx_threshold': 20.255797965667824, 'trend_confirmations': 1, 'macd_fast': 8, 'macd_slow': 33, 'macd_signal': 10, 'rsi_len': 15, 'bb_len': 20, 'bb_std': 2.7565653237635717, 'bb_oversold': 0.15582197089159375, 'bb_overbought': 0.8185979393247147, 'mfi_len': 24, 'rsi_oversold': 26.87322095804566, 'rsi_overbought': 61.30022817084447, 'stoch_k': 11, 'stoch_d': 7, 'willr_len': 23, 'cci_len': 10, 'tp_atr_mult': 11.095636397847667}
2025-07-16 23:20:42,141 - root - WARNING - Trial 30 failed - ValueError: Invalid parameters sampled: {'ema_fast': 23, 'ema_slow': 20, 'atr_len': 18, 'vol_sma_len': 17, 'sl_atr_mult': 3.316196505327287, 'trail_atr_mult': 3.4450385950415705, 'risk_multiplier': 1.5240060622604958, 'min_bars_between_trades': 2, 'use_volume_filter': True, 'st_len': 40, 'st_mult': 3.348101164112054, 'adx_len': 21, 'adx_threshold': 26.20569496577963, 'trend_confirmations': 2, 'macd_fast': 8, 'macd_slow': 24, 'macd_signal': 15, 'rsi_len': 13, 'bb_len': 13, 'bb_std': 2.1059685183730124, 'bb_oversold': 0.1982045586978948, 'bb_overbought': 0.8053962074256032, 'mfi_len': 10, 'rsi_oversold': 37.0846062554568, 'rsi_overbought': 64.08127969912871, 'stoch_k': 10, 'stoch_d': 4, 'willr_len': 10, 'cci_len': 12, 'tp_atr_mult': 9.605091859233298}
2025-07-16 23:20:42,695 - root - WARNING - Trial 35 failed - ValueError: Indicator calculation failed: EMA calculation failed: Invalid EMA parameters: fast=17, slow=100 with params: {'ema_fast': 17, 'ema_slow': 100, 'atr_len': 8, 'vol_sma_len': 32, 'sl_atr_mult': 2.853353159744071, 'trail_atr_mult': 3.5279606487666495, 'risk_multiplier': 1.709090020156119, 'min_bars_between_trades': 1, 'use_volume_filter': False, 'st_len': 24, 'st_mult': 4.037287207873313, 'adx_len': 20, 'adx_threshold': 25.586872338155644, 'trend_confirmations': 2, 'macd_fast': 10, 'macd_slow': 42, 'macd_signal': 8, 'rsi_len': 11, 'bb_len': 38, 'bb_std': 3.1740156679516396, 'bb_oversold': 0.055917490007167045, 'bb_overbought': 0.929101253077137, 'mfi_len': 13, 'rsi_oversold': 17.134024594350457, 'rsi_overbought': 63.19279704486508, 'stoch_k': 24, 'stoch_d': 9, 'willr_len': 12, 'cci_len': 30, 'tp_atr_mult': 10.673271908037378}
2025-07-16 23:20:43,128 - root - WARNING - Trial 39 failed - ValueError: Indicator calculation failed: EMA calculation failed: Invalid EMA parameters: fast=9, slow=74 with params: {'ema_fast': 9, 'ema_slow': 74, 'atr_len': 10, 'vol_sma_len': 18, 'sl_atr_mult': 3.492120269629537, 'trail_atr_mult': 3.2414784471029807, 'risk_multiplier': 1.7849834391502382, 'min_bars_between_trades': 1, 'use_volume_filter': False, 'st_len': 32, 'st_mult': 1.576965536203552, 'adx_len': 18, 'adx_threshold': 31.23069915547459, 'trend_confirmations': 1, 'macd_fast': 7, 'macd_slow': 35, 'macd_signal': 7, 'rsi_len': 20, 'bb_len': 36, 'bb_std': 2.973804626122121, 'bb_oversold': 0.1813938395689194, 'bb_overbought': 0.9612366288973158, 'mfi_len': 12, 'rsi_oversold': 27.802901122642936, 'rsi_overbought': 67.14031005021869, 'stoch_k': 24, 'stoch_d': 10, 'willr_len': 20, 'cci_len': 18, 'tp_atr_mult': 11.486328647770407}
2025-07-16 23:20:43,946 - root - WARNING - Trial 46 failed - ValueError: Indicator calculation failed: EMA calculation failed: Invalid EMA parameters: fast=13, slow=70 with params: {'ema_fast': 13, 'ema_slow': 70, 'atr_len': 24, 'vol_sma_len': 32, 'sl_atr_mult': 3.8632657864075792, 'trail_atr_mult': 2.9987406024889816, 'risk_multiplier': 1.6377470125320601, 'min_bars_between_trades': 2, 'use_volume_filter': False, 'st_len': 28, 'st_mult': 3.8592822224358487, 'adx_len': 11, 'adx_threshold': 38.52903203130486, 'trend_confirmations': 2, 'macd_fast': 15, 'macd_slow': 30, 'macd_signal': 5, 'rsi_len': 9, 'bb_len': 31, 'bb_std': 2.85066128369669, 'bb_oversold': 0.20783980544052186, 'bb_overbought': 0.8941268845337407, 'mfi_len': 22, 'rsi_oversold': 25.117793383943727, 'rsi_overbought': 75.11761020182446, 'stoch_k': 22, 'stoch_d': 6, 'willr_len': 20, 'cci_len': 25, 'tp_atr_mult': 12.12070611782602}
2025-07-16 23:20:44,377 - __main__ - INFO - BTCUSDT Adaptive: Best score = 1.000
2025-07-16 23:20:44,378 - __main__ - INFO - Running walk-forward validation for BTCUSDT Adaptive
2025-07-16 23:20:44,378 - __main__ - INFO - Total data available: 15.6 days (1500 bars)
2025-07-16 23:20:44,378 - __main__ - INFO - Walk-forward parameters: IS=7d, OOS=3d, step=1d
2025-07-16 23:20:48,892 - __main__ - INFO - BTCUSDT Adaptive: 6 valid WF windows, WF score = 0.600
2025-07-16 23:20:48,892 - __main__ - INFO - Validating and scoring strategies with anti-overfitting criteria
2025-07-16 23:20:48,892 - __main__ - INFO - BTCUSDT_Adaptive failed robustness validation. Failed checks: ['min_sortino', 'min_profit_factor', 'oos_positive_return', 'oos_min_sortino']
2025-07-16 23:20:48,892 - __main__ - INFO -   Robustness score: 0.50, Sortino stability: 0.00
2025-07-16 23:20:48,892 - __main__ - INFO - Saving optimization results
2025-07-16 23:20:48,892 - __main__ - WARNING - No validated strategies to save
2025-07-16 23:20:48,892 - __main__ - WARNING - Optimization completed but no strategies passed validation
2025-07-16 23:20:48,893 - __main__ - INFO - Binance client connection closed
2025-07-16 23:21:46,653 - __main__ - INFO - Ultratrader Orchestrator initialized
2025-07-16 23:21:46,653 - __main__ - INFO - Ultratrader Orchestrator initialized
2025-07-16 23:21:46,653 - __main__ - INFO - Starting Ultratrader optimization pipeline
2025-07-16 23:21:47,449 - __main__ - INFO - Binance client initialized successfully
2025-07-16 23:21:47,449 - __main__ - INFO - Optimizing strategies for symbols: ['BTCUSDT']
2025-07-16 23:21:47,449 - __main__ - INFO - Fetching market data for BTCUSDT
2025-07-16 23:21:47,467 - __main__ - INFO - Retrieved 1500 bars for BTCUSDT (15m)
2025-07-16 23:21:47,467 - __main__ - INFO - Retrieved 1500 bars for BTCUSDT (1h)
2025-07-16 23:21:47,468 - __main__ - INFO - Optimizing Trend strategy for BTCUSDT
2025-07-16 23:21:49,893 - root - WARNING - Trial 20 failed - ValueError: Invalid parameters sampled: {'ema_fast': 24, 'ema_slow': 20, 'atr_len': 15, 'vol_sma_len': 14, 'sl_atr_mult': 3.485927447555941, 'trail_atr_mult': 3.5783489442560805, 'risk_multiplier': 1.5747652813924093, 'min_bars_between_trades': 1, 'use_volume_filter': False, 'st_len': 27, 'st_mult': 2.808921970095546, 'adx_len': 12, 'adx_threshold': 33.167069582769685, 'trend_confirmations': 1, 'macd_fast': 18, 'macd_slow': 32, 'macd_signal': 10, 'rsi_len': 22, 'tp_atr_mult': 11.727319378823598}
2025-07-16 23:21:49,946 - root - WARNING - Trial 21 failed - ValueError: Invalid parameters sampled: {'ema_fast': 9, 'ema_slow': 87, 'atr_len': 8, 'vol_sma_len': 39, 'sl_atr_mult': 2.7775383895727725, 'trail_atr_mult': 3.2543834614759204, 'risk_multiplier': 2.187132372488157, 'min_bars_between_trades': 2, 'use_volume_filter': True, 'st_len': 39, 'st_mult': 4.897131804446349, 'adx_len': 25, 'adx_threshold': 39.15181438666859, 'trend_confirmations': 2, 'macd_fast': 18, 'macd_slow': 18, 'macd_signal': 7, 'rsi_len': 9, 'tp_atr_mult': 9.020279162109832}
2025-07-16 23:21:52,971 - root - WARNING - Trial 59 failed - ValueError: Invalid parameters sampled: {'ema_fast': 14, 'ema_slow': 59, 'atr_len': 8, 'vol_sma_len': 29, 'sl_atr_mult': 3.2509101039287787, 'trail_atr_mult': 3.9662996584480643, 'risk_multiplier': 1.7714089707763776, 'min_bars_between_trades': 2, 'use_volume_filter': True, 'st_len': 5, 'st_mult': 3.56192219407724, 'adx_len': 16, 'adx_threshold': 38.72232917583658, 'trend_confirmations': 1, 'macd_fast': 19, 'macd_slow': 18, 'macd_signal': 15, 'rsi_len': 23, 'tp_atr_mult': 10.36084829782843}
2025-07-16 23:21:54,337 - root - WARNING - Trial 74 failed - ValueError: Invalid parameters sampled: {'ema_fast': 27, 'ema_slow': 27, 'atr_len': 14, 'vol_sma_len': 15, 'sl_atr_mult': 2.717166191777667, 'trail_atr_mult': 1.1200950593000483, 'risk_multiplier': 2.8382456959273012, 'min_bars_between_trades': 2, 'use_volume_filter': False, 'st_len': 31, 'st_mult': 2.1254681922262706, 'adx_len': 18, 'adx_threshold': 26.657029521861457, 'trend_confirmations': 2, 'macd_fast': 9, 'macd_slow': 44, 'macd_signal': 14, 'rsi_len': 12, 'tp_atr_mult': 12.717318029948522}
2025-07-16 23:21:54,379 - __main__ - INFO - BTCUSDT Trend: Best score = 1.000
2025-07-16 23:21:54,379 - __main__ - INFO - Running walk-forward validation for BTCUSDT Trend
2025-07-16 23:21:54,379 - __main__ - INFO - Total data available: 15.6 days (1500 bars)
2025-07-16 23:21:54,379 - __main__ - INFO - Walk-forward parameters: IS=7d, OOS=3d, step=1d
2025-07-16 23:21:57,717 - __main__ - INFO - BTCUSDT Trend: 6 valid WF windows, WF score = 0.600
2025-07-16 23:21:57,717 - __main__ - INFO - Optimizing Reversion strategy for BTCUSDT
2025-07-16 23:21:57,879 - root - WARNING - Trial 9 failed - ValueError: Invalid parameters sampled: {'ema_fast': 22, 'ema_slow': 20, 'atr_len': 10, 'vol_sma_len': 26, 'sl_atr_mult': 3.3837903953853865, 'trail_atr_mult': 2.8862760304083217, 'risk_multiplier': 2.0606732736513997, 'min_bars_between_trades': 2, 'use_volume_filter': False, 'bb_len': 33, 'bb_std': 2.6941556678085936, 'bb_oversold': 0.21532138441366092, 'bb_overbought': 0.901250965229079, 'mfi_len': 21, 'rsi_len': 9, 'rsi_oversold': 24.19289507648584, 'rsi_overbought': 66.63005919204313, 'stoch_k': 12, 'stoch_d': 10, 'willr_len': 17, 'cci_len': 28, 'tp_atr_mult': 11.680247633975366}
2025-07-16 23:21:59,774 - root - WARNING - Trial 38 failed - ValueError: Invalid parameters sampled: {'ema_fast': 28, 'ema_slow': 28, 'atr_len': 21, 'vol_sma_len': 10, 'sl_atr_mult': 2.9278498507634376, 'trail_atr_mult': 1.9666048606821223, 'risk_multiplier': 2.3293316718453005, 'min_bars_between_trades': 2, 'use_volume_filter': True, 'bb_len': 10, 'bb_std': 3.2194542190564093, 'bb_oversold': 0.22554953904969413, 'bb_overbought': 0.7778096801332361, 'mfi_len': 12, 'rsi_len': 21, 'rsi_oversold': 19.75495004598275, 'rsi_overbought': 76.69695293655664, 'stoch_k': 18, 'stoch_d': 7, 'willr_len': 21, 'cci_len': 28, 'tp_atr_mult': 7.4623560160597275}
2025-07-16 23:21:59,851 - root - WARNING - Trial 39 failed - ValueError: Invalid parameters sampled: {'ema_fast': 28, 'ema_slow': 27, 'atr_len': 22, 'vol_sma_len': 9, 'sl_atr_mult': 2.582740951778386, 'trail_atr_mult': 1.578044383504116, 'risk_multiplier': 2.2905742004845946, 'min_bars_between_trades': 2, 'use_volume_filter': True, 'bb_len': 10, 'bb_std': 3.339680215052894, 'bb_oversold': 0.19700236207968613, 'bb_overbought': 0.7739425944475213, 'mfi_len': 11, 'rsi_len': 21, 'rsi_oversold': 19.425370329745363, 'rsi_overbought': 76.97098756286574, 'stoch_k': 20, 'stoch_d': 5, 'willr_len': 21, 'cci_len': 28, 'tp_atr_mult': 7.553483634736278}
2025-07-16 23:21:59,917 - root - WARNING - Trial 40 failed - ValueError: Invalid parameters sampled: {'ema_fast': 28, 'ema_slow': 28, 'atr_len': 22, 'vol_sma_len': 9, 'sl_atr_mult': 2.139570598336691, 'trail_atr_mult': 1.6082421608630826, 'risk_multiplier': 2.290468195075918, 'min_bars_between_trades': 2, 'use_volume_filter': True, 'bb_len': 14, 'bb_std': 3.2981858198824163, 'bb_oversold': 0.2238781893851314, 'bb_overbought': 0.7725337596029574, 'mfi_len': 12, 'rsi_len': 21, 'rsi_oversold': 19.085700443923944, 'rsi_overbought': 77.07469088399705, 'stoch_k': 22, 'stoch_d': 5, 'willr_len': 21, 'cci_len': 28, 'tp_atr_mult': 7.540171586717557}
2025-07-16 23:21:59,982 - root - WARNING - Trial 41 failed - ValueError: Invalid parameters sampled: {'ema_fast': 28, 'ema_slow': 25, 'atr_len': 22, 'vol_sma_len': 10, 'sl_atr_mult': 2.085528607027352, 'trail_atr_mult': 1.576563864782924, 'risk_multiplier': 2.324488590251017, 'min_bars_between_trades': 2, 'use_volume_filter': True, 'bb_len': 10, 'bb_std': 3.339696533719319, 'bb_oversold': 0.22411276920188836, 'bb_overbought': 0.7696721943555463, 'mfi_len': 12, 'rsi_len': 21, 'rsi_oversold': 19.357572587845752, 'rsi_overbought': 77.20774542827532, 'stoch_k': 24, 'stoch_d': 5, 'willr_len': 21, 'cci_len': 28, 'tp_atr_mult': 7.499188692645576}
2025-07-16 23:22:00,076 - root - WARNING - Trial 42 failed - ValueError: Invalid parameters sampled: {'ema_fast': 28, 'ema_slow': 27, 'atr_len': 24, 'vol_sma_len': 10, 'sl_atr_mult': 2.053218620287528, 'trail_atr_mult': 1.573575640711633, 'risk_multiplier': 2.3124996508635474, 'min_bars_between_trades': 2, 'use_volume_filter': True, 'bb_len': 10, 'bb_std': 3.2708692587975916, 'bb_oversold': 0.2242373280266595, 'bb_overbought': 0.7684420252149379, 'mfi_len': 13, 'rsi_len': 21, 'rsi_oversold': 15.449813380348727, 'rsi_overbought': 77.21587070041683, 'stoch_k': 24, 'stoch_d': 5, 'willr_len': 21, 'cci_len': 28, 'tp_atr_mult': 7.495326358491884}
2025-07-16 23:22:00,226 - root - WARNING - Trial 44 failed - ValueError: Invalid parameters sampled: {'ema_fast': 30, 'ema_slow': 20, 'atr_len': 20, 'vol_sma_len': 8, 'sl_atr_mult': 2.5737207059828924, 'trail_atr_mult': 1.974834289529383, 'risk_multiplier': 2.4998834537245713, 'min_bars_between_trades': 2, 'use_volume_filter': True, 'bb_len': 11, 'bb_std': 3.148672221037543, 'bb_oversold': 0.19868215660530458, 'bb_overbought': 0.7596702973664605, 'mfi_len': 12, 'rsi_len': 22, 'rsi_oversold': 16.668896548574843, 'rsi_overbought': 76.68932932238167, 'stoch_k': 21, 'stoch_d': 5, 'willr_len': 19, 'cci_len': 25, 'tp_atr_mult': 7.656301443336177}
2025-07-16 23:22:00,382 - root - WARNING - Trial 46 failed - ValueError: Invalid parameters sampled: {'ema_fast': 28, 'ema_slow': 23, 'atr_len': 22, 'vol_sma_len': 8, 'sl_atr_mult': 2.1354210429886287, 'trail_atr_mult': 1.379936034102647, 'risk_multiplier': 2.053177046421258, 'min_bars_between_trades': 2, 'use_volume_filter': True, 'bb_len': 10, 'bb_std': 3.1951981047583535, 'bb_oversold': 0.19591147252619096, 'bb_overbought': 0.7852919073373079, 'mfi_len': 15, 'rsi_len': 23, 'rsi_oversold': 21.74305268747055, 'rsi_overbought': 81.3616679383206, 'stoch_k': 23, 'stoch_d': 4, 'willr_len': 20, 'cci_len': 29, 'tp_atr_mult': 6.893431884835614}
2025-07-16 23:22:00,626 - root - WARNING - Trial 49 failed - ValueError: Invalid parameters sampled: {'ema_fast': 27, 'ema_slow': 23, 'atr_len': 22, 'vol_sma_len': 11, 'sl_atr_mult': 2.9094990683265194, 'trail_atr_mult': 1.5089702546496626, 'risk_multiplier': 2.6153874071872822, 'min_bars_between_trades': 2, 'use_volume_filter': True, 'bb_len': 11, 'bb_std': 3.062948782075203, 'bb_oversold': 0.23615852577630808, 'bb_overbought': 0.7991009032669187, 'mfi_len': 8, 'rsi_len': 25, 'rsi_oversold': 20.688486237757125, 'rsi_overbought': 73.84031884123674, 'stoch_k': 20, 'stoch_d': 9, 'willr_len': 17, 'cci_len': 24, 'tp_atr_mult': 9.331241179956066}
2025-07-16 23:22:00,697 - root - WARNING - Trial 50 failed - ValueError: Invalid parameters sampled: {'ema_fast': 26, 'ema_slow': 26, 'atr_len': 18, 'vol_sma_len': 10, 'sl_atr_mult': 2.1085509684725183, 'trail_atr_mult': 1.951761311074288, 'risk_multiplier': 2.840808250129175, 'min_bars_between_trades': 1, 'use_volume_filter': True, 'bb_len': 14, 'bb_std': 3.2046926464566483, 'bb_oversold': 0.2092639970966826, 'bb_overbought': 0.9070199551915216, 'mfi_len': 15, 'rsi_len': 24, 'rsi_oversold': 16.35460361682691, 'rsi_overbought': 81.19216671890207, 'stoch_k': 24, 'stoch_d': 4, 'willr_len': 22, 'cci_len': 29, 'tp_atr_mult': 8.523205999820988}
2025-07-16 23:22:00,766 - root - WARNING - Trial 51 failed - ValueError: Invalid parameters sampled: {'ema_fast': 29, 'ema_slow': 28, 'atr_len': 24, 'vol_sma_len': 10, 'sl_atr_mult': 2.035730163811321, 'trail_atr_mult': 1.555792684949628, 'risk_multiplier': 2.2823252631616056, 'min_bars_between_trades': 2, 'use_volume_filter': True, 'bb_len': 10, 'bb_std': 3.2762646414958367, 'bb_oversold': 0.22434648428100784, 'bb_overbought': 0.7642943707665197, 'mfi_len': 13, 'rsi_len': 21, 'rsi_oversold': 18.749263898866108, 'rsi_overbought': 77.21188243810157, 'stoch_k': 24, 'stoch_d': 5, 'willr_len': 21, 'cci_len': 28, 'tp_atr_mult': 7.257640073969456}
2025-07-16 23:22:00,921 - root - WARNING - Trial 53 failed - ValueError: Invalid parameters sampled: {'ema_fast': 28, 'ema_slow': 26, 'atr_len': 24, 'vol_sma_len': 12, 'sl_atr_mult': 2.2534739616050032, 'trail_atr_mult': 1.4208199828904369, 'risk_multiplier': 2.4435092195817085, 'min_bars_between_trades': 2, 'use_volume_filter': True, 'bb_len': 10, 'bb_std': 3.3643764101439237, 'bb_oversold': 0.2131182544636931, 'bb_overbought': 0.7833757765855353, 'mfi_len': 12, 'rsi_len': 20, 'rsi_oversold': 19.84607785501496, 'rsi_overbought': 79.90564306709261, 'stoch_k': 22, 'stoch_d': 4, 'willr_len': 21, 'cci_len': 28, 'tp_atr_mult': 6.903521540061959}
2025-07-16 23:22:00,989 - root - WARNING - Trial 54 failed - ValueError: Invalid parameters sampled: {'ema_fast': 26, 'ema_slow': 20, 'atr_len': 21, 'vol_sma_len': 25, 'sl_atr_mult': 2.426622006397972, 'trail_atr_mult': 1.232189953873675, 'risk_multiplier': 2.079209428588707, 'min_bars_between_trades': 2, 'use_volume_filter': True, 'bb_len': 11, 'bb_std': 3.240542585232071, 'bb_oversold': 0.2192880547996075, 'bb_overbought': 0.7502283824954883, 'mfi_len': 20, 'rsi_len': 19, 'rsi_oversold': 15.991048137831843, 'rsi_overbought': 77.44663239388291, 'stoch_k': 23, 'stoch_d': 5, 'willr_len': 19, 'cci_len': 26, 'tp_atr_mult': 6.004083624371795}
2025-07-16 23:22:01,234 - root - WARNING - Trial 57 failed - ValueError: Invalid parameters sampled: {'ema_fast': 24, 'ema_slow': 23, 'atr_len': 20, 'vol_sma_len': 11, 'sl_atr_mult': 2.003858056359904, 'trail_atr_mult': 2.338129217405868, 'risk_multiplier': 2.2972706576384327, 'min_bars_between_trades': 2, 'use_volume_filter': True, 'bb_len': 15, 'bb_std': 2.647164961679343, 'bb_oversold': 0.20631337030425348, 'bb_overbought': 0.897870403802119, 'mfi_len': 18, 'rsi_len': 18, 'rsi_oversold': 20.302626019484087, 'rsi_overbought': 73.8565463159248, 'stoch_k': 18, 'stoch_d': 5, 'willr_len': 19, 'cci_len': 24, 'tp_atr_mult': 9.035407633416574}
2025-07-16 23:22:01,303 - root - WARNING - Trial 58 failed - ValueError: Invalid parameters sampled: {'ema_fast': 27, 'ema_slow': 27, 'atr_len': 22, 'vol_sma_len': 15, 'sl_atr_mult': 2.3371253178294364, 'trail_atr_mult': 1.4705046571574578, 'risk_multiplier': 2.515480893336058, 'min_bars_between_trades': 2, 'use_volume_filter': True, 'bb_len': 10, 'bb_std': 3.392916597865878, 'bb_oversold': 0.18515912407712096, 'bb_overbought': 0.7619635279342432, 'mfi_len': 14, 'rsi_len': 22, 'rsi_oversold': 15.118653240291064, 'rsi_overbought': 77.64691026370679, 'stoch_k': 25, 'stoch_d': 4, 'willr_len': 24, 'cci_len': 28, 'tp_atr_mult': 10.028599500779801}
2025-07-16 23:22:01,560 - root - WARNING - Trial 61 failed - ValueError: Invalid parameters sampled: {'ema_fast': 30, 'ema_slow': 22, 'atr_len': 20, 'vol_sma_len': 8, 'sl_atr_mult': 2.604354305529716, 'trail_atr_mult': 1.9959486858062352, 'risk_multiplier': 2.5012504500613355, 'min_bars_between_trades': 2, 'use_volume_filter': True, 'bb_len': 11, 'bb_std': 3.1601932262257084, 'bb_oversold': 0.19693116244623232, 'bb_overbought': 0.7581625432359048, 'mfi_len': 12, 'rsi_len': 22, 'rsi_oversold': 16.52556430264827, 'rsi_overbought': 76.16105805449178, 'stoch_k': 21, 'stoch_d': 5, 'willr_len': 19, 'cci_len': 25, 'tp_atr_mult': 7.504151221451527}
2025-07-16 23:22:01,641 - root - WARNING - Trial 62 failed - ValueError: Invalid parameters sampled: {'ema_fast': 28, 'ema_slow': 20, 'atr_len': 20, 'vol_sma_len': 10, 'sl_atr_mult': 2.452314085541029, 'trail_atr_mult': 1.988128658274577, 'risk_multiplier': 2.398591908688268, 'min_bars_between_trades': 2, 'use_volume_filter': True, 'bb_len': 11, 'bb_std': 3.259435646307269, 'bb_oversold': 0.20255620482143513, 'bb_overbought': 0.7880491769667838, 'mfi_len': 12, 'rsi_len': 22, 'rsi_oversold': 16.787344652021293, 'rsi_overbought': 76.43737260515391, 'stoch_k': 21, 'stoch_d': 5, 'willr_len': 20, 'cci_len': 26, 'tp_atr_mult': 7.649966068310504}
2025-07-16 23:22:01,797 - root - WARNING - Trial 64 failed - ValueError: Invalid parameters sampled: {'ema_fast': 26, 'ema_slow': 25, 'atr_len': 23, 'vol_sma_len': 11, 'sl_atr_mult': 3.004173384670561, 'trail_atr_mult': 1.6536171505284412, 'risk_multiplier': 2.1113486532058716, 'min_bars_between_trades': 2, 'use_volume_filter': True, 'bb_len': 11, 'bb_std': 2.7573691115127428, 'bb_oversold': 0.1682628659308191, 'bb_overbought': 0.7737283480978066, 'mfi_len': 15, 'rsi_len': 23, 'rsi_oversold': 17.51948399491222, 'rsi_overbought': 78.5812745635762, 'stoch_k': 23, 'stoch_d': 4, 'willr_len': 18, 'cci_len': 29, 'tp_atr_mult': 8.292405503495807}
2025-07-16 23:22:01,884 - root - WARNING - Trial 65 failed - ValueError: Invalid parameters sampled: {'ema_fast': 29, 'ema_slow': 28, 'atr_len': 18, 'vol_sma_len': 27, 'sl_atr_mult': 2.152340790020311, 'trail_atr_mult': 2.201524667090991, 'risk_multiplier': 2.5608964848985805, 'min_bars_between_trades': 2, 'use_volume_filter': True, 'bb_len': 14, 'bb_std': 3.0410966324611257, 'bb_oversold': 0.1838017738141655, 'bb_overbought': 0.8014094639347624, 'mfi_len': 11, 'rsi_len': 20, 'rsi_oversold': 21.23436177846322, 'rsi_overbought': 77.71341299483942, 'stoch_k': 19, 'stoch_d': 6, 'willr_len': 23, 'cci_len': 28, 'tp_atr_mult': 6.818166264169015}
2025-07-16 23:22:01,962 - root - WARNING - Trial 66 failed - ValueError: Invalid parameters sampled: {'ema_fast': 30, 'ema_slow': 22, 'atr_len': 21, 'vol_sma_len': 8, 'sl_atr_mult': 2.805908353631528, 'trail_atr_mult': 2.3084179709944914, 'risk_multiplier': 2.159294975914167, 'min_bars_between_trades': 2, 'use_volume_filter': True, 'bb_len': 24, 'bb_std': 3.314007136917876, 'bb_oversold': 0.21272413686481936, 'bb_overbought': 0.7878278939626071, 'mfi_len': 21, 'rsi_len': 17, 'rsi_oversold': 19.377373008044117, 'rsi_overbought': 73.15415640346825, 'stoch_k': 18, 'stoch_d': 5, 'willr_len': 22, 'cci_len': 27, 'tp_atr_mult': 7.7114801855294}
2025-07-16 23:22:02,128 - root - WARNING - Trial 68 failed - ValueError: Invalid parameters sampled: {'ema_fast': 27, 'ema_slow': 20, 'atr_len': 22, 'vol_sma_len': 16, 'sl_atr_mult': 2.517394131073412, 'trail_atr_mult': 1.135729037632433, 'risk_multiplier': 2.3480934522753603, 'min_bars_between_trades': 2, 'use_volume_filter': True, 'bb_len': 31, 'bb_std': 3.1927456362669875, 'bb_oversold': 0.20046707445153755, 'bb_overbought': 0.8536812803449829, 'mfi_len': 14, 'rsi_len': 22, 'rsi_oversold': 15.92303971158994, 'rsi_overbought': 70.93461983690983, 'stoch_k': 19, 'stoch_d': 4, 'willr_len': 20, 'cci_len': 30, 'tp_atr_mult': 11.040629249380133}
2025-07-16 23:22:02,382 - root - WARNING - Trial 71 failed - ValueError: Invalid parameters sampled: {'ema_fast': 28, 'ema_slow': 24, 'atr_len': 22, 'vol_sma_len': 8, 'sl_atr_mult': 2.124186286860088, 'trail_atr_mult': 1.3561024277507596, 'risk_multiplier': 2.053400883077793, 'min_bars_between_trades': 2, 'use_volume_filter': True, 'bb_len': 10, 'bb_std': 3.2211709140906395, 'bb_oversold': 0.19691236509764556, 'bb_overbought': 0.7847197050486078, 'mfi_len': 15, 'rsi_len': 23, 'rsi_oversold': 21.83520514928736, 'rsi_overbought': 84.96413133742654, 'stoch_k': 23, 'stoch_d': 3, 'willr_len': 20, 'cci_len': 29, 'tp_atr_mult': 6.811478565297005}
2025-07-16 23:22:02,455 - root - WARNING - Trial 72 failed - ValueError: Invalid parameters sampled: {'ema_fast': 27, 'ema_slow': 23, 'atr_len': 20, 'vol_sma_len': 11, 'sl_atr_mult': 2.905819509934385, 'trail_atr_mult': 1.4500651128193665, 'risk_multiplier': 2.01054890339329, 'min_bars_between_trades': 2, 'use_volume_filter': True, 'bb_len': 11, 'bb_std': 3.1333842149521183, 'bb_oversold': 0.19320041128853987, 'bb_overbought': 0.7786083429625804, 'mfi_len': 13, 'rsi_len': 21, 'rsi_oversold': 23.18482811754237, 'rsi_overbought': 79.66023485059522, 'stoch_k': 25, 'stoch_d': 4, 'willr_len': 19, 'cci_len': 28, 'tp_atr_mult': 6.361524822152274}
2025-07-16 23:22:02,688 - __main__ - INFO - BTCUSDT Reversion: Best score = -999.000
2025-07-16 23:22:02,688 - __main__ - INFO - Running walk-forward validation for BTCUSDT Reversion
2025-07-16 23:22:02,688 - __main__ - INFO - Total data available: 15.6 days (1500 bars)
2025-07-16 23:22:02,689 - __main__ - INFO - Walk-forward parameters: IS=7d, OOS=3d, step=1d
2025-07-16 23:22:05,618 - __main__ - WARNING - No valid walk-forward windows for BTCUSDT Reversion
2025-07-16 23:22:05,619 - __main__ - INFO - Attempting single-split validation as fallback...
2025-07-16 23:22:06,241 - __main__ - WARNING - Single-split validation also failed
2025-07-16 23:22:06,241 - __main__ - WARNING - All validation attempts failed for BTCUSDT Reversion, using optimization results only
2025-07-16 23:22:06,241 - __main__ - INFO - BTCUSDT Reversion: 1 valid WF windows, WF score = 3.000
2025-07-16 23:22:06,241 - __main__ - INFO - Optimizing Adaptive strategy for BTCUSDT
2025-07-16 23:22:10,400 - root - WARNING - Trial 45 failed - ValueError: Invalid parameters sampled: {'ema_fast': 23, 'ema_slow': 20, 'atr_len': 19, 'vol_sma_len': 19, 'sl_atr_mult': 3.517566919003947, 'trail_atr_mult': 2.8264846037232485, 'risk_multiplier': 2.096611900253328, 'min_bars_between_trades': 1, 'use_volume_filter': True, 'st_len': 15, 'st_mult': 3.863382446343641, 'adx_len': 25, 'adx_threshold': 16.598149517986137, 'trend_confirmations': 1, 'macd_fast': 7, 'macd_slow': 41, 'macd_signal': 14, 'rsi_len': 15, 'bb_len': 18, 'bb_std': 2.677951984496531, 'bb_oversold': 0.10148205931168032, 'bb_overbought': 0.9120116499563181, 'mfi_len': 14, 'rsi_oversold': 29.74684889673103, 'rsi_overbought': 81.18365755775564, 'stoch_k': 24, 'stoch_d': 4, 'willr_len': 18, 'cci_len': 10, 'tp_atr_mult': 11.826321125310576}
2025-07-16 23:22:10,916 - __main__ - INFO - BTCUSDT Adaptive: Best score = 1.000
2025-07-16 23:22:10,916 - __main__ - INFO - Running walk-forward validation for BTCUSDT Adaptive
2025-07-16 23:22:10,917 - __main__ - INFO - Total data available: 15.6 days (1500 bars)
2025-07-16 23:22:10,917 - __main__ - INFO - Walk-forward parameters: IS=7d, OOS=3d, step=1d
2025-07-16 23:22:15,787 - __main__ - INFO - BTCUSDT Adaptive: 6 valid WF windows, WF score = 0.600
2025-07-16 23:22:15,787 - __main__ - INFO - Validating and scoring strategies with anti-overfitting criteria
2025-07-16 23:22:15,787 - __main__ - INFO - BTCUSDT_Trend failed robustness validation. Failed checks: ['min_sortino', 'min_profit_factor', 'oos_positive_return', 'oos_min_sortino']
2025-07-16 23:22:15,788 - __main__ - INFO -   Robustness score: 0.50, Sortino stability: 0.00
2025-07-16 23:22:15,788 - __main__ - INFO - BTCUSDT_Reversion failed robustness validation. Failed checks: ['min_trades', 'min_win_rate']
2025-07-16 23:22:15,788 - __main__ - INFO -   Robustness score: 0.75, Sortino stability: 0.00
2025-07-16 23:22:15,788 - __main__ - INFO - BTCUSDT_Adaptive failed robustness validation. Failed checks: ['min_sortino', 'min_profit_factor', 'oos_positive_return', 'oos_min_sortino']
2025-07-16 23:22:15,788 - __main__ - INFO -   Robustness score: 0.50, Sortino stability: 0.00
2025-07-16 23:22:15,788 - __main__ - INFO - Saving optimization results
2025-07-16 23:22:15,788 - __main__ - WARNING - No validated strategies to save
2025-07-16 23:22:15,788 - __main__ - WARNING - Optimization completed but no strategies passed validation
2025-07-16 23:22:15,789 - __main__ - INFO - Binance client connection closed
2025-07-16 23:23:05,431 - __main__ - INFO - Ultratrader Orchestrator initialized
2025-07-16 23:23:05,431 - __main__ - INFO - Ultratrader Orchestrator initialized
2025-07-16 23:23:05,432 - __main__ - INFO - Starting Ultratrader optimization pipeline
2025-07-16 23:23:06,115 - __main__ - INFO - Binance client initialized successfully
2025-07-16 23:23:06,115 - __main__ - INFO - Optimizing strategies for symbols: ['BTCUSDT']
2025-07-16 23:23:06,115 - __main__ - INFO - Fetching market data for BTCUSDT
2025-07-16 23:23:06,128 - __main__ - INFO - Retrieved 1500 bars for BTCUSDT (15m)
2025-07-16 23:23:06,128 - __main__ - INFO - Retrieved 1500 bars for BTCUSDT (1h)
2025-07-16 23:23:06,128 - __main__ - INFO - Optimizing Trend strategy for BTCUSDT
2025-07-16 23:23:08,469 - root - WARNING - Trial 20 failed - ValueError: Invalid parameters sampled: {'ema_fast': 24, 'ema_slow': 20, 'atr_len': 15, 'vol_sma_len': 14, 'sl_atr_mult': 3.485927447555941, 'trail_atr_mult': 3.5783489442560805, 'risk_multiplier': 1.5747652813924093, 'min_bars_between_trades': 1, 'use_volume_filter': False, 'st_len': 27, 'st_mult': 2.808921970095546, 'adx_len': 12, 'adx_threshold': 33.167069582769685, 'trend_confirmations': 1, 'macd_fast': 18, 'macd_slow': 32, 'macd_signal': 10, 'rsi_len': 22, 'tp_atr_mult': 11.727319378823598}
2025-07-16 23:23:08,519 - root - WARNING - Trial 21 failed - ValueError: Invalid parameters sampled: {'ema_fast': 9, 'ema_slow': 87, 'atr_len': 8, 'vol_sma_len': 39, 'sl_atr_mult': 2.7775383895727725, 'trail_atr_mult': 3.2543834614759204, 'risk_multiplier': 2.187132372488157, 'min_bars_between_trades': 2, 'use_volume_filter': True, 'st_len': 39, 'st_mult': 4.897131804446349, 'adx_len': 25, 'adx_threshold': 39.15181438666859, 'trend_confirmations': 2, 'macd_fast': 18, 'macd_slow': 18, 'macd_signal': 7, 'rsi_len': 9, 'tp_atr_mult': 9.020279162109832}
2025-07-16 23:23:11,428 - root - WARNING - Trial 59 failed - ValueError: Invalid parameters sampled: {'ema_fast': 14, 'ema_slow': 59, 'atr_len': 8, 'vol_sma_len': 29, 'sl_atr_mult': 3.2509101039287787, 'trail_atr_mult': 3.9662996584480643, 'risk_multiplier': 1.7714089707763776, 'min_bars_between_trades': 2, 'use_volume_filter': True, 'st_len': 5, 'st_mult': 3.56192219407724, 'adx_len': 16, 'adx_threshold': 38.72232917583658, 'trend_confirmations': 1, 'macd_fast': 19, 'macd_slow': 18, 'macd_signal': 15, 'rsi_len': 23, 'tp_atr_mult': 10.36084829782843}
2025-07-16 23:23:12,682 - root - WARNING - Trial 74 failed - ValueError: Invalid parameters sampled: {'ema_fast': 27, 'ema_slow': 27, 'atr_len': 14, 'vol_sma_len': 15, 'sl_atr_mult': 2.717166191777667, 'trail_atr_mult': 1.1200950593000483, 'risk_multiplier': 2.8382456959273012, 'min_bars_between_trades': 2, 'use_volume_filter': False, 'st_len': 31, 'st_mult': 2.1254681922262706, 'adx_len': 18, 'adx_threshold': 26.657029521861457, 'trend_confirmations': 2, 'macd_fast': 9, 'macd_slow': 44, 'macd_signal': 14, 'rsi_len': 12, 'tp_atr_mult': 12.717318029948522}
2025-07-16 23:23:12,718 - __main__ - INFO - BTCUSDT Trend: Best score = 1.000
2025-07-16 23:23:12,719 - __main__ - INFO - Running walk-forward validation for BTCUSDT Trend
2025-07-16 23:23:12,719 - __main__ - INFO - Total data available: 15.6 days (1500 bars)
2025-07-16 23:23:12,719 - __main__ - INFO - Walk-forward parameters: IS=7d, OOS=3d, step=1d
2025-07-16 23:23:16,059 - __main__ - INFO - BTCUSDT Trend: 6 valid WF windows, WF score = 0.600
2025-07-16 23:23:16,059 - __main__ - INFO - Optimizing Reversion strategy for BTCUSDT
2025-07-16 23:23:16,225 - root - WARNING - Trial 9 failed - ValueError: Invalid parameters sampled: {'ema_fast': 22, 'ema_slow': 20, 'atr_len': 10, 'vol_sma_len': 26, 'sl_atr_mult': 3.3837903953853865, 'trail_atr_mult': 2.8862760304083217, 'risk_multiplier': 2.0606732736513997, 'min_bars_between_trades': 2, 'use_volume_filter': False, 'bb_len': 33, 'bb_std': 2.6941556678085936, 'bb_oversold': 0.21532138441366092, 'bb_overbought': 0.901250965229079, 'mfi_len': 21, 'rsi_len': 9, 'rsi_oversold': 24.19289507648584, 'rsi_overbought': 66.63005919204313, 'stoch_k': 12, 'stoch_d': 10, 'willr_len': 17, 'cci_len': 28, 'tp_atr_mult': 11.680247633975366}
2025-07-16 23:23:18,035 - root - WARNING - Trial 38 failed - ValueError: Invalid parameters sampled: {'ema_fast': 28, 'ema_slow': 28, 'atr_len': 21, 'vol_sma_len': 10, 'sl_atr_mult': 2.9278498507634376, 'trail_atr_mult': 1.9666048606821223, 'risk_multiplier': 2.3293316718453005, 'min_bars_between_trades': 2, 'use_volume_filter': True, 'bb_len': 10, 'bb_std': 3.2194542190564093, 'bb_oversold': 0.22554953904969413, 'bb_overbought': 0.7778096801332361, 'mfi_len': 12, 'rsi_len': 21, 'rsi_oversold': 19.75495004598275, 'rsi_overbought': 76.69695293655664, 'stoch_k': 18, 'stoch_d': 7, 'willr_len': 21, 'cci_len': 28, 'tp_atr_mult': 7.4623560160597275}
2025-07-16 23:23:18,111 - root - WARNING - Trial 39 failed - ValueError: Invalid parameters sampled: {'ema_fast': 28, 'ema_slow': 27, 'atr_len': 22, 'vol_sma_len': 9, 'sl_atr_mult': 2.582740951778386, 'trail_atr_mult': 1.578044383504116, 'risk_multiplier': 2.2905742004845946, 'min_bars_between_trades': 2, 'use_volume_filter': True, 'bb_len': 10, 'bb_std': 3.339680215052894, 'bb_oversold': 0.19700236207968613, 'bb_overbought': 0.7739425944475213, 'mfi_len': 11, 'rsi_len': 21, 'rsi_oversold': 19.425370329745363, 'rsi_overbought': 76.97098756286574, 'stoch_k': 20, 'stoch_d': 5, 'willr_len': 21, 'cci_len': 28, 'tp_atr_mult': 7.553483634736278}
2025-07-16 23:23:18,195 - root - WARNING - Trial 40 failed - ValueError: Invalid parameters sampled: {'ema_fast': 28, 'ema_slow': 28, 'atr_len': 22, 'vol_sma_len': 9, 'sl_atr_mult': 2.139570598336691, 'trail_atr_mult': 1.6082421608630826, 'risk_multiplier': 2.290468195075918, 'min_bars_between_trades': 2, 'use_volume_filter': True, 'bb_len': 14, 'bb_std': 3.2981858198824163, 'bb_oversold': 0.2238781893851314, 'bb_overbought': 0.7725337596029574, 'mfi_len': 12, 'rsi_len': 21, 'rsi_oversold': 19.085700443923944, 'rsi_overbought': 77.07469088399705, 'stoch_k': 22, 'stoch_d': 5, 'willr_len': 21, 'cci_len': 28, 'tp_atr_mult': 7.540171586717557}
2025-07-16 23:23:18,288 - root - WARNING - Trial 41 failed - ValueError: Invalid parameters sampled: {'ema_fast': 28, 'ema_slow': 25, 'atr_len': 22, 'vol_sma_len': 10, 'sl_atr_mult': 2.085528607027352, 'trail_atr_mult': 1.576563864782924, 'risk_multiplier': 2.324488590251017, 'min_bars_between_trades': 2, 'use_volume_filter': True, 'bb_len': 10, 'bb_std': 3.339696533719319, 'bb_oversold': 0.22411276920188836, 'bb_overbought': 0.7696721943555463, 'mfi_len': 12, 'rsi_len': 21, 'rsi_oversold': 19.357572587845752, 'rsi_overbought': 77.20774542827532, 'stoch_k': 24, 'stoch_d': 5, 'willr_len': 21, 'cci_len': 28, 'tp_atr_mult': 7.499188692645576}
2025-07-16 23:23:18,377 - root - WARNING - Trial 42 failed - ValueError: Invalid parameters sampled: {'ema_fast': 28, 'ema_slow': 27, 'atr_len': 24, 'vol_sma_len': 10, 'sl_atr_mult': 2.053218620287528, 'trail_atr_mult': 1.573575640711633, 'risk_multiplier': 2.3124996508635474, 'min_bars_between_trades': 2, 'use_volume_filter': True, 'bb_len': 10, 'bb_std': 3.2708692587975916, 'bb_oversold': 0.2242373280266595, 'bb_overbought': 0.7684420252149379, 'mfi_len': 13, 'rsi_len': 21, 'rsi_oversold': 15.449813380348727, 'rsi_overbought': 77.21587070041683, 'stoch_k': 24, 'stoch_d': 5, 'willr_len': 21, 'cci_len': 28, 'tp_atr_mult': 7.495326358491884}
2025-07-16 23:23:18,538 - root - WARNING - Trial 44 failed - ValueError: Invalid parameters sampled: {'ema_fast': 30, 'ema_slow': 20, 'atr_len': 20, 'vol_sma_len': 8, 'sl_atr_mult': 2.5737207059828924, 'trail_atr_mult': 1.974834289529383, 'risk_multiplier': 2.4998834537245713, 'min_bars_between_trades': 2, 'use_volume_filter': True, 'bb_len': 11, 'bb_std': 3.148672221037543, 'bb_oversold': 0.19868215660530458, 'bb_overbought': 0.7596702973664605, 'mfi_len': 12, 'rsi_len': 22, 'rsi_oversold': 16.668896548574843, 'rsi_overbought': 76.68932932238167, 'stoch_k': 21, 'stoch_d': 5, 'willr_len': 19, 'cci_len': 25, 'tp_atr_mult': 7.656301443336177}
2025-07-16 23:23:18,677 - root - WARNING - Trial 46 failed - ValueError: Invalid parameters sampled: {'ema_fast': 28, 'ema_slow': 23, 'atr_len': 22, 'vol_sma_len': 8, 'sl_atr_mult': 2.1354210429886287, 'trail_atr_mult': 1.379936034102647, 'risk_multiplier': 2.053177046421258, 'min_bars_between_trades': 2, 'use_volume_filter': True, 'bb_len': 10, 'bb_std': 3.1951981047583535, 'bb_oversold': 0.19591147252619096, 'bb_overbought': 0.7852919073373079, 'mfi_len': 15, 'rsi_len': 23, 'rsi_oversold': 21.74305268747055, 'rsi_overbought': 81.3616679383206, 'stoch_k': 23, 'stoch_d': 4, 'willr_len': 20, 'cci_len': 29, 'tp_atr_mult': 6.893431884835614}
2025-07-16 23:23:18,907 - root - WARNING - Trial 49 failed - ValueError: Invalid parameters sampled: {'ema_fast': 27, 'ema_slow': 23, 'atr_len': 22, 'vol_sma_len': 11, 'sl_atr_mult': 2.9094990683265194, 'trail_atr_mult': 1.5089702546496626, 'risk_multiplier': 2.6153874071872822, 'min_bars_between_trades': 2, 'use_volume_filter': True, 'bb_len': 11, 'bb_std': 3.062948782075203, 'bb_oversold': 0.23615852577630808, 'bb_overbought': 0.7991009032669187, 'mfi_len': 8, 'rsi_len': 25, 'rsi_oversold': 20.688486237757125, 'rsi_overbought': 73.84031884123674, 'stoch_k': 20, 'stoch_d': 9, 'willr_len': 17, 'cci_len': 24, 'tp_atr_mult': 9.331241179956066}
2025-07-16 23:23:18,978 - root - WARNING - Trial 50 failed - ValueError: Invalid parameters sampled: {'ema_fast': 26, 'ema_slow': 26, 'atr_len': 18, 'vol_sma_len': 10, 'sl_atr_mult': 2.1085509684725183, 'trail_atr_mult': 1.951761311074288, 'risk_multiplier': 2.840808250129175, 'min_bars_between_trades': 1, 'use_volume_filter': True, 'bb_len': 14, 'bb_std': 3.2046926464566483, 'bb_oversold': 0.2092639970966826, 'bb_overbought': 0.9070199551915216, 'mfi_len': 15, 'rsi_len': 24, 'rsi_oversold': 16.35460361682691, 'rsi_overbought': 81.19216671890207, 'stoch_k': 24, 'stoch_d': 4, 'willr_len': 22, 'cci_len': 29, 'tp_atr_mult': 8.523205999820988}
2025-07-16 23:23:19,043 - root - WARNING - Trial 51 failed - ValueError: Invalid parameters sampled: {'ema_fast': 29, 'ema_slow': 28, 'atr_len': 24, 'vol_sma_len': 10, 'sl_atr_mult': 2.035730163811321, 'trail_atr_mult': 1.555792684949628, 'risk_multiplier': 2.2823252631616056, 'min_bars_between_trades': 2, 'use_volume_filter': True, 'bb_len': 10, 'bb_std': 3.2762646414958367, 'bb_oversold': 0.22434648428100784, 'bb_overbought': 0.7642943707665197, 'mfi_len': 13, 'rsi_len': 21, 'rsi_oversold': 18.749263898866108, 'rsi_overbought': 77.21188243810157, 'stoch_k': 24, 'stoch_d': 5, 'willr_len': 21, 'cci_len': 28, 'tp_atr_mult': 7.257640073969456}
2025-07-16 23:23:19,192 - root - WARNING - Trial 53 failed - ValueError: Invalid parameters sampled: {'ema_fast': 28, 'ema_slow': 26, 'atr_len': 24, 'vol_sma_len': 12, 'sl_atr_mult': 2.2534739616050032, 'trail_atr_mult': 1.4208199828904369, 'risk_multiplier': 2.4435092195817085, 'min_bars_between_trades': 2, 'use_volume_filter': True, 'bb_len': 10, 'bb_std': 3.3643764101439237, 'bb_oversold': 0.2131182544636931, 'bb_overbought': 0.7833757765855353, 'mfi_len': 12, 'rsi_len': 20, 'rsi_oversold': 19.84607785501496, 'rsi_overbought': 79.90564306709261, 'stoch_k': 22, 'stoch_d': 4, 'willr_len': 21, 'cci_len': 28, 'tp_atr_mult': 6.903521540061959}
2025-07-16 23:23:19,266 - root - WARNING - Trial 54 failed - ValueError: Invalid parameters sampled: {'ema_fast': 26, 'ema_slow': 20, 'atr_len': 21, 'vol_sma_len': 25, 'sl_atr_mult': 2.426622006397972, 'trail_atr_mult': 1.232189953873675, 'risk_multiplier': 2.079209428588707, 'min_bars_between_trades': 2, 'use_volume_filter': True, 'bb_len': 11, 'bb_std': 3.240542585232071, 'bb_oversold': 0.2192880547996075, 'bb_overbought': 0.7502283824954883, 'mfi_len': 20, 'rsi_len': 19, 'rsi_oversold': 15.991048137831843, 'rsi_overbought': 77.44663239388291, 'stoch_k': 23, 'stoch_d': 5, 'willr_len': 19, 'cci_len': 26, 'tp_atr_mult': 6.004083624371795}
2025-07-16 23:23:19,508 - root - WARNING - Trial 57 failed - ValueError: Invalid parameters sampled: {'ema_fast': 24, 'ema_slow': 23, 'atr_len': 20, 'vol_sma_len': 11, 'sl_atr_mult': 2.003858056359904, 'trail_atr_mult': 2.338129217405868, 'risk_multiplier': 2.2972706576384327, 'min_bars_between_trades': 2, 'use_volume_filter': True, 'bb_len': 15, 'bb_std': 2.647164961679343, 'bb_oversold': 0.20631337030425348, 'bb_overbought': 0.897870403802119, 'mfi_len': 18, 'rsi_len': 18, 'rsi_oversold': 20.302626019484087, 'rsi_overbought': 73.8565463159248, 'stoch_k': 18, 'stoch_d': 5, 'willr_len': 19, 'cci_len': 24, 'tp_atr_mult': 9.035407633416574}
2025-07-16 23:23:19,577 - root - WARNING - Trial 58 failed - ValueError: Invalid parameters sampled: {'ema_fast': 27, 'ema_slow': 27, 'atr_len': 22, 'vol_sma_len': 15, 'sl_atr_mult': 2.3371253178294364, 'trail_atr_mult': 1.4705046571574578, 'risk_multiplier': 2.515480893336058, 'min_bars_between_trades': 2, 'use_volume_filter': True, 'bb_len': 10, 'bb_std': 3.392916597865878, 'bb_oversold': 0.18515912407712096, 'bb_overbought': 0.7619635279342432, 'mfi_len': 14, 'rsi_len': 22, 'rsi_oversold': 15.118653240291064, 'rsi_overbought': 77.64691026370679, 'stoch_k': 25, 'stoch_d': 4, 'willr_len': 24, 'cci_len': 28, 'tp_atr_mult': 10.028599500779801}
2025-07-16 23:23:19,823 - root - WARNING - Trial 61 failed - ValueError: Invalid parameters sampled: {'ema_fast': 30, 'ema_slow': 22, 'atr_len': 20, 'vol_sma_len': 8, 'sl_atr_mult': 2.604354305529716, 'trail_atr_mult': 1.9959486858062352, 'risk_multiplier': 2.5012504500613355, 'min_bars_between_trades': 2, 'use_volume_filter': True, 'bb_len': 11, 'bb_std': 3.1601932262257084, 'bb_oversold': 0.19693116244623232, 'bb_overbought': 0.7581625432359048, 'mfi_len': 12, 'rsi_len': 22, 'rsi_oversold': 16.52556430264827, 'rsi_overbought': 76.16105805449178, 'stoch_k': 21, 'stoch_d': 5, 'willr_len': 19, 'cci_len': 25, 'tp_atr_mult': 7.504151221451527}
2025-07-16 23:23:19,891 - root - WARNING - Trial 62 failed - ValueError: Invalid parameters sampled: {'ema_fast': 28, 'ema_slow': 20, 'atr_len': 20, 'vol_sma_len': 10, 'sl_atr_mult': 2.452314085541029, 'trail_atr_mult': 1.988128658274577, 'risk_multiplier': 2.398591908688268, 'min_bars_between_trades': 2, 'use_volume_filter': True, 'bb_len': 11, 'bb_std': 3.259435646307269, 'bb_oversold': 0.20255620482143513, 'bb_overbought': 0.7880491769667838, 'mfi_len': 12, 'rsi_len': 22, 'rsi_oversold': 16.787344652021293, 'rsi_overbought': 76.43737260515391, 'stoch_k': 21, 'stoch_d': 5, 'willr_len': 20, 'cci_len': 26, 'tp_atr_mult': 7.649966068310504}
2025-07-16 23:23:20,045 - root - WARNING - Trial 64 failed - ValueError: Invalid parameters sampled: {'ema_fast': 26, 'ema_slow': 25, 'atr_len': 23, 'vol_sma_len': 11, 'sl_atr_mult': 3.004173384670561, 'trail_atr_mult': 1.6536171505284412, 'risk_multiplier': 2.1113486532058716, 'min_bars_between_trades': 2, 'use_volume_filter': True, 'bb_len': 11, 'bb_std': 2.7573691115127428, 'bb_oversold': 0.1682628659308191, 'bb_overbought': 0.7737283480978066, 'mfi_len': 15, 'rsi_len': 23, 'rsi_oversold': 17.51948399491222, 'rsi_overbought': 78.5812745635762, 'stoch_k': 23, 'stoch_d': 4, 'willr_len': 18, 'cci_len': 29, 'tp_atr_mult': 8.292405503495807}
2025-07-16 23:23:20,111 - root - WARNING - Trial 65 failed - ValueError: Invalid parameters sampled: {'ema_fast': 29, 'ema_slow': 28, 'atr_len': 18, 'vol_sma_len': 27, 'sl_atr_mult': 2.152340790020311, 'trail_atr_mult': 2.201524667090991, 'risk_multiplier': 2.5608964848985805, 'min_bars_between_trades': 2, 'use_volume_filter': True, 'bb_len': 14, 'bb_std': 3.0410966324611257, 'bb_oversold': 0.1838017738141655, 'bb_overbought': 0.8014094639347624, 'mfi_len': 11, 'rsi_len': 20, 'rsi_oversold': 21.23436177846322, 'rsi_overbought': 77.71341299483942, 'stoch_k': 19, 'stoch_d': 6, 'willr_len': 23, 'cci_len': 28, 'tp_atr_mult': 6.818166264169015}
2025-07-16 23:23:20,176 - root - WARNING - Trial 66 failed - ValueError: Invalid parameters sampled: {'ema_fast': 30, 'ema_slow': 22, 'atr_len': 21, 'vol_sma_len': 8, 'sl_atr_mult': 2.805908353631528, 'trail_atr_mult': 2.3084179709944914, 'risk_multiplier': 2.159294975914167, 'min_bars_between_trades': 2, 'use_volume_filter': True, 'bb_len': 24, 'bb_std': 3.314007136917876, 'bb_oversold': 0.21272413686481936, 'bb_overbought': 0.7878278939626071, 'mfi_len': 21, 'rsi_len': 17, 'rsi_oversold': 19.377373008044117, 'rsi_overbought': 73.15415640346825, 'stoch_k': 18, 'stoch_d': 5, 'willr_len': 22, 'cci_len': 27, 'tp_atr_mult': 7.7114801855294}
2025-07-16 23:23:20,336 - root - WARNING - Trial 68 failed - ValueError: Invalid parameters sampled: {'ema_fast': 27, 'ema_slow': 20, 'atr_len': 22, 'vol_sma_len': 16, 'sl_atr_mult': 2.517394131073412, 'trail_atr_mult': 1.135729037632433, 'risk_multiplier': 2.3480934522753603, 'min_bars_between_trades': 2, 'use_volume_filter': True, 'bb_len': 31, 'bb_std': 3.1927456362669875, 'bb_oversold': 0.20046707445153755, 'bb_overbought': 0.8536812803449829, 'mfi_len': 14, 'rsi_len': 22, 'rsi_oversold': 15.92303971158994, 'rsi_overbought': 70.93461983690983, 'stoch_k': 19, 'stoch_d': 4, 'willr_len': 20, 'cci_len': 30, 'tp_atr_mult': 11.040629249380133}
2025-07-16 23:23:20,570 - root - WARNING - Trial 71 failed - ValueError: Invalid parameters sampled: {'ema_fast': 28, 'ema_slow': 24, 'atr_len': 22, 'vol_sma_len': 8, 'sl_atr_mult': 2.124186286860088, 'trail_atr_mult': 1.3561024277507596, 'risk_multiplier': 2.053400883077793, 'min_bars_between_trades': 2, 'use_volume_filter': True, 'bb_len': 10, 'bb_std': 3.2211709140906395, 'bb_oversold': 0.19691236509764556, 'bb_overbought': 0.7847197050486078, 'mfi_len': 15, 'rsi_len': 23, 'rsi_oversold': 21.83520514928736, 'rsi_overbought': 84.96413133742654, 'stoch_k': 23, 'stoch_d': 3, 'willr_len': 20, 'cci_len': 29, 'tp_atr_mult': 6.811478565297005}
2025-07-16 23:23:20,638 - root - WARNING - Trial 72 failed - ValueError: Invalid parameters sampled: {'ema_fast': 27, 'ema_slow': 23, 'atr_len': 20, 'vol_sma_len': 11, 'sl_atr_mult': 2.905819509934385, 'trail_atr_mult': 1.4500651128193665, 'risk_multiplier': 2.01054890339329, 'min_bars_between_trades': 2, 'use_volume_filter': True, 'bb_len': 11, 'bb_std': 3.1333842149521183, 'bb_oversold': 0.19320041128853987, 'bb_overbought': 0.7786083429625804, 'mfi_len': 13, 'rsi_len': 21, 'rsi_oversold': 23.18482811754237, 'rsi_overbought': 79.66023485059522, 'stoch_k': 25, 'stoch_d': 4, 'willr_len': 19, 'cci_len': 28, 'tp_atr_mult': 6.361524822152274}
2025-07-16 23:23:20,845 - __main__ - INFO - BTCUSDT Reversion: Best score = -999.000
2025-07-16 23:23:20,845 - __main__ - INFO - Running walk-forward validation for BTCUSDT Reversion
2025-07-16 23:23:20,845 - __main__ - INFO - Total data available: 15.6 days (1500 bars)
2025-07-16 23:23:20,845 - __main__ - INFO - Walk-forward parameters: IS=7d, OOS=3d, step=1d
2025-07-16 23:23:23,694 - __main__ - WARNING - No valid walk-forward windows for BTCUSDT Reversion
2025-07-16 23:23:23,694 - __main__ - INFO - Attempting single-split validation as fallback...
2025-07-16 23:23:24,305 - __main__ - WARNING - Single-split validation also failed
2025-07-16 23:23:24,305 - __main__ - WARNING - All validation attempts failed for BTCUSDT Reversion, using optimization results only
2025-07-16 23:23:24,305 - __main__ - INFO - BTCUSDT Reversion: 1 valid WF windows, WF score = 3.000
2025-07-16 23:23:24,305 - __main__ - INFO - Optimizing Adaptive strategy for BTCUSDT
2025-07-16 23:23:28,511 - root - WARNING - Trial 45 failed - ValueError: Invalid parameters sampled: {'ema_fast': 23, 'ema_slow': 20, 'atr_len': 19, 'vol_sma_len': 19, 'sl_atr_mult': 3.517566919003947, 'trail_atr_mult': 2.8264846037232485, 'risk_multiplier': 2.096611900253328, 'min_bars_between_trades': 1, 'use_volume_filter': True, 'st_len': 15, 'st_mult': 3.863382446343641, 'adx_len': 25, 'adx_threshold': 16.598149517986137, 'trend_confirmations': 1, 'macd_fast': 7, 'macd_slow': 41, 'macd_signal': 14, 'rsi_len': 15, 'bb_len': 18, 'bb_std': 2.677951984496531, 'bb_oversold': 0.10148205931168032, 'bb_overbought': 0.9120116499563181, 'mfi_len': 14, 'rsi_oversold': 29.74684889673103, 'rsi_overbought': 81.18365755775564, 'stoch_k': 24, 'stoch_d': 4, 'willr_len': 18, 'cci_len': 10, 'tp_atr_mult': 11.826321125310576}
2025-07-16 23:23:29,063 - __main__ - INFO - BTCUSDT Adaptive: Best score = 1.000
2025-07-16 23:23:29,063 - __main__ - INFO - Running walk-forward validation for BTCUSDT Adaptive
2025-07-16 23:23:29,063 - __main__ - INFO - Total data available: 15.6 days (1500 bars)
2025-07-16 23:23:29,063 - __main__ - INFO - Walk-forward parameters: IS=7d, OOS=3d, step=1d
2025-07-16 23:25:28,287 - __main__ - INFO - Ultratrader Orchestrator initialized
2025-07-16 23:25:28,288 - __main__ - INFO - Ultratrader Orchestrator initialized
2025-07-16 23:25:28,288 - __main__ - INFO - Starting Ultratrader optimization pipeline
2025-07-16 23:25:29,113 - __main__ - INFO - Binance client initialized successfully
2025-07-16 23:25:29,113 - __main__ - INFO - Optimizing strategies for symbols: ['BTCUSDT']
2025-07-16 23:25:29,113 - __main__ - INFO - Fetching market data for BTCUSDT
2025-07-16 23:25:29,126 - __main__ - INFO - Retrieved 1500 bars for BTCUSDT (15m)
2025-07-16 23:25:29,126 - __main__ - INFO - Retrieved 1500 bars for BTCUSDT (1h)
2025-07-16 23:25:29,126 - __main__ - INFO - Optimizing Trend strategy for BTCUSDT
2025-07-16 23:25:31,463 - root - WARNING - Trial 20 failed - ValueError: Invalid parameters sampled: {'ema_fast': 24, 'ema_slow': 20, 'atr_len': 15, 'vol_sma_len': 14, 'sl_atr_mult': 3.485927447555941, 'trail_atr_mult': 3.5783489442560805, 'risk_multiplier': 1.5747652813924093, 'min_bars_between_trades': 1, 'use_volume_filter': False, 'st_len': 27, 'st_mult': 2.808921970095546, 'adx_len': 12, 'adx_threshold': 33.167069582769685, 'trend_confirmations': 1, 'macd_fast': 18, 'macd_slow': 32, 'macd_signal': 10, 'rsi_len': 22, 'tp_atr_mult': 11.727319378823598}
2025-07-16 23:25:31,516 - root - WARNING - Trial 21 failed - ValueError: Invalid parameters sampled: {'ema_fast': 9, 'ema_slow': 87, 'atr_len': 8, 'vol_sma_len': 39, 'sl_atr_mult': 2.7775383895727725, 'trail_atr_mult': 3.2543834614759204, 'risk_multiplier': 2.187132372488157, 'min_bars_between_trades': 2, 'use_volume_filter': True, 'st_len': 39, 'st_mult': 4.897131804446349, 'adx_len': 25, 'adx_threshold': 39.15181438666859, 'trend_confirmations': 2, 'macd_fast': 18, 'macd_slow': 18, 'macd_signal': 7, 'rsi_len': 9, 'tp_atr_mult': 9.020279162109832}
2025-07-16 23:25:34,956 - root - WARNING - Trial 59 failed - ValueError: Invalid parameters sampled: {'ema_fast': 14, 'ema_slow': 59, 'atr_len': 8, 'vol_sma_len': 29, 'sl_atr_mult': 3.2509101039287787, 'trail_atr_mult': 3.9662996584480643, 'risk_multiplier': 1.7714089707763776, 'min_bars_between_trades': 2, 'use_volume_filter': True, 'st_len': 5, 'st_mult': 3.56192219407724, 'adx_len': 16, 'adx_threshold': 38.72232917583658, 'trend_confirmations': 1, 'macd_fast': 19, 'macd_slow': 18, 'macd_signal': 15, 'rsi_len': 23, 'tp_atr_mult': 10.36084829782843}
2025-07-16 23:25:36,205 - root - WARNING - Trial 74 failed - ValueError: Invalid parameters sampled: {'ema_fast': 27, 'ema_slow': 27, 'atr_len': 14, 'vol_sma_len': 15, 'sl_atr_mult': 2.717166191777667, 'trail_atr_mult': 1.1200950593000483, 'risk_multiplier': 2.8382456959273012, 'min_bars_between_trades': 2, 'use_volume_filter': False, 'st_len': 31, 'st_mult': 2.1254681922262706, 'adx_len': 18, 'adx_threshold': 26.657029521861457, 'trend_confirmations': 2, 'macd_fast': 9, 'macd_slow': 44, 'macd_signal': 14, 'rsi_len': 12, 'tp_atr_mult': 12.717318029948522}
2025-07-16 23:25:36,240 - __main__ - INFO - BTCUSDT Trend: Best score = 1.000
2025-07-16 23:25:36,240 - __main__ - INFO - Running walk-forward validation for BTCUSDT Trend
2025-07-16 23:25:36,240 - __main__ - INFO - Total data available: 15.6 days (1500 bars)
2025-07-16 23:25:36,241 - __main__ - INFO - Walk-forward parameters: IS=7d, OOS=3d, step=1d
2025-07-16 23:25:39,387 - __main__ - INFO - BTCUSDT Trend: 6 valid WF windows, WF score = 0.600
2025-07-16 23:25:39,387 - __main__ - INFO - Optimizing Reversion strategy for BTCUSDT
2025-07-16 23:25:39,528 - root - WARNING - Trial 9 failed - ValueError: Invalid parameters sampled: {'ema_fast': 22, 'ema_slow': 20, 'atr_len': 10, 'vol_sma_len': 26, 'sl_atr_mult': 3.3837903953853865, 'trail_atr_mult': 2.8862760304083217, 'risk_multiplier': 2.0606732736513997, 'min_bars_between_trades': 2, 'use_volume_filter': False, 'bb_len': 33, 'bb_std': 2.6941556678085936, 'bb_oversold': 0.21532138441366092, 'bb_overbought': 0.901250965229079, 'mfi_len': 21, 'rsi_len': 9, 'rsi_oversold': 24.19289507648584, 'rsi_overbought': 66.63005919204313, 'stoch_k': 12, 'stoch_d': 10, 'willr_len': 17, 'cci_len': 28, 'tp_atr_mult': 11.680247633975366}
2025-07-16 23:25:41,271 - root - WARNING - Trial 38 failed - ValueError: Invalid parameters sampled: {'ema_fast': 28, 'ema_slow': 28, 'atr_len': 21, 'vol_sma_len': 10, 'sl_atr_mult': 2.9278498507634376, 'trail_atr_mult': 1.9666048606821223, 'risk_multiplier': 2.3293316718453005, 'min_bars_between_trades': 2, 'use_volume_filter': True, 'bb_len': 10, 'bb_std': 3.2194542190564093, 'bb_oversold': 0.22554953904969413, 'bb_overbought': 0.7778096801332361, 'mfi_len': 12, 'rsi_len': 21, 'rsi_oversold': 19.75495004598275, 'rsi_overbought': 76.69695293655664, 'stoch_k': 18, 'stoch_d': 7, 'willr_len': 21, 'cci_len': 28, 'tp_atr_mult': 7.4623560160597275}
2025-07-16 23:25:41,333 - root - WARNING - Trial 39 failed - ValueError: Invalid parameters sampled: {'ema_fast': 28, 'ema_slow': 27, 'atr_len': 22, 'vol_sma_len': 9, 'sl_atr_mult': 2.582740951778386, 'trail_atr_mult': 1.578044383504116, 'risk_multiplier': 2.2905742004845946, 'min_bars_between_trades': 2, 'use_volume_filter': True, 'bb_len': 10, 'bb_std': 3.339680215052894, 'bb_oversold': 0.19700236207968613, 'bb_overbought': 0.7739425944475213, 'mfi_len': 11, 'rsi_len': 21, 'rsi_oversold': 19.425370329745363, 'rsi_overbought': 76.97098756286574, 'stoch_k': 20, 'stoch_d': 5, 'willr_len': 21, 'cci_len': 28, 'tp_atr_mult': 7.553483634736278}
2025-07-16 23:25:41,396 - root - WARNING - Trial 40 failed - ValueError: Invalid parameters sampled: {'ema_fast': 28, 'ema_slow': 28, 'atr_len': 22, 'vol_sma_len': 9, 'sl_atr_mult': 2.139570598336691, 'trail_atr_mult': 1.6082421608630826, 'risk_multiplier': 2.290468195075918, 'min_bars_between_trades': 2, 'use_volume_filter': True, 'bb_len': 14, 'bb_std': 3.2981858198824163, 'bb_oversold': 0.2238781893851314, 'bb_overbought': 0.7725337596029574, 'mfi_len': 12, 'rsi_len': 21, 'rsi_oversold': 19.085700443923944, 'rsi_overbought': 77.07469088399705, 'stoch_k': 22, 'stoch_d': 5, 'willr_len': 21, 'cci_len': 28, 'tp_atr_mult': 7.540171586717557}
2025-07-16 23:25:41,471 - root - WARNING - Trial 41 failed - ValueError: Invalid parameters sampled: {'ema_fast': 28, 'ema_slow': 25, 'atr_len': 22, 'vol_sma_len': 10, 'sl_atr_mult': 2.085528607027352, 'trail_atr_mult': 1.576563864782924, 'risk_multiplier': 2.324488590251017, 'min_bars_between_trades': 2, 'use_volume_filter': True, 'bb_len': 10, 'bb_std': 3.339696533719319, 'bb_oversold': 0.22411276920188836, 'bb_overbought': 0.7696721943555463, 'mfi_len': 12, 'rsi_len': 21, 'rsi_oversold': 19.357572587845752, 'rsi_overbought': 77.20774542827532, 'stoch_k': 24, 'stoch_d': 5, 'willr_len': 21, 'cci_len': 28, 'tp_atr_mult': 7.499188692645576}
2025-07-16 23:25:41,535 - root - WARNING - Trial 42 failed - ValueError: Invalid parameters sampled: {'ema_fast': 28, 'ema_slow': 27, 'atr_len': 24, 'vol_sma_len': 10, 'sl_atr_mult': 2.053218620287528, 'trail_atr_mult': 1.573575640711633, 'risk_multiplier': 2.3124996508635474, 'min_bars_between_trades': 2, 'use_volume_filter': True, 'bb_len': 10, 'bb_std': 3.2708692587975916, 'bb_oversold': 0.2242373280266595, 'bb_overbought': 0.7684420252149379, 'mfi_len': 13, 'rsi_len': 21, 'rsi_oversold': 15.449813380348727, 'rsi_overbought': 77.21587070041683, 'stoch_k': 24, 'stoch_d': 5, 'willr_len': 21, 'cci_len': 28, 'tp_atr_mult': 7.495326358491884}
2025-07-16 23:25:41,683 - root - WARNING - Trial 44 failed - ValueError: Invalid parameters sampled: {'ema_fast': 30, 'ema_slow': 20, 'atr_len': 20, 'vol_sma_len': 8, 'sl_atr_mult': 2.5737207059828924, 'trail_atr_mult': 1.974834289529383, 'risk_multiplier': 2.4998834537245713, 'min_bars_between_trades': 2, 'use_volume_filter': True, 'bb_len': 11, 'bb_std': 3.148672221037543, 'bb_oversold': 0.19868215660530458, 'bb_overbought': 0.7596702973664605, 'mfi_len': 12, 'rsi_len': 22, 'rsi_oversold': 16.668896548574843, 'rsi_overbought': 76.68932932238167, 'stoch_k': 21, 'stoch_d': 5, 'willr_len': 19, 'cci_len': 25, 'tp_atr_mult': 7.656301443336177}
2025-07-16 23:25:41,838 - root - WARNING - Trial 46 failed - ValueError: Invalid parameters sampled: {'ema_fast': 28, 'ema_slow': 23, 'atr_len': 22, 'vol_sma_len': 8, 'sl_atr_mult': 2.1354210429886287, 'trail_atr_mult': 1.379936034102647, 'risk_multiplier': 2.053177046421258, 'min_bars_between_trades': 2, 'use_volume_filter': True, 'bb_len': 10, 'bb_std': 3.1951981047583535, 'bb_oversold': 0.19591147252619096, 'bb_overbought': 0.7852919073373079, 'mfi_len': 15, 'rsi_len': 23, 'rsi_oversold': 21.74305268747055, 'rsi_overbought': 81.3616679383206, 'stoch_k': 23, 'stoch_d': 4, 'willr_len': 20, 'cci_len': 29, 'tp_atr_mult': 6.893431884835614}
2025-07-16 23:25:42,064 - root - WARNING - Trial 49 failed - ValueError: Invalid parameters sampled: {'ema_fast': 27, 'ema_slow': 23, 'atr_len': 22, 'vol_sma_len': 11, 'sl_atr_mult': 2.9094990683265194, 'trail_atr_mult': 1.5089702546496626, 'risk_multiplier': 2.6153874071872822, 'min_bars_between_trades': 2, 'use_volume_filter': True, 'bb_len': 11, 'bb_std': 3.062948782075203, 'bb_oversold': 0.23615852577630808, 'bb_overbought': 0.7991009032669187, 'mfi_len': 8, 'rsi_len': 25, 'rsi_oversold': 20.688486237757125, 'rsi_overbought': 73.84031884123674, 'stoch_k': 20, 'stoch_d': 9, 'willr_len': 17, 'cci_len': 24, 'tp_atr_mult': 9.331241179956066}
2025-07-16 23:25:42,132 - root - WARNING - Trial 50 failed - ValueError: Invalid parameters sampled: {'ema_fast': 26, 'ema_slow': 26, 'atr_len': 18, 'vol_sma_len': 10, 'sl_atr_mult': 2.1085509684725183, 'trail_atr_mult': 1.951761311074288, 'risk_multiplier': 2.840808250129175, 'min_bars_between_trades': 1, 'use_volume_filter': True, 'bb_len': 14, 'bb_std': 3.2046926464566483, 'bb_oversold': 0.2092639970966826, 'bb_overbought': 0.9070199551915216, 'mfi_len': 15, 'rsi_len': 24, 'rsi_oversold': 16.35460361682691, 'rsi_overbought': 81.19216671890207, 'stoch_k': 24, 'stoch_d': 4, 'willr_len': 22, 'cci_len': 29, 'tp_atr_mult': 8.523205999820988}
2025-07-16 23:25:42,203 - root - WARNING - Trial 51 failed - ValueError: Invalid parameters sampled: {'ema_fast': 29, 'ema_slow': 28, 'atr_len': 24, 'vol_sma_len': 10, 'sl_atr_mult': 2.035730163811321, 'trail_atr_mult': 1.555792684949628, 'risk_multiplier': 2.2823252631616056, 'min_bars_between_trades': 2, 'use_volume_filter': True, 'bb_len': 10, 'bb_std': 3.2762646414958367, 'bb_oversold': 0.22434648428100784, 'bb_overbought': 0.7642943707665197, 'mfi_len': 13, 'rsi_len': 21, 'rsi_oversold': 18.749263898866108, 'rsi_overbought': 77.21188243810157, 'stoch_k': 24, 'stoch_d': 5, 'willr_len': 21, 'cci_len': 28, 'tp_atr_mult': 7.257640073969456}
2025-07-16 23:25:42,368 - root - WARNING - Trial 53 failed - ValueError: Invalid parameters sampled: {'ema_fast': 28, 'ema_slow': 26, 'atr_len': 24, 'vol_sma_len': 12, 'sl_atr_mult': 2.2534739616050032, 'trail_atr_mult': 1.4208199828904369, 'risk_multiplier': 2.4435092195817085, 'min_bars_between_trades': 2, 'use_volume_filter': True, 'bb_len': 10, 'bb_std': 3.3643764101439237, 'bb_oversold': 0.2131182544636931, 'bb_overbought': 0.7833757765855353, 'mfi_len': 12, 'rsi_len': 20, 'rsi_oversold': 19.84607785501496, 'rsi_overbought': 79.90564306709261, 'stoch_k': 22, 'stoch_d': 4, 'willr_len': 21, 'cci_len': 28, 'tp_atr_mult': 6.903521540061959}
2025-07-16 23:25:42,434 - root - WARNING - Trial 54 failed - ValueError: Invalid parameters sampled: {'ema_fast': 26, 'ema_slow': 20, 'atr_len': 21, 'vol_sma_len': 25, 'sl_atr_mult': 2.426622006397972, 'trail_atr_mult': 1.232189953873675, 'risk_multiplier': 2.079209428588707, 'min_bars_between_trades': 2, 'use_volume_filter': True, 'bb_len': 11, 'bb_std': 3.240542585232071, 'bb_oversold': 0.2192880547996075, 'bb_overbought': 0.7502283824954883, 'mfi_len': 20, 'rsi_len': 19, 'rsi_oversold': 15.991048137831843, 'rsi_overbought': 77.44663239388291, 'stoch_k': 23, 'stoch_d': 5, 'willr_len': 19, 'cci_len': 26, 'tp_atr_mult': 6.004083624371795}
2025-07-16 23:25:42,671 - root - WARNING - Trial 57 failed - ValueError: Invalid parameters sampled: {'ema_fast': 24, 'ema_slow': 23, 'atr_len': 20, 'vol_sma_len': 11, 'sl_atr_mult': 2.003858056359904, 'trail_atr_mult': 2.338129217405868, 'risk_multiplier': 2.2972706576384327, 'min_bars_between_trades': 2, 'use_volume_filter': True, 'bb_len': 15, 'bb_std': 2.647164961679343, 'bb_oversold': 0.20631337030425348, 'bb_overbought': 0.897870403802119, 'mfi_len': 18, 'rsi_len': 18, 'rsi_oversold': 20.302626019484087, 'rsi_overbought': 73.8565463159248, 'stoch_k': 18, 'stoch_d': 5, 'willr_len': 19, 'cci_len': 24, 'tp_atr_mult': 9.035407633416574}
2025-07-16 23:25:42,742 - root - WARNING - Trial 58 failed - ValueError: Invalid parameters sampled: {'ema_fast': 27, 'ema_slow': 27, 'atr_len': 22, 'vol_sma_len': 15, 'sl_atr_mult': 2.3371253178294364, 'trail_atr_mult': 1.4705046571574578, 'risk_multiplier': 2.515480893336058, 'min_bars_between_trades': 2, 'use_volume_filter': True, 'bb_len': 10, 'bb_std': 3.392916597865878, 'bb_oversold': 0.18515912407712096, 'bb_overbought': 0.7619635279342432, 'mfi_len': 14, 'rsi_len': 22, 'rsi_oversold': 15.118653240291064, 'rsi_overbought': 77.64691026370679, 'stoch_k': 25, 'stoch_d': 4, 'willr_len': 24, 'cci_len': 28, 'tp_atr_mult': 10.028599500779801}
2025-07-16 23:25:42,978 - root - WARNING - Trial 61 failed - ValueError: Invalid parameters sampled: {'ema_fast': 30, 'ema_slow': 22, 'atr_len': 20, 'vol_sma_len': 8, 'sl_atr_mult': 2.604354305529716, 'trail_atr_mult': 1.9959486858062352, 'risk_multiplier': 2.5012504500613355, 'min_bars_between_trades': 2, 'use_volume_filter': True, 'bb_len': 11, 'bb_std': 3.1601932262257084, 'bb_oversold': 0.19693116244623232, 'bb_overbought': 0.7581625432359048, 'mfi_len': 12, 'rsi_len': 22, 'rsi_oversold': 16.52556430264827, 'rsi_overbought': 76.16105805449178, 'stoch_k': 21, 'stoch_d': 5, 'willr_len': 19, 'cci_len': 25, 'tp_atr_mult': 7.504151221451527}
2025-07-16 23:25:43,048 - root - WARNING - Trial 62 failed - ValueError: Invalid parameters sampled: {'ema_fast': 28, 'ema_slow': 20, 'atr_len': 20, 'vol_sma_len': 10, 'sl_atr_mult': 2.452314085541029, 'trail_atr_mult': 1.988128658274577, 'risk_multiplier': 2.398591908688268, 'min_bars_between_trades': 2, 'use_volume_filter': True, 'bb_len': 11, 'bb_std': 3.259435646307269, 'bb_oversold': 0.20255620482143513, 'bb_overbought': 0.7880491769667838, 'mfi_len': 12, 'rsi_len': 22, 'rsi_oversold': 16.787344652021293, 'rsi_overbought': 76.43737260515391, 'stoch_k': 21, 'stoch_d': 5, 'willr_len': 20, 'cci_len': 26, 'tp_atr_mult': 7.649966068310504}
2025-07-16 23:25:43,194 - root - WARNING - Trial 64 failed - ValueError: Invalid parameters sampled: {'ema_fast': 26, 'ema_slow': 25, 'atr_len': 23, 'vol_sma_len': 11, 'sl_atr_mult': 3.004173384670561, 'trail_atr_mult': 1.6536171505284412, 'risk_multiplier': 2.1113486532058716, 'min_bars_between_trades': 2, 'use_volume_filter': True, 'bb_len': 11, 'bb_std': 2.7573691115127428, 'bb_oversold': 0.1682628659308191, 'bb_overbought': 0.7737283480978066, 'mfi_len': 15, 'rsi_len': 23, 'rsi_oversold': 17.51948399491222, 'rsi_overbought': 78.5812745635762, 'stoch_k': 23, 'stoch_d': 4, 'willr_len': 18, 'cci_len': 29, 'tp_atr_mult': 8.292405503495807}
2025-07-16 23:25:43,276 - root - WARNING - Trial 65 failed - ValueError: Invalid parameters sampled: {'ema_fast': 29, 'ema_slow': 28, 'atr_len': 18, 'vol_sma_len': 27, 'sl_atr_mult': 2.152340790020311, 'trail_atr_mult': 2.201524667090991, 'risk_multiplier': 2.5608964848985805, 'min_bars_between_trades': 2, 'use_volume_filter': True, 'bb_len': 14, 'bb_std': 3.0410966324611257, 'bb_oversold': 0.1838017738141655, 'bb_overbought': 0.8014094639347624, 'mfi_len': 11, 'rsi_len': 20, 'rsi_oversold': 21.23436177846322, 'rsi_overbought': 77.71341299483942, 'stoch_k': 19, 'stoch_d': 6, 'willr_len': 23, 'cci_len': 28, 'tp_atr_mult': 6.818166264169015}
2025-07-16 23:25:43,346 - root - WARNING - Trial 66 failed - ValueError: Invalid parameters sampled: {'ema_fast': 30, 'ema_slow': 22, 'atr_len': 21, 'vol_sma_len': 8, 'sl_atr_mult': 2.805908353631528, 'trail_atr_mult': 2.3084179709944914, 'risk_multiplier': 2.159294975914167, 'min_bars_between_trades': 2, 'use_volume_filter': True, 'bb_len': 24, 'bb_std': 3.314007136917876, 'bb_oversold': 0.21272413686481936, 'bb_overbought': 0.7878278939626071, 'mfi_len': 21, 'rsi_len': 17, 'rsi_oversold': 19.377373008044117, 'rsi_overbought': 73.15415640346825, 'stoch_k': 18, 'stoch_d': 5, 'willr_len': 22, 'cci_len': 27, 'tp_atr_mult': 7.7114801855294}
2025-07-16 23:25:43,501 - root - WARNING - Trial 68 failed - ValueError: Invalid parameters sampled: {'ema_fast': 27, 'ema_slow': 20, 'atr_len': 22, 'vol_sma_len': 16, 'sl_atr_mult': 2.517394131073412, 'trail_atr_mult': 1.135729037632433, 'risk_multiplier': 2.3480934522753603, 'min_bars_between_trades': 2, 'use_volume_filter': True, 'bb_len': 31, 'bb_std': 3.1927456362669875, 'bb_oversold': 0.20046707445153755, 'bb_overbought': 0.8536812803449829, 'mfi_len': 14, 'rsi_len': 22, 'rsi_oversold': 15.92303971158994, 'rsi_overbought': 70.93461983690983, 'stoch_k': 19, 'stoch_d': 4, 'willr_len': 20, 'cci_len': 30, 'tp_atr_mult': 11.040629249380133}
2025-07-16 23:25:43,735 - root - WARNING - Trial 71 failed - ValueError: Invalid parameters sampled: {'ema_fast': 28, 'ema_slow': 24, 'atr_len': 22, 'vol_sma_len': 8, 'sl_atr_mult': 2.124186286860088, 'trail_atr_mult': 1.3561024277507596, 'risk_multiplier': 2.053400883077793, 'min_bars_between_trades': 2, 'use_volume_filter': True, 'bb_len': 10, 'bb_std': 3.2211709140906395, 'bb_oversold': 0.19691236509764556, 'bb_overbought': 0.7847197050486078, 'mfi_len': 15, 'rsi_len': 23, 'rsi_oversold': 21.83520514928736, 'rsi_overbought': 84.96413133742654, 'stoch_k': 23, 'stoch_d': 3, 'willr_len': 20, 'cci_len': 29, 'tp_atr_mult': 6.811478565297005}
2025-07-16 23:25:43,813 - root - WARNING - Trial 72 failed - ValueError: Invalid parameters sampled: {'ema_fast': 27, 'ema_slow': 23, 'atr_len': 20, 'vol_sma_len': 11, 'sl_atr_mult': 2.905819509934385, 'trail_atr_mult': 1.4500651128193665, 'risk_multiplier': 2.01054890339329, 'min_bars_between_trades': 2, 'use_volume_filter': True, 'bb_len': 11, 'bb_std': 3.1333842149521183, 'bb_oversold': 0.19320041128853987, 'bb_overbought': 0.7786083429625804, 'mfi_len': 13, 'rsi_len': 21, 'rsi_oversold': 23.18482811754237, 'rsi_overbought': 79.66023485059522, 'stoch_k': 25, 'stoch_d': 4, 'willr_len': 19, 'cci_len': 28, 'tp_atr_mult': 6.361524822152274}
2025-07-16 23:25:44,019 - __main__ - INFO - BTCUSDT Reversion: Best score = -999.000
2025-07-16 23:25:44,019 - __main__ - INFO - Running walk-forward validation for BTCUSDT Reversion
2025-07-16 23:25:44,019 - __main__ - INFO - Total data available: 15.6 days (1500 bars)
2025-07-16 23:25:44,019 - __main__ - INFO - Walk-forward parameters: IS=7d, OOS=3d, step=1d
2025-07-16 23:25:46,893 - __main__ - WARNING - No valid walk-forward windows for BTCUSDT Reversion
2025-07-16 23:25:46,893 - __main__ - INFO - Attempting single-split validation as fallback...
2025-07-16 23:25:47,493 - __main__ - WARNING - Single-split validation also failed
2025-07-16 23:25:47,493 - __main__ - WARNING - All validation attempts failed for BTCUSDT Reversion, using optimization results only
2025-07-16 23:25:47,493 - __main__ - INFO - BTCUSDT Reversion: 1 valid WF windows, WF score = 3.000
2025-07-16 23:25:47,493 - __main__ - INFO - Optimizing Adaptive strategy for BTCUSDT
2025-07-16 23:25:51,566 - root - WARNING - Trial 45 failed - ValueError: Invalid parameters sampled: {'ema_fast': 23, 'ema_slow': 20, 'atr_len': 19, 'vol_sma_len': 19, 'sl_atr_mult': 3.517566919003947, 'trail_atr_mult': 2.8264846037232485, 'risk_multiplier': 2.096611900253328, 'min_bars_between_trades': 1, 'use_volume_filter': True, 'st_len': 15, 'st_mult': 3.863382446343641, 'adx_len': 25, 'adx_threshold': 16.598149517986137, 'trend_confirmations': 1, 'macd_fast': 7, 'macd_slow': 41, 'macd_signal': 14, 'rsi_len': 15, 'bb_len': 18, 'bb_std': 2.677951984496531, 'bb_oversold': 0.10148205931168032, 'bb_overbought': 0.9120116499563181, 'mfi_len': 14, 'rsi_oversold': 29.74684889673103, 'rsi_overbought': 81.18365755775564, 'stoch_k': 24, 'stoch_d': 4, 'willr_len': 18, 'cci_len': 10, 'tp_atr_mult': 11.826321125310576}
2025-07-16 23:25:52,064 - __main__ - INFO - BTCUSDT Adaptive: Best score = 1.000
2025-07-16 23:25:52,065 - __main__ - INFO - Running walk-forward validation for BTCUSDT Adaptive
2025-07-16 23:25:52,065 - __main__ - INFO - Total data available: 15.6 days (1500 bars)
2025-07-16 23:25:52,065 - __main__ - INFO - Walk-forward parameters: IS=7d, OOS=3d, step=1d
2025-07-16 23:25:56,249 - __main__ - INFO - BTCUSDT Adaptive: 6 valid WF windows, WF score = 0.600
2025-07-16 23:25:56,249 - __main__ - INFO - Validating and scoring strategies with anti-overfitting criteria
2025-07-16 23:25:56,249 - __main__ - INFO - BTCUSDT_Trend failed robustness validation. Failed checks: ['min_sortino', 'min_profit_factor', 'oos_positive_return', 'oos_min_sortino']
2025-07-16 23:25:56,249 - __main__ - INFO -   Robustness score: 0.50, Sortino stability: 0.00
2025-07-16 23:25:56,250 - __main__ - INFO - BTCUSDT_Reversion failed robustness validation. Failed checks: ['min_trades', 'min_win_rate']
2025-07-16 23:25:56,250 - __main__ - INFO -   Robustness score: 0.75, Sortino stability: 0.00
2025-07-16 23:25:56,250 - __main__ - INFO - BTCUSDT_Adaptive failed robustness validation. Failed checks: ['min_sortino', 'min_profit_factor', 'oos_positive_return', 'oos_min_sortino']
2025-07-16 23:25:56,250 - __main__ - INFO -   Robustness score: 0.50, Sortino stability: 0.00
2025-07-16 23:25:56,250 - __main__ - INFO - Saving optimization results
2025-07-16 23:25:56,250 - __main__ - WARNING - No validated strategies to save
2025-07-16 23:25:56,250 - __main__ - WARNING - Optimization completed but no strategies passed validation
2025-07-16 23:25:56,251 - __main__ - INFO - Binance client connection closed
2025-07-16 23:26:22,377 - __main__ - INFO - Ultratrader Orchestrator initialized
2025-07-16 23:26:22,377 - __main__ - INFO - Ultratrader Orchestrator initialized
2025-07-16 23:26:22,377 - __main__ - INFO - Starting Ultratrader optimization pipeline
2025-07-16 23:26:23,066 - __main__ - INFO - Binance client initialized successfully
2025-07-16 23:26:23,066 - __main__ - INFO - Optimizing strategies for symbols: ['BTCUSDT']
2025-07-16 23:26:23,066 - __main__ - INFO - Fetching market data for BTCUSDT
2025-07-16 23:26:23,078 - __main__ - INFO - Retrieved 1500 bars for BTCUSDT (15m)
2025-07-16 23:26:23,078 - __main__ - INFO - Retrieved 1500 bars for BTCUSDT (1h)
2025-07-16 23:26:23,078 - __main__ - INFO - Optimizing Trend strategy for BTCUSDT
2025-07-16 23:26:25,399 - root - WARNING - Trial 20 failed - ValueError: Invalid parameters sampled: {'ema_fast': 24, 'ema_slow': 20, 'atr_len': 15, 'vol_sma_len': 14, 'sl_atr_mult': 3.485927447555941, 'trail_atr_mult': 3.5783489442560805, 'risk_multiplier': 1.5747652813924093, 'min_bars_between_trades': 1, 'use_volume_filter': False, 'st_len': 27, 'st_mult': 2.808921970095546, 'adx_len': 12, 'adx_threshold': 33.167069582769685, 'trend_confirmations': 1, 'macd_fast': 18, 'macd_slow': 32, 'macd_signal': 10, 'rsi_len': 22, 'tp_atr_mult': 11.727319378823598}
2025-07-16 23:26:25,473 - root - WARNING - Trial 21 failed - ValueError: Invalid parameters sampled: {'ema_fast': 9, 'ema_slow': 87, 'atr_len': 8, 'vol_sma_len': 39, 'sl_atr_mult': 2.7775383895727725, 'trail_atr_mult': 3.2543834614759204, 'risk_multiplier': 2.187132372488157, 'min_bars_between_trades': 2, 'use_volume_filter': True, 'st_len': 39, 'st_mult': 4.897131804446349, 'adx_len': 25, 'adx_threshold': 39.15181438666859, 'trend_confirmations': 2, 'macd_fast': 18, 'macd_slow': 18, 'macd_signal': 7, 'rsi_len': 9, 'tp_atr_mult': 9.020279162109832}
2025-07-16 23:26:28,319 - root - WARNING - Trial 59 failed - ValueError: Invalid parameters sampled: {'ema_fast': 14, 'ema_slow': 59, 'atr_len': 8, 'vol_sma_len': 29, 'sl_atr_mult': 3.2509101039287787, 'trail_atr_mult': 3.9662996584480643, 'risk_multiplier': 1.7714089707763776, 'min_bars_between_trades': 2, 'use_volume_filter': True, 'st_len': 5, 'st_mult': 3.56192219407724, 'adx_len': 16, 'adx_threshold': 38.72232917583658, 'trend_confirmations': 1, 'macd_fast': 19, 'macd_slow': 18, 'macd_signal': 15, 'rsi_len': 23, 'tp_atr_mult': 10.36084829782843}
2025-07-16 23:26:29,486 - root - WARNING - Trial 74 failed - ValueError: Invalid parameters sampled: {'ema_fast': 27, 'ema_slow': 27, 'atr_len': 14, 'vol_sma_len': 15, 'sl_atr_mult': 2.717166191777667, 'trail_atr_mult': 1.1200950593000483, 'risk_multiplier': 2.8382456959273012, 'min_bars_between_trades': 2, 'use_volume_filter': False, 'st_len': 31, 'st_mult': 2.1254681922262706, 'adx_len': 18, 'adx_threshold': 26.657029521861457, 'trend_confirmations': 2, 'macd_fast': 9, 'macd_slow': 44, 'macd_signal': 14, 'rsi_len': 12, 'tp_atr_mult': 12.717318029948522}
2025-07-16 23:26:29,522 - __main__ - INFO - BTCUSDT Trend: Best score = 1.000
2025-07-16 23:26:29,522 - __main__ - INFO - Running walk-forward validation for BTCUSDT Trend
2025-07-16 23:26:29,522 - __main__ - INFO - Total data available: 15.6 days (1500 bars)
2025-07-16 23:26:29,522 - __main__ - INFO - Walk-forward parameters: IS=7d, OOS=3d, step=1d
2025-07-16 23:26:32,910 - __main__ - INFO - BTCUSDT Trend: 6 valid WF windows, WF score = 0.600
2025-07-16 23:26:32,910 - __main__ - INFO - Optimizing Reversion strategy for BTCUSDT
2025-07-16 23:26:33,063 - root - WARNING - Trial 9 failed - ValueError: Invalid parameters sampled: {'ema_fast': 22, 'ema_slow': 20, 'atr_len': 10, 'vol_sma_len': 26, 'sl_atr_mult': 3.3837903953853865, 'trail_atr_mult': 2.8862760304083217, 'risk_multiplier': 2.0606732736513997, 'min_bars_between_trades': 2, 'use_volume_filter': False, 'bb_len': 33, 'bb_std': 2.6941556678085936, 'bb_oversold': 0.21532138441366092, 'bb_overbought': 0.901250965229079, 'mfi_len': 21, 'rsi_len': 9, 'rsi_oversold': 24.19289507648584, 'rsi_overbought': 66.63005919204313, 'stoch_k': 12, 'stoch_d': 10, 'willr_len': 17, 'cci_len': 28, 'tp_atr_mult': 11.680247633975366}
2025-07-16 23:26:34,816 - root - WARNING - Trial 38 failed - ValueError: Invalid parameters sampled: {'ema_fast': 28, 'ema_slow': 28, 'atr_len': 21, 'vol_sma_len': 10, 'sl_atr_mult': 2.9278498507634376, 'trail_atr_mult': 1.9666048606821223, 'risk_multiplier': 2.3293316718453005, 'min_bars_between_trades': 2, 'use_volume_filter': True, 'bb_len': 10, 'bb_std': 3.2194542190564093, 'bb_oversold': 0.22554953904969413, 'bb_overbought': 0.7778096801332361, 'mfi_len': 12, 'rsi_len': 21, 'rsi_oversold': 19.75495004598275, 'rsi_overbought': 76.69695293655664, 'stoch_k': 18, 'stoch_d': 7, 'willr_len': 21, 'cci_len': 28, 'tp_atr_mult': 7.4623560160597275}
2025-07-16 23:26:34,878 - root - WARNING - Trial 39 failed - ValueError: Invalid parameters sampled: {'ema_fast': 28, 'ema_slow': 27, 'atr_len': 22, 'vol_sma_len': 9, 'sl_atr_mult': 2.582740951778386, 'trail_atr_mult': 1.578044383504116, 'risk_multiplier': 2.2905742004845946, 'min_bars_between_trades': 2, 'use_volume_filter': True, 'bb_len': 10, 'bb_std': 3.339680215052894, 'bb_oversold': 0.19700236207968613, 'bb_overbought': 0.7739425944475213, 'mfi_len': 11, 'rsi_len': 21, 'rsi_oversold': 19.425370329745363, 'rsi_overbought': 76.97098756286574, 'stoch_k': 20, 'stoch_d': 5, 'willr_len': 21, 'cci_len': 28, 'tp_atr_mult': 7.553483634736278}
2025-07-16 23:26:34,939 - root - WARNING - Trial 40 failed - ValueError: Invalid parameters sampled: {'ema_fast': 28, 'ema_slow': 28, 'atr_len': 22, 'vol_sma_len': 9, 'sl_atr_mult': 2.139570598336691, 'trail_atr_mult': 1.6082421608630826, 'risk_multiplier': 2.290468195075918, 'min_bars_between_trades': 2, 'use_volume_filter': True, 'bb_len': 14, 'bb_std': 3.2981858198824163, 'bb_oversold': 0.2238781893851314, 'bb_overbought': 0.7725337596029574, 'mfi_len': 12, 'rsi_len': 21, 'rsi_oversold': 19.085700443923944, 'rsi_overbought': 77.07469088399705, 'stoch_k': 22, 'stoch_d': 5, 'willr_len': 21, 'cci_len': 28, 'tp_atr_mult': 7.540171586717557}
2025-07-16 23:26:35,012 - root - WARNING - Trial 41 failed - ValueError: Invalid parameters sampled: {'ema_fast': 28, 'ema_slow': 25, 'atr_len': 22, 'vol_sma_len': 10, 'sl_atr_mult': 2.085528607027352, 'trail_atr_mult': 1.576563864782924, 'risk_multiplier': 2.324488590251017, 'min_bars_between_trades': 2, 'use_volume_filter': True, 'bb_len': 10, 'bb_std': 3.339696533719319, 'bb_oversold': 0.22411276920188836, 'bb_overbought': 0.7696721943555463, 'mfi_len': 12, 'rsi_len': 21, 'rsi_oversold': 19.357572587845752, 'rsi_overbought': 77.20774542827532, 'stoch_k': 24, 'stoch_d': 5, 'willr_len': 21, 'cci_len': 28, 'tp_atr_mult': 7.499188692645576}
2025-07-16 23:26:35,073 - root - WARNING - Trial 42 failed - ValueError: Invalid parameters sampled: {'ema_fast': 28, 'ema_slow': 27, 'atr_len': 24, 'vol_sma_len': 10, 'sl_atr_mult': 2.053218620287528, 'trail_atr_mult': 1.573575640711633, 'risk_multiplier': 2.3124996508635474, 'min_bars_between_trades': 2, 'use_volume_filter': True, 'bb_len': 10, 'bb_std': 3.2708692587975916, 'bb_oversold': 0.2242373280266595, 'bb_overbought': 0.7684420252149379, 'mfi_len': 13, 'rsi_len': 21, 'rsi_oversold': 15.449813380348727, 'rsi_overbought': 77.21587070041683, 'stoch_k': 24, 'stoch_d': 5, 'willr_len': 21, 'cci_len': 28, 'tp_atr_mult': 7.495326358491884}
2025-07-16 23:26:35,219 - root - WARNING - Trial 44 failed - ValueError: Invalid parameters sampled: {'ema_fast': 30, 'ema_slow': 20, 'atr_len': 20, 'vol_sma_len': 8, 'sl_atr_mult': 2.5737207059828924, 'trail_atr_mult': 1.974834289529383, 'risk_multiplier': 2.4998834537245713, 'min_bars_between_trades': 2, 'use_volume_filter': True, 'bb_len': 11, 'bb_std': 3.148672221037543, 'bb_oversold': 0.19868215660530458, 'bb_overbought': 0.7596702973664605, 'mfi_len': 12, 'rsi_len': 22, 'rsi_oversold': 16.668896548574843, 'rsi_overbought': 76.68932932238167, 'stoch_k': 21, 'stoch_d': 5, 'willr_len': 19, 'cci_len': 25, 'tp_atr_mult': 7.656301443336177}
2025-07-16 23:26:35,363 - root - WARNING - Trial 46 failed - ValueError: Invalid parameters sampled: {'ema_fast': 28, 'ema_slow': 23, 'atr_len': 22, 'vol_sma_len': 8, 'sl_atr_mult': 2.1354210429886287, 'trail_atr_mult': 1.379936034102647, 'risk_multiplier': 2.053177046421258, 'min_bars_between_trades': 2, 'use_volume_filter': True, 'bb_len': 10, 'bb_std': 3.1951981047583535, 'bb_oversold': 0.19591147252619096, 'bb_overbought': 0.7852919073373079, 'mfi_len': 15, 'rsi_len': 23, 'rsi_oversold': 21.74305268747055, 'rsi_overbought': 81.3616679383206, 'stoch_k': 23, 'stoch_d': 4, 'willr_len': 20, 'cci_len': 29, 'tp_atr_mult': 6.893431884835614}
2025-07-16 23:26:35,585 - root - WARNING - Trial 49 failed - ValueError: Invalid parameters sampled: {'ema_fast': 27, 'ema_slow': 23, 'atr_len': 22, 'vol_sma_len': 11, 'sl_atr_mult': 2.9094990683265194, 'trail_atr_mult': 1.5089702546496626, 'risk_multiplier': 2.6153874071872822, 'min_bars_between_trades': 2, 'use_volume_filter': True, 'bb_len': 11, 'bb_std': 3.062948782075203, 'bb_oversold': 0.23615852577630808, 'bb_overbought': 0.7991009032669187, 'mfi_len': 8, 'rsi_len': 25, 'rsi_oversold': 20.688486237757125, 'rsi_overbought': 73.84031884123674, 'stoch_k': 20, 'stoch_d': 9, 'willr_len': 17, 'cci_len': 24, 'tp_atr_mult': 9.331241179956066}
2025-07-16 23:26:35,648 - root - WARNING - Trial 50 failed - ValueError: Invalid parameters sampled: {'ema_fast': 26, 'ema_slow': 26, 'atr_len': 18, 'vol_sma_len': 10, 'sl_atr_mult': 2.1085509684725183, 'trail_atr_mult': 1.951761311074288, 'risk_multiplier': 2.840808250129175, 'min_bars_between_trades': 1, 'use_volume_filter': True, 'bb_len': 14, 'bb_std': 3.2046926464566483, 'bb_oversold': 0.2092639970966826, 'bb_overbought': 0.9070199551915216, 'mfi_len': 15, 'rsi_len': 24, 'rsi_oversold': 16.35460361682691, 'rsi_overbought': 81.19216671890207, 'stoch_k': 24, 'stoch_d': 4, 'willr_len': 22, 'cci_len': 29, 'tp_atr_mult': 8.523205999820988}
2025-07-16 23:26:35,713 - root - WARNING - Trial 51 failed - ValueError: Invalid parameters sampled: {'ema_fast': 29, 'ema_slow': 28, 'atr_len': 24, 'vol_sma_len': 10, 'sl_atr_mult': 2.035730163811321, 'trail_atr_mult': 1.555792684949628, 'risk_multiplier': 2.2823252631616056, 'min_bars_between_trades': 2, 'use_volume_filter': True, 'bb_len': 10, 'bb_std': 3.2762646414958367, 'bb_oversold': 0.22434648428100784, 'bb_overbought': 0.7642943707665197, 'mfi_len': 13, 'rsi_len': 21, 'rsi_oversold': 18.749263898866108, 'rsi_overbought': 77.21188243810157, 'stoch_k': 24, 'stoch_d': 5, 'willr_len': 21, 'cci_len': 28, 'tp_atr_mult': 7.257640073969456}
2025-07-16 23:26:35,872 - root - WARNING - Trial 53 failed - ValueError: Invalid parameters sampled: {'ema_fast': 28, 'ema_slow': 26, 'atr_len': 24, 'vol_sma_len': 12, 'sl_atr_mult': 2.2534739616050032, 'trail_atr_mult': 1.4208199828904369, 'risk_multiplier': 2.4435092195817085, 'min_bars_between_trades': 2, 'use_volume_filter': True, 'bb_len': 10, 'bb_std': 3.3643764101439237, 'bb_oversold': 0.2131182544636931, 'bb_overbought': 0.7833757765855353, 'mfi_len': 12, 'rsi_len': 20, 'rsi_oversold': 19.84607785501496, 'rsi_overbought': 79.90564306709261, 'stoch_k': 22, 'stoch_d': 4, 'willr_len': 21, 'cci_len': 28, 'tp_atr_mult': 6.903521540061959}
2025-07-16 23:26:35,967 - root - WARNING - Trial 54 failed - ValueError: Invalid parameters sampled: {'ema_fast': 26, 'ema_slow': 20, 'atr_len': 21, 'vol_sma_len': 25, 'sl_atr_mult': 2.426622006397972, 'trail_atr_mult': 1.232189953873675, 'risk_multiplier': 2.079209428588707, 'min_bars_between_trades': 2, 'use_volume_filter': True, 'bb_len': 11, 'bb_std': 3.240542585232071, 'bb_oversold': 0.2192880547996075, 'bb_overbought': 0.7502283824954883, 'mfi_len': 20, 'rsi_len': 19, 'rsi_oversold': 15.991048137831843, 'rsi_overbought': 77.44663239388291, 'stoch_k': 23, 'stoch_d': 5, 'willr_len': 19, 'cci_len': 26, 'tp_atr_mult': 6.004083624371795}
2025-07-16 23:26:36,242 - root - WARNING - Trial 57 failed - ValueError: Invalid parameters sampled: {'ema_fast': 24, 'ema_slow': 23, 'atr_len': 20, 'vol_sma_len': 11, 'sl_atr_mult': 2.003858056359904, 'trail_atr_mult': 2.338129217405868, 'risk_multiplier': 2.2972706576384327, 'min_bars_between_trades': 2, 'use_volume_filter': True, 'bb_len': 15, 'bb_std': 2.647164961679343, 'bb_oversold': 0.20631337030425348, 'bb_overbought': 0.897870403802119, 'mfi_len': 18, 'rsi_len': 18, 'rsi_oversold': 20.302626019484087, 'rsi_overbought': 73.8565463159248, 'stoch_k': 18, 'stoch_d': 5, 'willr_len': 19, 'cci_len': 24, 'tp_atr_mult': 9.035407633416574}
2025-07-16 23:26:36,309 - root - WARNING - Trial 58 failed - ValueError: Invalid parameters sampled: {'ema_fast': 27, 'ema_slow': 27, 'atr_len': 22, 'vol_sma_len': 15, 'sl_atr_mult': 2.3371253178294364, 'trail_atr_mult': 1.4705046571574578, 'risk_multiplier': 2.515480893336058, 'min_bars_between_trades': 2, 'use_volume_filter': True, 'bb_len': 10, 'bb_std': 3.392916597865878, 'bb_oversold': 0.18515912407712096, 'bb_overbought': 0.7619635279342432, 'mfi_len': 14, 'rsi_len': 22, 'rsi_oversold': 15.118653240291064, 'rsi_overbought': 77.64691026370679, 'stoch_k': 25, 'stoch_d': 4, 'willr_len': 24, 'cci_len': 28, 'tp_atr_mult': 10.028599500779801}
2025-07-16 23:26:36,556 - root - WARNING - Trial 61 failed - ValueError: Invalid parameters sampled: {'ema_fast': 30, 'ema_slow': 22, 'atr_len': 20, 'vol_sma_len': 8, 'sl_atr_mult': 2.604354305529716, 'trail_atr_mult': 1.9959486858062352, 'risk_multiplier': 2.5012504500613355, 'min_bars_between_trades': 2, 'use_volume_filter': True, 'bb_len': 11, 'bb_std': 3.1601932262257084, 'bb_oversold': 0.19693116244623232, 'bb_overbought': 0.7581625432359048, 'mfi_len': 12, 'rsi_len': 22, 'rsi_oversold': 16.52556430264827, 'rsi_overbought': 76.16105805449178, 'stoch_k': 21, 'stoch_d': 5, 'willr_len': 19, 'cci_len': 25, 'tp_atr_mult': 7.504151221451527}
2025-07-16 23:26:36,623 - root - WARNING - Trial 62 failed - ValueError: Invalid parameters sampled: {'ema_fast': 28, 'ema_slow': 20, 'atr_len': 20, 'vol_sma_len': 10, 'sl_atr_mult': 2.452314085541029, 'trail_atr_mult': 1.988128658274577, 'risk_multiplier': 2.398591908688268, 'min_bars_between_trades': 2, 'use_volume_filter': True, 'bb_len': 11, 'bb_std': 3.259435646307269, 'bb_oversold': 0.20255620482143513, 'bb_overbought': 0.7880491769667838, 'mfi_len': 12, 'rsi_len': 22, 'rsi_oversold': 16.787344652021293, 'rsi_overbought': 76.43737260515391, 'stoch_k': 21, 'stoch_d': 5, 'willr_len': 20, 'cci_len': 26, 'tp_atr_mult': 7.649966068310504}
2025-07-16 23:26:36,770 - root - WARNING - Trial 64 failed - ValueError: Invalid parameters sampled: {'ema_fast': 26, 'ema_slow': 25, 'atr_len': 23, 'vol_sma_len': 11, 'sl_atr_mult': 3.004173384670561, 'trail_atr_mult': 1.6536171505284412, 'risk_multiplier': 2.1113486532058716, 'min_bars_between_trades': 2, 'use_volume_filter': True, 'bb_len': 11, 'bb_std': 2.7573691115127428, 'bb_oversold': 0.1682628659308191, 'bb_overbought': 0.7737283480978066, 'mfi_len': 15, 'rsi_len': 23, 'rsi_oversold': 17.51948399491222, 'rsi_overbought': 78.5812745635762, 'stoch_k': 23, 'stoch_d': 4, 'willr_len': 18, 'cci_len': 29, 'tp_atr_mult': 8.292405503495807}
2025-07-16 23:26:36,839 - root - WARNING - Trial 65 failed - ValueError: Invalid parameters sampled: {'ema_fast': 29, 'ema_slow': 28, 'atr_len': 18, 'vol_sma_len': 27, 'sl_atr_mult': 2.152340790020311, 'trail_atr_mult': 2.201524667090991, 'risk_multiplier': 2.5608964848985805, 'min_bars_between_trades': 2, 'use_volume_filter': True, 'bb_len': 14, 'bb_std': 3.0410966324611257, 'bb_oversold': 0.1838017738141655, 'bb_overbought': 0.8014094639347624, 'mfi_len': 11, 'rsi_len': 20, 'rsi_oversold': 21.23436177846322, 'rsi_overbought': 77.71341299483942, 'stoch_k': 19, 'stoch_d': 6, 'willr_len': 23, 'cci_len': 28, 'tp_atr_mult': 6.818166264169015}
2025-07-16 23:26:36,905 - root - WARNING - Trial 66 failed - ValueError: Invalid parameters sampled: {'ema_fast': 30, 'ema_slow': 22, 'atr_len': 21, 'vol_sma_len': 8, 'sl_atr_mult': 2.805908353631528, 'trail_atr_mult': 2.3084179709944914, 'risk_multiplier': 2.159294975914167, 'min_bars_between_trades': 2, 'use_volume_filter': True, 'bb_len': 24, 'bb_std': 3.314007136917876, 'bb_oversold': 0.21272413686481936, 'bb_overbought': 0.7878278939626071, 'mfi_len': 21, 'rsi_len': 17, 'rsi_oversold': 19.377373008044117, 'rsi_overbought': 73.15415640346825, 'stoch_k': 18, 'stoch_d': 5, 'willr_len': 22, 'cci_len': 27, 'tp_atr_mult': 7.7114801855294}
2025-07-16 23:26:37,058 - root - WARNING - Trial 68 failed - ValueError: Invalid parameters sampled: {'ema_fast': 27, 'ema_slow': 20, 'atr_len': 22, 'vol_sma_len': 16, 'sl_atr_mult': 2.517394131073412, 'trail_atr_mult': 1.135729037632433, 'risk_multiplier': 2.3480934522753603, 'min_bars_between_trades': 2, 'use_volume_filter': True, 'bb_len': 31, 'bb_std': 3.1927456362669875, 'bb_oversold': 0.20046707445153755, 'bb_overbought': 0.8536812803449829, 'mfi_len': 14, 'rsi_len': 22, 'rsi_oversold': 15.92303971158994, 'rsi_overbought': 70.93461983690983, 'stoch_k': 19, 'stoch_d': 4, 'willr_len': 20, 'cci_len': 30, 'tp_atr_mult': 11.040629249380133}
2025-07-16 23:26:37,339 - root - WARNING - Trial 71 failed - ValueError: Invalid parameters sampled: {'ema_fast': 28, 'ema_slow': 24, 'atr_len': 22, 'vol_sma_len': 8, 'sl_atr_mult': 2.124186286860088, 'trail_atr_mult': 1.3561024277507596, 'risk_multiplier': 2.053400883077793, 'min_bars_between_trades': 2, 'use_volume_filter': True, 'bb_len': 10, 'bb_std': 3.2211709140906395, 'bb_oversold': 0.19691236509764556, 'bb_overbought': 0.7847197050486078, 'mfi_len': 15, 'rsi_len': 23, 'rsi_oversold': 21.83520514928736, 'rsi_overbought': 84.96413133742654, 'stoch_k': 23, 'stoch_d': 3, 'willr_len': 20, 'cci_len': 29, 'tp_atr_mult': 6.811478565297005}
2025-07-16 23:26:37,446 - root - WARNING - Trial 72 failed - ValueError: Invalid parameters sampled: {'ema_fast': 27, 'ema_slow': 23, 'atr_len': 20, 'vol_sma_len': 11, 'sl_atr_mult': 2.905819509934385, 'trail_atr_mult': 1.4500651128193665, 'risk_multiplier': 2.01054890339329, 'min_bars_between_trades': 2, 'use_volume_filter': True, 'bb_len': 11, 'bb_std': 3.1333842149521183, 'bb_oversold': 0.19320041128853987, 'bb_overbought': 0.7786083429625804, 'mfi_len': 13, 'rsi_len': 21, 'rsi_oversold': 23.18482811754237, 'rsi_overbought': 79.66023485059522, 'stoch_k': 25, 'stoch_d': 4, 'willr_len': 19, 'cci_len': 28, 'tp_atr_mult': 6.361524822152274}
2025-07-16 23:26:37,663 - __main__ - INFO - BTCUSDT Reversion: Best score = -999.000
2025-07-16 23:26:37,663 - __main__ - INFO - Running walk-forward validation for BTCUSDT Reversion
2025-07-16 23:26:37,663 - __main__ - INFO - Total data available: 15.6 days (1500 bars)
2025-07-16 23:26:37,664 - __main__ - INFO - Walk-forward parameters: IS=7d, OOS=3d, step=1d
2025-07-16 23:26:40,246 - __main__ - WARNING - No valid walk-forward windows for BTCUSDT Reversion
2025-07-16 23:26:40,247 - __main__ - INFO - Attempting single-split validation as fallback...
2025-07-16 23:26:40,796 - __main__ - WARNING - Single-split validation also failed
2025-07-16 23:26:40,796 - __main__ - WARNING - All validation attempts failed for BTCUSDT Reversion, using optimization results only
2025-07-16 23:26:40,796 - __main__ - INFO - BTCUSDT Reversion: 1 valid WF windows, WF score = 3.000
2025-07-16 23:26:40,796 - __main__ - INFO - Optimizing Adaptive strategy for BTCUSDT
2025-07-16 23:26:44,357 - root - WARNING - Trial 45 failed - ValueError: Invalid parameters sampled: {'ema_fast': 23, 'ema_slow': 20, 'atr_len': 19, 'vol_sma_len': 19, 'sl_atr_mult': 3.517566919003947, 'trail_atr_mult': 2.8264846037232485, 'risk_multiplier': 2.096611900253328, 'min_bars_between_trades': 1, 'use_volume_filter': True, 'st_len': 15, 'st_mult': 3.863382446343641, 'adx_len': 25, 'adx_threshold': 16.598149517986137, 'trend_confirmations': 1, 'macd_fast': 7, 'macd_slow': 41, 'macd_signal': 14, 'rsi_len': 15, 'bb_len': 18, 'bb_std': 2.677951984496531, 'bb_oversold': 0.10148205931168032, 'bb_overbought': 0.9120116499563181, 'mfi_len': 14, 'rsi_oversold': 29.74684889673103, 'rsi_overbought': 81.18365755775564, 'stoch_k': 24, 'stoch_d': 4, 'willr_len': 18, 'cci_len': 10, 'tp_atr_mult': 11.826321125310576}
2025-07-16 23:26:44,827 - __main__ - INFO - BTCUSDT Adaptive: Best score = 1.000
2025-07-16 23:26:44,827 - __main__ - INFO - Running walk-forward validation for BTCUSDT Adaptive
2025-07-16 23:26:44,827 - __main__ - INFO - Total data available: 15.6 days (1500 bars)
2025-07-16 23:26:44,827 - __main__ - INFO - Walk-forward parameters: IS=7d, OOS=3d, step=1d
2025-07-16 23:26:48,817 - __main__ - INFO - BTCUSDT Adaptive: 6 valid WF windows, WF score = 0.600
2025-07-16 23:26:48,817 - __main__ - INFO - Validating and scoring strategies with anti-overfitting criteria
2025-07-16 23:26:48,818 - __main__ - INFO - BTCUSDT_Trend failed robustness validation. Failed checks: ['min_sortino', 'min_profit_factor', 'oos_positive_return', 'oos_min_sortino']
2025-07-16 23:26:48,818 - __main__ - INFO -   Robustness score: 0.50, Sortino stability: 0.00
2025-07-16 23:26:48,818 - __main__ - INFO - BTCUSDT_Reversion failed robustness validation. Failed checks: ['min_trades', 'min_win_rate']
2025-07-16 23:26:48,818 - __main__ - INFO -   Robustness score: 0.75, Sortino stability: 0.00
2025-07-16 23:26:48,818 - __main__ - INFO - BTCUSDT_Adaptive failed robustness validation. Failed checks: ['min_sortino', 'min_profit_factor', 'oos_positive_return', 'oos_min_sortino']
2025-07-16 23:26:48,818 - __main__ - INFO -   Robustness score: 0.50, Sortino stability: 0.00
2025-07-16 23:26:48,819 - __main__ - INFO - Saving optimization results
2025-07-16 23:26:48,819 - __main__ - WARNING - No validated strategies to save
2025-07-16 23:26:48,819 - __main__ - WARNING - Optimization completed but no strategies passed validation
2025-07-16 23:26:48,819 - __main__ - INFO - Binance client connection closed
2025-07-16 23:31:05,559 - __main__ - INFO - Ultratrader Orchestrator initialized
2025-07-16 23:31:05,560 - __main__ - INFO - Ultratrader Orchestrator initialized
2025-07-16 23:31:05,560 - __main__ - INFO - Starting Ultratrader optimization pipeline
2025-07-16 23:31:06,246 - __main__ - INFO - Binance client initialized successfully
2025-07-16 23:31:06,246 - __main__ - INFO - Optimizing strategies for symbols: ['BTCUSDT']
2025-07-16 23:31:06,246 - __main__ - INFO - Fetching market data for BTCUSDT
2025-07-16 23:31:06,275 - __main__ - INFO - Retrieved 1500 bars for BTCUSDT (15m)
2025-07-16 23:31:06,275 - __main__ - INFO - Retrieved 1500 bars for BTCUSDT (1h)
2025-07-16 23:31:06,276 - __main__ - INFO - Optimizing Trend strategy for BTCUSDT
2025-07-16 23:31:13,402 - __main__ - INFO - BTCUSDT Trend: Best score = 1.000
2025-07-16 23:31:13,403 - __main__ - INFO - Running walk-forward validation for BTCUSDT Trend
2025-07-16 23:31:13,403 - __main__ - INFO - Total data available: 15.6 days (1500 bars)
2025-07-16 23:31:13,403 - __main__ - INFO - Walk-forward parameters: IS=7d, OOS=3d, step=1d
2025-07-16 23:31:16,694 - __main__ - INFO - BTCUSDT Trend: 6 valid WF windows, WF score = 0.600
2025-07-16 23:31:16,694 - __main__ - INFO - Optimizing Reversion strategy for BTCUSDT
2025-07-16 23:31:21,772 - __main__ - ERROR - Error optimizing BTCUSDT Reversion: No trials are completed yet.
2025-07-16 23:31:21,772 - __main__ - INFO - Running walk-forward validation for BTCUSDT Reversion
2025-07-16 23:31:21,773 - __main__ - INFO - Total data available: 15.6 days (1500 bars)
2025-07-16 23:31:21,773 - __main__ - INFO - Walk-forward parameters: IS=7d, OOS=3d, step=1d
2025-07-16 23:31:24,610 - __main__ - WARNING - No valid walk-forward windows for BTCUSDT Reversion
2025-07-16 23:31:24,610 - __main__ - INFO - Attempting single-split validation as fallback...
2025-07-16 23:31:25,222 - __main__ - WARNING - Single-split validation also failed
2025-07-16 23:31:25,222 - __main__ - WARNING - All validation attempts failed for BTCUSDT Reversion, using optimization results only
2025-07-16 23:31:25,222 - __main__ - INFO - BTCUSDT Reversion: 1 valid WF windows, WF score = 3.000
2025-07-16 23:31:25,222 - __main__ - INFO - Optimizing Adaptive strategy for BTCUSDT
2025-07-16 23:31:30,006 - __main__ - INFO - BTCUSDT Adaptive: Best score = 1.000
2025-07-16 23:31:30,006 - __main__ - INFO - Running walk-forward validation for BTCUSDT Adaptive
2025-07-16 23:31:30,006 - __main__ - INFO - Total data available: 15.6 days (1500 bars)
2025-07-16 23:31:30,006 - __main__ - INFO - Walk-forward parameters: IS=7d, OOS=3d, step=1d
2025-07-16 23:31:34,539 - __main__ - INFO - BTCUSDT Adaptive: 6 valid WF windows, WF score = 0.600
2025-07-16 23:31:34,539 - __main__ - INFO - Validating and scoring strategies with anti-overfitting criteria
2025-07-16 23:31:34,540 - __main__ - INFO - BTCUSDT_Trend failed robustness validation. Failed checks: ['min_sortino', 'min_profit_factor', 'oos_positive_return', 'oos_min_sortino']
2025-07-16 23:31:34,540 - __main__ - INFO -   Robustness score: 0.50, Sortino stability: 0.00
2025-07-16 23:31:34,540 - __main__ - INFO - BTCUSDT_Adaptive failed robustness validation. Failed checks: ['min_sortino', 'min_profit_factor', 'oos_positive_return', 'oos_min_sortino']
2025-07-16 23:31:34,541 - __main__ - INFO -   Robustness score: 0.50, Sortino stability: 0.00
2025-07-16 23:31:34,541 - __main__ - INFO - Saving optimization results
2025-07-16 23:31:34,541 - __main__ - WARNING - No validated strategies to save
2025-07-16 23:31:34,541 - __main__ - WARNING - Optimization completed but no strategies passed validation
2025-07-16 23:31:34,542 - __main__ - INFO - Binance client connection closed
2025-07-16 23:36:07,846 - __main__ - INFO - Ultratrader Orchestrator initialized
2025-07-16 23:36:07,846 - __main__ - INFO - Ultratrader Orchestrator initialized
2025-07-16 23:36:07,846 - __main__ - INFO - Starting Ultratrader optimization pipeline
2025-07-16 23:36:08,531 - __main__ - INFO - Binance client initialized successfully
2025-07-16 23:36:08,531 - __main__ - INFO - Optimizing strategies for symbols: ['BTCUSDT']
2025-07-16 23:36:08,531 - __main__ - INFO - Fetching market data for BTCUSDT
2025-07-16 23:36:08,545 - __main__ - INFO - Retrieved 1500 bars for BTCUSDT (15m)
2025-07-16 23:36:08,545 - __main__ - INFO - Retrieved 1500 bars for BTCUSDT (1h)
2025-07-16 23:36:08,546 - __main__ - INFO - Optimizing Trend strategy for BTCUSDT
2025-07-16 23:36:15,566 - __main__ - INFO - BTCUSDT Trend: Best score = 1.000
2025-07-16 23:36:15,566 - __main__ - INFO - Running walk-forward validation for BTCUSDT Trend
2025-07-16 23:36:15,566 - __main__ - INFO - Total data available: 15.6 days (1500 bars)
2025-07-16 23:36:15,566 - __main__ - INFO - Walk-forward parameters: IS=7d, OOS=3d, step=1d
2025-07-16 23:36:18,942 - __main__ - INFO - BTCUSDT Trend: 6 valid WF windows, WF score = 0.600
2025-07-16 23:36:18,942 - __main__ - INFO - Optimizing Reversion strategy for BTCUSDT
2025-07-16 23:36:24,264 - __main__ - INFO - BTCUSDT Reversion: Best score = 1.000
2025-07-16 23:36:24,264 - __main__ - INFO - Running walk-forward validation for BTCUSDT Reversion
2025-07-16 23:36:24,264 - __main__ - INFO - Total data available: 15.6 days (1500 bars)
2025-07-16 23:36:24,264 - __main__ - INFO - Walk-forward parameters: IS=7d, OOS=3d, step=1d
2025-07-16 23:36:27,413 - __main__ - INFO - BTCUSDT Reversion: 6 valid WF windows, WF score = 0.660
2025-07-16 23:36:27,414 - __main__ - INFO - Optimizing Adaptive strategy for BTCUSDT
2025-07-16 23:36:32,073 - __main__ - INFO - BTCUSDT Adaptive: Best score = 1.000
2025-07-16 23:36:32,073 - __main__ - INFO - Running walk-forward validation for BTCUSDT Adaptive
2025-07-16 23:36:32,074 - __main__ - INFO - Total data available: 15.6 days (1500 bars)
2025-07-16 23:36:32,074 - __main__ - INFO - Walk-forward parameters: IS=7d, OOS=3d, step=1d
2025-07-16 23:36:36,848 - __main__ - INFO - BTCUSDT Adaptive: 6 valid WF windows, WF score = 0.600
2025-07-16 23:36:36,848 - __main__ - INFO - Validating and scoring strategies with anti-overfitting criteria
2025-07-16 23:36:36,849 - __main__ - INFO - BTCUSDT_Trend failed robustness validation. Failed checks: ['min_sortino', 'min_profit_factor', 'oos_positive_return', 'oos_min_sortino']
2025-07-16 23:36:36,849 - __main__ - INFO -   Robustness score: 0.50, Sortino stability: 0.00
2025-07-16 23:36:36,849 - __main__ - INFO - BTCUSDT_Reversion failed robustness validation. Failed checks: ['min_sortino', 'min_profit_factor', 'min_win_rate', 'oos_positive_return', 'oos_min_sortino']
2025-07-16 23:36:36,850 - __main__ - INFO -   Robustness score: 0.38, Sortino stability: 0.00
2025-07-16 23:36:36,850 - __main__ - INFO - BTCUSDT_Adaptive failed robustness validation. Failed checks: ['min_sortino', 'min_profit_factor', 'oos_positive_return', 'oos_min_sortino']
2025-07-16 23:36:36,850 - __main__ - INFO -   Robustness score: 0.50, Sortino stability: 0.00
2025-07-16 23:36:36,850 - __main__ - INFO - Saving optimization results
2025-07-16 23:36:36,850 - __main__ - WARNING - No validated strategies to save
2025-07-16 23:36:36,854 - __main__ - WARNING - Optimization completed but no strategies passed validation
2025-07-16 23:36:36,855 - __main__ - INFO - Binance client connection closed
2025-07-16 23:41:31,808 - __main__ - INFO - Ultratrader Orchestrator initialized
2025-07-16 23:41:31,808 - __main__ - INFO - Ultratrader Orchestrator initialized
2025-07-16 23:41:31,808 - __main__ - INFO - Starting Ultratrader optimization pipeline
2025-07-16 23:41:32,498 - __main__ - INFO - Binance client initialized successfully
2025-07-16 23:41:32,498 - __main__ - INFO - Optimizing strategies for symbols: ['BTCUSDT']
2025-07-16 23:41:32,498 - __main__ - INFO - Fetching market data for BTCUSDT
2025-07-16 23:41:32,510 - __main__ - INFO - Retrieved 1500 bars for BTCUSDT (15m)
2025-07-16 23:41:32,510 - __main__ - INFO - Retrieved 1500 bars for BTCUSDT (1h)
2025-07-16 23:41:32,510 - __main__ - INFO - Optimizing Trend strategy for BTCUSDT
2025-07-16 23:41:40,020 - __main__ - INFO - BTCUSDT Trend: Best score = 1.000
2025-07-16 23:41:40,020 - __main__ - INFO - Running walk-forward validation for BTCUSDT Trend
2025-07-16 23:41:40,020 - __main__ - INFO - Total data available: 15.6 days (1500 bars)
2025-07-16 23:41:40,020 - __main__ - INFO - Walk-forward parameters: IS=7d, OOS=3d, step=1d
2025-07-16 23:41:43,559 - __main__ - INFO - BTCUSDT Trend: 6 valid WF windows, WF score = 0.600
2025-07-16 23:41:43,560 - __main__ - INFO - Optimizing Reversion strategy for BTCUSDT
2025-07-16 23:41:49,097 - __main__ - INFO - BTCUSDT Reversion: Best score = 1.000
2025-07-16 23:41:49,097 - __main__ - INFO - Running walk-forward validation for BTCUSDT Reversion
2025-07-16 23:41:49,097 - __main__ - INFO - Total data available: 15.6 days (1500 bars)
2025-07-16 23:41:49,097 - __main__ - INFO - Walk-forward parameters: IS=7d, OOS=3d, step=1d
2025-07-16 23:41:52,290 - __main__ - INFO - BTCUSDT Reversion: 6 valid WF windows, WF score = 0.660
2025-07-16 23:41:52,290 - __main__ - INFO - Optimizing Adaptive strategy for BTCUSDT
2025-07-16 23:41:56,845 - __main__ - INFO - BTCUSDT Adaptive: Best score = 1.000
2025-07-16 23:41:56,846 - __main__ - INFO - Running walk-forward validation for BTCUSDT Adaptive
2025-07-16 23:41:56,846 - __main__ - INFO - Total data available: 15.6 days (1500 bars)
2025-07-16 23:41:56,846 - __main__ - INFO - Walk-forward parameters: IS=7d, OOS=3d, step=1d
2025-07-16 23:42:01,294 - __main__ - INFO - BTCUSDT Adaptive: 6 valid WF windows, WF score = 0.600
2025-07-16 23:42:01,294 - __main__ - INFO - Validating and scoring strategies with anti-overfitting criteria
2025-07-16 23:42:01,294 - __main__ - INFO - BTCUSDT_Trend failed robustness validation. Failed checks: ['min_sortino', 'min_profit_factor', 'min_win_rate', 'sortino_stability', 'oos_positive_return', 'oos_min_sortino']
2025-07-16 23:42:01,294 - __main__ - INFO -   Robustness score: 0.25, Sortino stability: 0.00
2025-07-16 23:42:01,294 - __main__ - INFO - BTCUSDT_Reversion failed robustness validation. Failed checks: ['min_sortino', 'min_profit_factor', 'min_win_rate', 'sortino_stability', 'oos_positive_return', 'oos_min_sortino']
2025-07-16 23:42:01,295 - __main__ - INFO -   Robustness score: 0.25, Sortino stability: 0.00
2025-07-16 23:42:01,295 - __main__ - INFO - BTCUSDT_Adaptive failed robustness validation. Failed checks: ['min_sortino', 'min_profit_factor', 'min_win_rate', 'sortino_stability', 'oos_positive_return', 'oos_min_sortino']
2025-07-16 23:42:01,295 - __main__ - INFO -   Robustness score: 0.25, Sortino stability: 0.00
2025-07-16 23:42:01,295 - __main__ - INFO - Saving optimization results
2025-07-16 23:42:01,295 - __main__ - WARNING - No validated strategies to save
2025-07-16 23:42:01,295 - __main__ - WARNING - Optimization completed but no strategies passed validation
2025-07-16 23:42:01,296 - __main__ - INFO - Binance client connection closed
2025-07-16 23:44:44,369 - __main__ - INFO - Ultratrader Orchestrator initialized
2025-07-16 23:44:44,369 - __main__ - INFO - Ultratrader Orchestrator initialized
2025-07-16 23:44:44,369 - __main__ - INFO - Starting Ultratrader optimization pipeline
2025-07-16 23:44:45,051 - __main__ - INFO - Binance client initialized successfully
2025-07-16 23:44:45,052 - __main__ - INFO - Optimizing strategies for symbols: ['BTCUSDT']
2025-07-16 23:44:45,052 - __main__ - INFO - Fetching market data for BTCUSDT
2025-07-16 23:44:45,070 - __main__ - INFO - Retrieved 1500 bars for BTCUSDT (15m)
2025-07-16 23:44:45,070 - __main__ - INFO - Retrieved 1500 bars for BTCUSDT (1h)
2025-07-16 23:44:45,070 - __main__ - INFO - Optimizing Trend strategy for BTCUSDT
2025-07-16 23:44:52,291 - __main__ - INFO - BTCUSDT Trend: Best score = 1.000
2025-07-16 23:44:52,291 - __main__ - INFO - Running walk-forward validation for BTCUSDT Trend
2025-07-16 23:44:52,291 - __main__ - INFO - Total data available: 15.6 days (1500 bars)
2025-07-16 23:44:52,291 - __main__ - INFO - Walk-forward parameters: IS=7d, OOS=3d, step=1d
2025-07-16 23:44:55,694 - __main__ - INFO - BTCUSDT Trend: 6 valid WF windows, WF score = 0.600
2025-07-16 23:44:55,694 - __main__ - INFO - Optimizing Reversion strategy for BTCUSDT
2025-07-16 23:45:01,408 - __main__ - INFO - BTCUSDT Reversion: Best score = 1.000
2025-07-16 23:45:01,409 - __main__ - INFO - Running walk-forward validation for BTCUSDT Reversion
2025-07-16 23:45:01,409 - __main__ - INFO - Total data available: 15.6 days (1500 bars)
2025-07-16 23:45:01,409 - __main__ - INFO - Walk-forward parameters: IS=7d, OOS=3d, step=1d
2025-07-16 23:45:04,743 - __main__ - INFO - BTCUSDT Reversion: 6 valid WF windows, WF score = 0.600
2025-07-16 23:45:04,744 - __main__ - INFO - Optimizing Adaptive strategy for BTCUSDT
2025-07-16 23:45:09,429 - __main__ - INFO - BTCUSDT Adaptive: Best score = 1.000
2025-07-16 23:45:09,429 - __main__ - INFO - Running walk-forward validation for BTCUSDT Adaptive
2025-07-16 23:45:09,429 - __main__ - INFO - Total data available: 15.6 days (1500 bars)
2025-07-16 23:45:09,430 - __main__ - INFO - Walk-forward parameters: IS=7d, OOS=3d, step=1d
2025-07-16 23:45:14,105 - __main__ - INFO - BTCUSDT Adaptive: 6 valid WF windows, WF score = 0.600
2025-07-16 23:45:14,105 - __main__ - INFO - Validating and scoring strategies with anti-overfitting criteria
2025-07-16 23:45:14,107 - __main__ - INFO - BTCUSDT_Trend failed robustness validation. Failed checks: ['min_sortino', 'min_profit_factor', 'min_win_rate', 'sortino_stability', 'oos_positive_return', 'oos_min_sortino']
2025-07-16 23:45:14,107 - __main__ - INFO -   Robustness score: 0.25, Sortino stability: 0.00
2025-07-16 23:45:14,107 - __main__ - INFO - BTCUSDT_Reversion failed robustness validation. Failed checks: ['min_sortino', 'min_profit_factor', 'min_win_rate', 'sortino_stability', 'oos_positive_return', 'oos_min_sortino']
2025-07-16 23:45:14,107 - __main__ - INFO -   Robustness score: 0.25, Sortino stability: 0.00
2025-07-16 23:45:14,108 - __main__ - INFO - BTCUSDT_Adaptive failed robustness validation. Failed checks: ['min_sortino', 'min_profit_factor', 'min_win_rate', 'sortino_stability', 'oos_positive_return', 'oos_min_sortino']
2025-07-16 23:45:14,108 - __main__ - INFO -   Robustness score: 0.25, Sortino stability: 0.00
2025-07-16 23:45:14,108 - __main__ - INFO - Saving optimization results
2025-07-16 23:45:14,108 - __main__ - WARNING - No validated strategies to save
2025-07-16 23:45:14,108 - __main__ - WARNING - Optimization completed but no strategies passed validation
2025-07-16 23:45:14,110 - __main__ - INFO - Binance client connection closed
2025-07-16 23:48:41,319 - __main__ - INFO - Ultratrader Orchestrator initialized
2025-07-16 23:48:41,319 - __main__ - INFO - Ultratrader Orchestrator initialized
2025-07-16 23:48:41,319 - __main__ - INFO - Starting Ultratrader optimization pipeline
2025-07-16 23:48:42,006 - __main__ - INFO - Binance client initialized successfully
2025-07-16 23:48:42,006 - __main__ - INFO - Optimizing strategies for symbols: ['BTCUSDT']
2025-07-16 23:48:42,006 - __main__ - INFO - Fetching market data for BTCUSDT
2025-07-16 23:48:42,021 - __main__ - INFO - Retrieved 1500 bars for BTCUSDT (15m)
2025-07-16 23:48:42,021 - __main__ - INFO - Retrieved 1500 bars for BTCUSDT (1h)
2025-07-16 23:48:42,021 - __main__ - INFO - Optimizing Trend strategy for BTCUSDT
2025-07-16 23:48:49,876 - __main__ - INFO - BTCUSDT Trend: Best score = 1.000
2025-07-16 23:48:49,876 - __main__ - INFO - Running walk-forward validation for BTCUSDT Trend
2025-07-16 23:48:49,876 - __main__ - INFO - Total data available: 15.6 days (1500 bars)
2025-07-16 23:48:49,877 - __main__ - INFO - Walk-forward parameters: IS=7d, OOS=3d, step=1d
2025-07-16 23:48:53,322 - __main__ - INFO - BTCUSDT Trend: 6 valid WF windows, WF score = 0.600
2025-07-16 23:48:53,323 - __main__ - INFO - Optimizing Reversion strategy for BTCUSDT
2025-07-16 23:48:58,691 - __main__ - ERROR - Error optimizing BTCUSDT Reversion: No trials are completed yet.
2025-07-16 23:48:58,692 - __main__ - INFO - Running walk-forward validation for BTCUSDT Reversion
2025-07-16 23:48:58,692 - __main__ - INFO - Total data available: 15.6 days (1500 bars)
2025-07-16 23:48:58,692 - __main__ - INFO - Walk-forward parameters: IS=7d, OOS=3d, step=1d
2025-07-16 23:49:01,770 - __main__ - WARNING - No valid walk-forward windows for BTCUSDT Reversion
2025-07-16 23:49:01,770 - __main__ - INFO - Attempting single-split validation as fallback...
2025-07-16 23:49:02,441 - __main__ - WARNING - Single-split validation also failed
2025-07-16 23:49:02,442 - __main__ - WARNING - All validation attempts failed for BTCUSDT Reversion, using optimization results only
2025-07-16 23:49:02,442 - __main__ - INFO - BTCUSDT Reversion: 1 valid WF windows, WF score = 3.000
2025-07-16 23:49:02,442 - __main__ - INFO - Optimizing Adaptive strategy for BTCUSDT
2025-07-16 23:49:07,124 - __main__ - INFO - BTCUSDT Adaptive: Best score = 1.000
2025-07-16 23:49:07,124 - __main__ - INFO - Running walk-forward validation for BTCUSDT Adaptive
2025-07-16 23:49:07,124 - __main__ - INFO - Total data available: 15.6 days (1500 bars)
2025-07-16 23:49:07,124 - __main__ - INFO - Walk-forward parameters: IS=7d, OOS=3d, step=1d
2025-07-16 23:49:11,748 - __main__ - INFO - BTCUSDT Adaptive: 6 valid WF windows, WF score = 0.600
2025-07-16 23:49:11,748 - __main__ - INFO - Validating and scoring strategies with anti-overfitting criteria
2025-07-16 23:49:11,748 - __main__ - INFO - BTCUSDT_Trend failed robustness validation. Failed checks: ['min_sortino', 'min_profit_factor', 'min_win_rate', 'sortino_stability', 'oos_positive_return', 'oos_min_sortino']
2025-07-16 23:49:11,749 - __main__ - INFO -   Robustness score: 0.25, Sortino stability: 0.00
2025-07-16 23:49:11,749 - __main__ - INFO - BTCUSDT_Adaptive failed robustness validation. Failed checks: ['min_sortino', 'min_profit_factor', 'min_win_rate', 'sortino_stability', 'oos_positive_return', 'oos_min_sortino']
2025-07-16 23:49:11,749 - __main__ - INFO -   Robustness score: 0.25, Sortino stability: 0.00
2025-07-16 23:49:11,749 - __main__ - INFO - Saving optimization results
2025-07-16 23:49:11,749 - __main__ - WARNING - No validated strategies to save
2025-07-16 23:49:11,749 - __main__ - WARNING - Optimization completed but no strategies passed validation
2025-07-16 23:49:11,750 - __main__ - INFO - Binance client connection closed
2025-07-16 23:50:02,356 - __main__ - INFO - Ultratrader Orchestrator initialized
2025-07-16 23:50:02,356 - __main__ - INFO - Ultratrader Orchestrator initialized
2025-07-16 23:50:02,356 - __main__ - INFO - Starting Ultratrader optimization pipeline
2025-07-16 23:50:03,041 - __main__ - INFO - Binance client initialized successfully
2025-07-16 23:50:03,041 - __main__ - INFO - Optimizing strategies for symbols: ['BTCUSDT']
2025-07-16 23:50:03,041 - __main__ - INFO - Fetching market data for BTCUSDT
2025-07-16 23:50:03,054 - __main__ - INFO - Retrieved 1500 bars for BTCUSDT (15m)
2025-07-16 23:50:03,054 - __main__ - INFO - Retrieved 1500 bars for BTCUSDT (1h)
2025-07-16 23:50:03,054 - __main__ - INFO - Optimizing Trend strategy for BTCUSDT
2025-07-16 23:50:10,316 - __main__ - INFO - BTCUSDT Trend: Best score = 1.000
2025-07-16 23:50:10,316 - __main__ - INFO - Running walk-forward validation for BTCUSDT Trend
2025-07-16 23:50:10,316 - __main__ - INFO - Total data available: 15.6 days (1500 bars)
2025-07-16 23:50:10,316 - __main__ - INFO - Walk-forward parameters: IS=7d, OOS=3d, step=1d
2025-07-16 23:50:13,528 - __main__ - INFO - BTCUSDT Trend: 6 valid WF windows, WF score = 0.600
2025-07-16 23:50:13,528 - __main__ - INFO - Optimizing Reversion strategy for BTCUSDT
2025-07-16 23:50:19,033 - __main__ - ERROR - Error optimizing BTCUSDT Reversion: No trials are completed yet.
2025-07-16 23:50:19,033 - __main__ - INFO - Running walk-forward validation for BTCUSDT Reversion
2025-07-16 23:50:19,034 - __main__ - INFO - Total data available: 15.6 days (1500 bars)
2025-07-16 23:50:19,034 - __main__ - INFO - Walk-forward parameters: IS=7d, OOS=3d, step=1d
2025-07-16 23:50:22,225 - __main__ - WARNING - No valid walk-forward windows for BTCUSDT Reversion
2025-07-16 23:50:22,225 - __main__ - INFO - Attempting single-split validation as fallback...
2025-07-16 23:50:22,891 - __main__ - WARNING - Single-split validation also failed
2025-07-16 23:50:22,892 - __main__ - WARNING - All validation attempts failed for BTCUSDT Reversion, using optimization results only
2025-07-16 23:50:22,892 - __main__ - INFO - BTCUSDT Reversion: 1 valid WF windows, WF score = 3.000
2025-07-16 23:50:22,892 - __main__ - INFO - Optimizing Adaptive strategy for BTCUSDT
2025-07-16 23:50:27,559 - __main__ - INFO - BTCUSDT Adaptive: Best score = 1.000
2025-07-16 23:50:27,559 - __main__ - INFO - Running walk-forward validation for BTCUSDT Adaptive
2025-07-16 23:50:27,559 - __main__ - INFO - Total data available: 15.6 days (1500 bars)
2025-07-16 23:50:27,559 - __main__ - INFO - Walk-forward parameters: IS=7d, OOS=3d, step=1d
2025-07-16 23:50:32,035 - __main__ - INFO - BTCUSDT Adaptive: 6 valid WF windows, WF score = 0.600
2025-07-16 23:50:32,035 - __main__ - INFO - Validating and scoring strategies with anti-overfitting criteria
2025-07-16 23:50:32,035 - __main__ - INFO - BTCUSDT_Trend failed robustness validation. Failed checks: ['min_sortino', 'min_profit_factor', 'min_win_rate', 'sortino_stability', 'oos_positive_return', 'oos_min_sortino']
2025-07-16 23:50:32,035 - __main__ - INFO -   Robustness score: 0.25, Sortino stability: 0.00
2025-07-16 23:50:32,036 - __main__ - INFO - BTCUSDT_Adaptive failed robustness validation. Failed checks: ['min_sortino', 'min_profit_factor', 'min_win_rate', 'sortino_stability', 'oos_positive_return', 'oos_min_sortino']
2025-07-16 23:50:32,036 - __main__ - INFO -   Robustness score: 0.25, Sortino stability: 0.00
2025-07-16 23:50:32,036 - __main__ - INFO - Saving optimization results
2025-07-16 23:50:32,036 - __main__ - WARNING - No validated strategies to save
2025-07-16 23:50:32,036 - __main__ - WARNING - Optimization completed but no strategies passed validation
2025-07-16 23:50:32,036 - __main__ - INFO - Binance client connection closed
2025-07-16 23:51:13,378 - __main__ - INFO - Ultratrader Orchestrator initialized
2025-07-16 23:51:13,379 - __main__ - INFO - Ultratrader Orchestrator initialized
2025-07-16 23:51:13,379 - __main__ - INFO - Starting Ultratrader optimization pipeline
2025-07-16 23:51:14,063 - __main__ - INFO - Binance client initialized successfully
2025-07-16 23:51:14,063 - __main__ - INFO - Optimizing strategies for symbols: ['BTCUSDT']
2025-07-16 23:51:14,063 - __main__ - INFO - Fetching market data for BTCUSDT
2025-07-16 23:51:14,075 - __main__ - INFO - Retrieved 1500 bars for BTCUSDT (15m)
2025-07-16 23:51:14,076 - __main__ - INFO - Retrieved 1500 bars for BTCUSDT (1h)
2025-07-16 23:51:14,076 - __main__ - INFO - Optimizing Trend strategy for BTCUSDT
2025-07-16 23:51:21,267 - __main__ - INFO - BTCUSDT Trend: Best score = 1.000
2025-07-16 23:51:21,267 - __main__ - INFO - Running walk-forward validation for BTCUSDT Trend
2025-07-16 23:51:21,267 - __main__ - INFO - Total data available: 15.6 days (1500 bars)
2025-07-16 23:51:21,267 - __main__ - INFO - Walk-forward parameters: IS=7d, OOS=3d, step=1d
2025-07-16 23:51:24,483 - __main__ - INFO - BTCUSDT Trend: 6 valid WF windows, WF score = 0.600
2025-07-16 23:51:24,483 - __main__ - INFO - Optimizing Reversion strategy for BTCUSDT
2025-07-16 23:51:30,153 - __main__ - INFO - BTCUSDT Reversion: Best score = 1.000
2025-07-16 23:51:30,153 - __main__ - INFO - Running walk-forward validation for BTCUSDT Reversion
2025-07-16 23:51:30,154 - __main__ - INFO - Total data available: 15.6 days (1500 bars)
2025-07-16 23:51:30,154 - __main__ - INFO - Walk-forward parameters: IS=7d, OOS=3d, step=1d
2025-07-16 23:51:33,404 - __main__ - INFO - BTCUSDT Reversion: 6 valid WF windows, WF score = 0.600
2025-07-16 23:51:33,404 - __main__ - INFO - Optimizing Adaptive strategy for BTCUSDT
2025-07-16 23:51:38,122 - __main__ - INFO - BTCUSDT Adaptive: Best score = 1.000
2025-07-16 23:51:38,122 - __main__ - INFO - Running walk-forward validation for BTCUSDT Adaptive
2025-07-16 23:51:38,123 - __main__ - INFO - Total data available: 15.6 days (1500 bars)
2025-07-16 23:51:38,123 - __main__ - INFO - Walk-forward parameters: IS=7d, OOS=3d, step=1d
2025-07-16 23:51:42,436 - __main__ - INFO - BTCUSDT Adaptive: 6 valid WF windows, WF score = 0.600
2025-07-16 23:51:42,436 - __main__ - INFO - Validating and scoring strategies with anti-overfitting criteria
2025-07-16 23:51:42,436 - __main__ - INFO - BTCUSDT_Trend failed robustness validation. Failed checks: ['min_sortino', 'min_profit_factor', 'min_win_rate', 'sortino_stability', 'oos_positive_return', 'oos_min_sortino']
2025-07-16 23:51:42,436 - __main__ - INFO -   Robustness score: 0.25, Sortino stability: 0.00
2025-07-16 23:51:42,436 - __main__ - INFO - BTCUSDT_Reversion failed robustness validation. Failed checks: ['min_sortino', 'min_profit_factor', 'min_win_rate', 'sortino_stability', 'oos_positive_return', 'oos_min_sortino']
2025-07-16 23:51:42,436 - __main__ - INFO -   Robustness score: 0.25, Sortino stability: 0.00
2025-07-16 23:51:42,436 - __main__ - INFO - BTCUSDT_Adaptive failed robustness validation. Failed checks: ['min_sortino', 'min_profit_factor', 'min_win_rate', 'sortino_stability', 'oos_positive_return', 'oos_min_sortino']
2025-07-16 23:51:42,437 - __main__ - INFO -   Robustness score: 0.25, Sortino stability: 0.00
2025-07-16 23:51:42,437 - __main__ - INFO - Saving optimization results
2025-07-16 23:51:42,437 - __main__ - WARNING - No validated strategies to save
2025-07-16 23:51:42,437 - __main__ - WARNING - Optimization completed but no strategies passed validation
2025-07-16 23:51:42,438 - __main__ - INFO - Binance client connection closed
2025-07-16 23:53:30,716 - __main__ - INFO - Ultratrader Orchestrator initialized
2025-07-16 23:53:30,716 - __main__ - INFO - Ultratrader Orchestrator initialized
2025-07-16 23:53:30,716 - __main__ - INFO - Starting Ultratrader optimization pipeline
2025-07-16 23:53:31,501 - __main__ - INFO - Binance client initialized successfully
2025-07-16 23:53:31,501 - __main__ - INFO - Optimizing strategies for symbols: ['BTCUSDT']
2025-07-16 23:53:31,501 - __main__ - INFO - Fetching market data for BTCUSDT
2025-07-16 23:53:31,513 - __main__ - INFO - Retrieved 1500 bars for BTCUSDT (15m)
2025-07-16 23:53:31,513 - __main__ - INFO - Retrieved 1500 bars for BTCUSDT (1h)
2025-07-16 23:53:31,513 - __main__ - INFO - Optimizing Trend strategy for BTCUSDT
2025-07-16 23:53:38,940 - __main__ - INFO - BTCUSDT Trend: Best score = 1.000
2025-07-16 23:53:38,940 - __main__ - INFO - Running walk-forward validation for BTCUSDT Trend
2025-07-16 23:53:38,940 - __main__ - INFO - Total data available: 15.6 days (1500 bars)
2025-07-16 23:53:38,940 - __main__ - INFO - Walk-forward parameters: IS=7d, OOS=3d, step=1d
2025-07-16 23:53:42,502 - __main__ - INFO - BTCUSDT Trend: 6 valid WF windows, WF score = 0.600
2025-07-16 23:53:42,502 - __main__ - INFO - Optimizing Reversion strategy for BTCUSDT
2025-07-16 23:53:48,324 - __main__ - INFO - BTCUSDT Reversion: Best score = 1.000
2025-07-16 23:53:48,324 - __main__ - INFO - Running walk-forward validation for BTCUSDT Reversion
2025-07-16 23:53:48,324 - __main__ - INFO - Total data available: 15.6 days (1500 bars)
2025-07-16 23:53:48,324 - __main__ - INFO - Walk-forward parameters: IS=7d, OOS=3d, step=1d
2025-07-16 23:53:51,559 - __main__ - INFO - BTCUSDT Reversion: 6 valid WF windows, WF score = 0.600
2025-07-16 23:53:51,559 - __main__ - INFO - Optimizing Adaptive strategy for BTCUSDT
2025-07-16 23:53:56,124 - __main__ - INFO - BTCUSDT Adaptive: Best score = 1.000
2025-07-16 23:53:56,124 - __main__ - INFO - Running walk-forward validation for BTCUSDT Adaptive
2025-07-16 23:53:56,124 - __main__ - INFO - Total data available: 15.6 days (1500 bars)
2025-07-16 23:53:56,124 - __main__ - INFO - Walk-forward parameters: IS=7d, OOS=3d, step=1d
2025-07-16 23:54:00,222 - __main__ - INFO - BTCUSDT Adaptive: 6 valid WF windows, WF score = 0.600
2025-07-16 23:54:00,222 - __main__ - INFO - Validating and scoring strategies with anti-overfitting criteria
2025-07-16 23:54:00,222 - __main__ - INFO - BTCUSDT_Trend failed robustness validation. Failed checks: ['min_sortino', 'min_profit_factor', 'min_win_rate', 'sortino_stability', 'oos_positive_return', 'oos_min_sortino']
2025-07-16 23:54:00,222 - __main__ - INFO -   Robustness score: 0.25, Sortino stability: 0.00
2025-07-16 23:54:00,222 - __main__ - INFO - BTCUSDT_Reversion failed robustness validation. Failed checks: ['min_sortino', 'min_profit_factor', 'min_win_rate', 'sortino_stability', 'oos_positive_return', 'oos_min_sortino']
2025-07-16 23:54:00,222 - __main__ - INFO -   Robustness score: 0.25, Sortino stability: 0.00
2025-07-16 23:54:00,222 - __main__ - INFO - BTCUSDT_Adaptive failed robustness validation. Failed checks: ['min_sortino', 'min_profit_factor', 'min_win_rate', 'sortino_stability', 'oos_positive_return', 'oos_min_sortino']
2025-07-16 23:54:00,222 - __main__ - INFO -   Robustness score: 0.25, Sortino stability: 0.00
2025-07-16 23:54:00,222 - __main__ - INFO - Saving optimization results
2025-07-16 23:54:00,223 - __main__ - WARNING - No validated strategies to save
2025-07-16 23:54:00,223 - __main__ - WARNING - Optimization completed but no strategies passed validation
2025-07-16 23:54:00,223 - __main__ - INFO - Binance client connection closed
2025-07-16 23:58:15,261 - __main__ - INFO - Ultratrader Orchestrator initialized
2025-07-16 23:58:15,261 - __main__ - INFO - Ultratrader Orchestrator initialized
2025-07-16 23:58:15,261 - __main__ - INFO - Starting Ultratrader optimization pipeline
2025-07-16 23:58:15,946 - __main__ - INFO - Binance client initialized successfully
2025-07-16 23:58:15,946 - __main__ - INFO - Optimizing strategies for symbols: ['BTCUSDT']
2025-07-16 23:58:15,946 - __main__ - INFO - Fetching market data for BTCUSDT
2025-07-16 23:58:15,979 - __main__ - INFO - Retrieved 1500 bars for BTCUSDT (15m)
2025-07-16 23:58:15,980 - __main__ - INFO - Retrieved 1500 bars for BTCUSDT (1h)
2025-07-16 23:58:15,980 - __main__ - INFO - Optimizing Trend strategy for BTCUSDT
2025-07-16 23:58:24,146 - __main__ - INFO - BTCUSDT Trend: Best score = 1.000
2025-07-16 23:58:24,147 - __main__ - INFO - Running walk-forward validation for BTCUSDT Trend
2025-07-16 23:58:24,147 - __main__ - INFO - Total data available: 15.6 days (1500 bars)
2025-07-16 23:58:24,147 - __main__ - INFO - Walk-forward parameters: IS=7d, OOS=3d, step=1d
2025-07-16 23:58:27,735 - __main__ - INFO - BTCUSDT Trend: 6 valid WF windows, WF score = 0.600
2025-07-16 23:58:27,735 - __main__ - INFO - Optimizing Reversion strategy for BTCUSDT
2025-07-16 23:58:34,666 - __main__ - INFO - BTCUSDT Reversion: Best score = 1.000
2025-07-16 23:58:34,666 - __main__ - INFO - Running walk-forward validation for BTCUSDT Reversion
2025-07-16 23:58:34,667 - __main__ - INFO - Total data available: 15.6 days (1500 bars)
2025-07-16 23:58:34,667 - __main__ - INFO - Walk-forward parameters: IS=7d, OOS=3d, step=1d
2025-07-16 23:58:38,584 - __main__ - INFO - BTCUSDT Reversion: 6 valid WF windows, WF score = 0.600
2025-07-16 23:58:38,584 - __main__ - INFO - Optimizing Adaptive strategy for BTCUSDT
2025-07-16 23:58:43,650 - __main__ - INFO - BTCUSDT Adaptive: Best score = 1.000
2025-07-16 23:58:43,650 - __main__ - INFO - Running walk-forward validation for BTCUSDT Adaptive
2025-07-16 23:58:43,650 - __main__ - INFO - Total data available: 15.6 days (1500 bars)
2025-07-16 23:58:43,650 - __main__ - INFO - Walk-forward parameters: IS=7d, OOS=3d, step=1d
2025-07-16 23:58:48,449 - __main__ - INFO - BTCUSDT Adaptive: 6 valid WF windows, WF score = 0.600
2025-07-16 23:58:48,450 - __main__ - INFO - Validating and scoring strategies with anti-overfitting criteria
2025-07-16 23:58:48,504 - __main__ - INFO - ==================================================
2025-07-16 23:58:48,505 - __main__ - ERROR - Critical error in optimization pipeline: 'best_score'
2025-07-16 23:58:48,507 - __main__ - INFO - Binance client connection closed
2025-07-16 23:58:48,507 - __main__ - ERROR - Fatal error: 'best_score'
2025-07-17 00:01:10,812 - __main__ - INFO - Ultratrader Orchestrator initialized
2025-07-17 00:01:10,812 - __main__ - INFO - Ultratrader Orchestrator initialized
2025-07-17 00:01:10,812 - __main__ - INFO - Starting Ultratrader optimization pipeline
2025-07-17 00:01:11,494 - __main__ - INFO - Binance client initialized successfully
2025-07-17 00:01:11,494 - __main__ - INFO - Optimizing strategies for symbols: ['BTCUSDT']
2025-07-17 00:01:11,494 - __main__ - INFO - Fetching market data for BTCUSDT
2025-07-17 00:01:11,513 - __main__ - INFO - Retrieved 1500 bars for BTCUSDT (15m)
2025-07-17 00:01:11,513 - __main__ - INFO - Retrieved 1500 bars for BTCUSDT (1h)
2025-07-17 00:01:11,514 - __main__ - INFO - Optimizing Trend strategy for BTCUSDT
2025-07-17 00:01:19,583 - __main__ - INFO - BTCUSDT Trend: Best score = 1.000
2025-07-17 00:01:19,583 - __main__ - INFO - Running walk-forward validation for BTCUSDT Trend
2025-07-17 00:01:19,583 - __main__ - INFO - Total data available: 15.6 days (1500 bars)
2025-07-17 00:01:19,583 - __main__ - INFO - Walk-forward parameters: IS=7d, OOS=3d, step=1d
2025-07-17 00:01:22,667 - __main__ - INFO - BTCUSDT Trend: 6 valid WF windows, WF score = 0.600
2025-07-17 00:01:22,667 - __main__ - INFO - Optimizing Reversion strategy for BTCUSDT
2025-07-17 00:01:28,028 - __main__ - INFO - BTCUSDT Reversion: Best score = 1.000
2025-07-17 00:01:28,029 - __main__ - INFO - Running walk-forward validation for BTCUSDT Reversion
2025-07-17 00:01:28,029 - __main__ - INFO - Total data available: 15.6 days (1500 bars)
2025-07-17 00:01:28,029 - __main__ - INFO - Walk-forward parameters: IS=7d, OOS=3d, step=1d
2025-07-17 00:01:31,559 - __main__ - INFO - BTCUSDT Reversion: 6 valid WF windows, WF score = 0.600
2025-07-17 00:01:31,559 - __main__ - INFO - Optimizing Adaptive strategy for BTCUSDT
2025-07-17 00:01:36,678 - __main__ - INFO - BTCUSDT Adaptive: Best score = 1.000
2025-07-17 00:01:36,678 - __main__ - INFO - Running walk-forward validation for BTCUSDT Adaptive
2025-07-17 00:01:36,679 - __main__ - INFO - Total data available: 15.6 days (1500 bars)
2025-07-17 00:01:36,679 - __main__ - INFO - Walk-forward parameters: IS=7d, OOS=3d, step=1d
2025-07-17 00:01:40,775 - __main__ - INFO - BTCUSDT Adaptive: 6 valid WF windows, WF score = 0.600
2025-07-17 00:01:40,775 - __main__ - INFO - Validating and scoring strategies with anti-overfitting criteria
2025-07-17 00:01:40,778 - __main__ - INFO - ==================================================
2025-07-17 00:01:40,780 - __main__ - INFO - ==================================================
2025-07-17 00:01:40,782 - __main__ - INFO - ==================================================
2025-07-17 00:01:40,785 - __main__ - INFO -    Valid Windows: 6
2025-07-17 00:01:40,785 - __main__ - INFO -    WF Score: 0.600
2025-07-17 00:01:40,785 - __main__ - INFO -    OOS Return: -0.69%
2025-07-17 00:01:40,785 - __main__ - INFO -    OOS Sortino: -9999.00
2025-07-17 00:01:40,785 - __main__ - INFO -    Sortino Stability: -11.688
2025-07-17 00:01:40,785 - __main__ - INFO - BTCUSDT_Adaptive failed robustness validation. Failed checks: ['min_sortino', 'min_profit_factor', 'min_win_rate', 'oos_positive_return', 'oos_min_sortino']
2025-07-17 00:01:40,785 - __main__ - INFO -   Robustness score: 0.38, Sortino stability: 0.00
2025-07-17 00:01:40,787 - __main__ - INFO -    Valid Windows: 6
2025-07-17 00:01:40,787 - __main__ - INFO -    WF Score: 0.600
2025-07-17 00:01:40,787 - __main__ - INFO -    OOS Return: -0.69%
2025-07-17 00:01:40,787 - __main__ - INFO -    OOS Sortino: -9999.00
2025-07-17 00:01:40,787 - __main__ - INFO -    Sortino Stability: -11.688
2025-07-17 00:01:40,788 - __main__ - INFO - BTCUSDT_Adaptive failed robustness validation. Failed checks: ['min_sortino', 'min_profit_factor', 'min_win_rate', 'oos_positive_return', 'oos_min_sortino']
2025-07-17 00:01:40,788 - __main__ - INFO -   Robustness score: 0.38, Sortino stability: 0.00
2025-07-17 00:01:40,790 - __main__ - INFO -    Valid Windows: 6
2025-07-17 00:01:40,790 - __main__ - INFO -    WF Score: 0.600
2025-07-17 00:01:40,790 - __main__ - INFO -    OOS Return: -0.69%
2025-07-17 00:01:40,790 - __main__ - INFO -    OOS Sortino: -9999.00
2025-07-17 00:01:40,790 - __main__ - INFO -    Sortino Stability: -11.688
2025-07-17 00:01:40,790 - __main__ - INFO - BTCUSDT_Adaptive failed robustness validation. Failed checks: ['min_sortino', 'min_profit_factor', 'min_win_rate', 'oos_positive_return', 'oos_min_sortino']
2025-07-17 00:01:40,790 - __main__ - INFO -   Robustness score: 0.38, Sortino stability: 0.00
2025-07-17 00:01:40,791 - __main__ - INFO -    Valid Windows: 6
2025-07-17 00:01:40,792 - __main__ - INFO -    WF Score: 0.600
2025-07-17 00:01:40,792 - __main__ - INFO -    OOS Return: -0.69%
2025-07-17 00:01:40,792 - __main__ - INFO -    OOS Sortino: -9999.00
2025-07-17 00:01:40,792 - __main__ - INFO -    Sortino Stability: -11.688
2025-07-17 00:01:40,792 - __main__ - INFO - BTCUSDT_Adaptive failed robustness validation. Failed checks: ['min_sortino', 'min_profit_factor', 'min_win_rate', 'oos_positive_return', 'oos_min_sortino']
2025-07-17 00:01:40,792 - __main__ - INFO -   Robustness score: 0.38, Sortino stability: 0.00
2025-07-17 00:01:40,794 - __main__ - INFO -    Valid Windows: 6
2025-07-17 00:01:40,795 - __main__ - INFO -    WF Score: 0.600
2025-07-17 00:01:40,795 - __main__ - INFO -    OOS Return: -0.69%
2025-07-17 00:01:40,795 - __main__ - INFO -    OOS Sortino: -9999.00
2025-07-17 00:01:40,795 - __main__ - INFO -    Sortino Stability: -11.688
2025-07-17 00:01:40,795 - __main__ - INFO - BTCUSDT_Adaptive failed robustness validation. Failed checks: ['min_sortino', 'min_profit_factor', 'min_win_rate', 'oos_positive_return', 'oos_min_sortino']
2025-07-17 00:01:40,795 - __main__ - INFO -   Robustness score: 0.38, Sortino stability: 0.00
2025-07-17 00:01:40,798 - __main__ - INFO -    Valid Windows: 6
2025-07-17 00:01:40,798 - __main__ - INFO -    WF Score: 0.600
2025-07-17 00:01:40,798 - __main__ - INFO -    OOS Return: -0.69%
2025-07-17 00:01:40,798 - __main__ - INFO -    OOS Sortino: -9999.00
2025-07-17 00:01:40,798 - __main__ - INFO -    Sortino Stability: -11.688
2025-07-17 00:01:40,798 - __main__ - INFO - BTCUSDT_Adaptive failed robustness validation. Failed checks: ['min_sortino', 'min_profit_factor', 'min_win_rate', 'oos_positive_return', 'oos_min_sortino']
2025-07-17 00:01:40,798 - __main__ - INFO -   Robustness score: 0.38, Sortino stability: 0.00
2025-07-17 00:01:40,800 - __main__ - INFO -    Valid Windows: 6
2025-07-17 00:01:40,800 - __main__ - INFO -    WF Score: 0.600
2025-07-17 00:01:40,800 - __main__ - INFO -    OOS Return: -0.69%
2025-07-17 00:01:40,800 - __main__ - INFO -    OOS Sortino: -9999.00
2025-07-17 00:01:40,800 - __main__ - INFO -    Sortino Stability: -11.688
2025-07-17 00:01:40,801 - __main__ - INFO - BTCUSDT_Adaptive failed robustness validation. Failed checks: ['min_sortino', 'min_profit_factor', 'min_win_rate', 'oos_positive_return', 'oos_min_sortino']
2025-07-17 00:01:40,801 - __main__ - INFO -   Robustness score: 0.38, Sortino stability: 0.00
2025-07-17 00:01:40,803 - __main__ - INFO -    Valid Windows: 6
2025-07-17 00:01:40,803 - __main__ - INFO -    WF Score: 0.600
2025-07-17 00:01:40,803 - __main__ - INFO -    OOS Return: -0.69%
2025-07-17 00:01:40,803 - __main__ - INFO -    OOS Sortino: -9999.00
2025-07-17 00:01:40,803 - __main__ - INFO -    Sortino Stability: -11.688
2025-07-17 00:01:40,803 - __main__ - INFO - BTCUSDT_Adaptive failed robustness validation. Failed checks: ['min_sortino', 'min_profit_factor', 'min_win_rate', 'oos_positive_return', 'oos_min_sortino']
2025-07-17 00:01:40,803 - __main__ - INFO -   Robustness score: 0.38, Sortino stability: 0.00
2025-07-17 00:01:40,805 - __main__ - INFO -    Valid Windows: 6
2025-07-17 00:01:40,805 - __main__ - INFO -    WF Score: 0.600
2025-07-17 00:01:40,805 - __main__ - INFO -    OOS Return: -0.69%
2025-07-17 00:01:40,805 - __main__ - INFO -    OOS Sortino: -9999.00
2025-07-17 00:01:40,805 - __main__ - INFO -    Sortino Stability: -11.688
2025-07-17 00:01:40,805 - __main__ - INFO - BTCUSDT_Adaptive failed robustness validation. Failed checks: ['min_sortino', 'min_profit_factor', 'min_win_rate', 'oos_positive_return', 'oos_min_sortino']
2025-07-17 00:01:40,805 - __main__ - INFO -   Robustness score: 0.38, Sortino stability: 0.00
2025-07-17 00:01:40,807 - __main__ - INFO -    Valid Windows: 6
2025-07-17 00:01:40,807 - __main__ - INFO -    WF Score: 0.600
2025-07-17 00:01:40,807 - __main__ - INFO -    OOS Return: -0.69%
2025-07-17 00:01:40,807 - __main__ - INFO -    OOS Sortino: -9999.00
2025-07-17 00:01:40,807 - __main__ - INFO -    Sortino Stability: -11.688
2025-07-17 00:01:40,808 - __main__ - INFO - BTCUSDT_Adaptive failed robustness validation. Failed checks: ['min_sortino', 'min_profit_factor', 'min_win_rate', 'oos_positive_return', 'oos_min_sortino']
2025-07-17 00:01:40,808 - __main__ - INFO -   Robustness score: 0.38, Sortino stability: 0.00
2025-07-17 00:01:40,809 - __main__ - INFO -    Valid Windows: 6
2025-07-17 00:01:40,809 - __main__ - INFO -    WF Score: 0.600
2025-07-17 00:01:40,809 - __main__ - INFO -    OOS Return: -0.69%
2025-07-17 00:01:40,810 - __main__ - INFO -    OOS Sortino: -9999.00
2025-07-17 00:01:40,810 - __main__ - INFO -    Sortino Stability: -11.688
2025-07-17 00:01:40,810 - __main__ - INFO - BTCUSDT_Adaptive failed robustness validation. Failed checks: ['min_sortino', 'min_profit_factor', 'min_win_rate', 'oos_positive_return', 'oos_min_sortino']
2025-07-17 00:01:40,810 - __main__ - INFO -   Robustness score: 0.38, Sortino stability: 0.00
2025-07-17 00:01:40,811 - __main__ - INFO -    Valid Windows: 6
2025-07-17 00:01:40,812 - __main__ - INFO -    WF Score: 0.600
2025-07-17 00:01:40,812 - __main__ - INFO -    OOS Return: -0.69%
2025-07-17 00:01:40,812 - __main__ - INFO -    OOS Sortino: -9999.00
2025-07-17 00:01:40,812 - __main__ - INFO -    Sortino Stability: -11.688
2025-07-17 00:01:40,812 - __main__ - INFO - BTCUSDT_Adaptive failed robustness validation. Failed checks: ['min_sortino', 'min_profit_factor', 'min_win_rate', 'oos_positive_return', 'oos_min_sortino']
2025-07-17 00:01:40,812 - __main__ - INFO -   Robustness score: 0.38, Sortino stability: 0.00
2025-07-17 00:01:40,814 - __main__ - INFO -    Valid Windows: 6
2025-07-17 00:01:40,815 - __main__ - INFO -    WF Score: 0.600
2025-07-17 00:01:40,815 - __main__ - INFO -    OOS Return: -0.69%
2025-07-17 00:01:40,815 - __main__ - INFO -    OOS Sortino: -9999.00
2025-07-17 00:01:40,815 - __main__ - INFO -    Sortino Stability: -11.688
2025-07-17 00:01:40,815 - __main__ - INFO - BTCUSDT_Adaptive failed robustness validation. Failed checks: ['min_sortino', 'min_profit_factor', 'min_win_rate', 'oos_positive_return', 'oos_min_sortino']
2025-07-17 00:01:40,815 - __main__ - INFO -   Robustness score: 0.38, Sortino stability: 0.00
2025-07-17 00:01:40,818 - __main__ - INFO -    Valid Windows: 6
2025-07-17 00:01:40,818 - __main__ - INFO -    WF Score: 0.600
2025-07-17 00:01:40,818 - __main__ - INFO -    OOS Return: -0.69%
2025-07-17 00:01:40,818 - __main__ - INFO -    OOS Sortino: -9999.00
2025-07-17 00:01:40,818 - __main__ - INFO -    Sortino Stability: -11.688
2025-07-17 00:01:40,820 - __main__ - INFO - BTCUSDT_Adaptive failed robustness validation. Failed checks: ['min_sortino', 'min_profit_factor', 'min_win_rate', 'oos_positive_return', 'oos_min_sortino']
2025-07-17 00:01:40,820 - __main__ - INFO -   Robustness score: 0.38, Sortino stability: 0.00
2025-07-17 00:01:40,822 - __main__ - INFO -    Valid Windows: 6
2025-07-17 00:01:40,822 - __main__ - INFO -    WF Score: 0.600
2025-07-17 00:01:40,822 - __main__ - INFO -    OOS Return: -0.69%
2025-07-17 00:01:40,822 - __main__ - INFO -    OOS Sortino: -9999.00
2025-07-17 00:01:40,822 - __main__ - INFO -    Sortino Stability: -11.688
2025-07-17 00:01:40,822 - __main__ - INFO - BTCUSDT_Adaptive failed robustness validation. Failed checks: ['min_sortino', 'min_profit_factor', 'min_win_rate', 'oos_positive_return', 'oos_min_sortino']
2025-07-17 00:01:40,822 - __main__ - INFO -   Robustness score: 0.38, Sortino stability: 0.00
2025-07-17 00:01:40,825 - __main__ - INFO -    Valid Windows: 6
2025-07-17 00:01:40,825 - __main__ - INFO -    WF Score: 0.600
2025-07-17 00:01:40,825 - __main__ - INFO -    OOS Return: -0.69%
2025-07-17 00:01:40,825 - __main__ - INFO -    OOS Sortino: -9999.00
2025-07-17 00:01:40,825 - __main__ - INFO -    Sortino Stability: -11.688
2025-07-17 00:01:40,825 - __main__ - INFO - BTCUSDT_Adaptive failed robustness validation. Failed checks: ['min_sortino', 'min_profit_factor', 'min_win_rate', 'oos_positive_return', 'oos_min_sortino']
2025-07-17 00:01:40,825 - __main__ - INFO -   Robustness score: 0.38, Sortino stability: 0.00
2025-07-17 00:01:40,828 - __main__ - INFO -    Valid Windows: 6
2025-07-17 00:01:40,828 - __main__ - INFO -    WF Score: 0.600
2025-07-17 00:01:40,828 - __main__ - INFO -    OOS Return: -0.69%
2025-07-17 00:01:40,828 - __main__ - INFO -    OOS Sortino: -9999.00
2025-07-17 00:01:40,828 - __main__ - INFO -    Sortino Stability: -11.688
2025-07-17 00:01:40,830 - __main__ - INFO - BTCUSDT_Adaptive failed robustness validation. Failed checks: ['min_sortino', 'min_profit_factor', 'min_win_rate', 'oos_positive_return', 'oos_min_sortino']
2025-07-17 00:01:40,830 - __main__ - INFO -   Robustness score: 0.38, Sortino stability: 0.00
2025-07-17 00:01:40,834 - __main__ - INFO -    Valid Windows: 6
2025-07-17 00:01:40,834 - __main__ - INFO -    WF Score: 0.600
2025-07-17 00:01:40,835 - __main__ - INFO -    OOS Return: -0.69%
2025-07-17 00:01:40,835 - __main__ - INFO -    OOS Sortino: -9999.00
2025-07-17 00:01:40,835 - __main__ - INFO -    Sortino Stability: -11.688
2025-07-17 00:01:40,835 - __main__ - INFO - BTCUSDT_Adaptive failed robustness validation. Failed checks: ['min_sortino', 'min_profit_factor', 'min_win_rate', 'oos_positive_return', 'oos_min_sortino']
2025-07-17 00:01:40,835 - __main__ - INFO -   Robustness score: 0.38, Sortino stability: 0.00
2025-07-17 00:01:40,838 - __main__ - INFO -    Valid Windows: 6
2025-07-17 00:01:40,838 - __main__ - INFO -    WF Score: 0.600
2025-07-17 00:01:40,838 - __main__ - INFO -    OOS Return: -0.69%
2025-07-17 00:01:40,838 - __main__ - INFO -    OOS Sortino: -9999.00
2025-07-17 00:01:40,838 - __main__ - INFO -    Sortino Stability: -11.688
2025-07-17 00:01:40,838 - __main__ - INFO - BTCUSDT_Adaptive failed robustness validation. Failed checks: ['min_sortino', 'min_profit_factor', 'min_win_rate', 'oos_positive_return', 'oos_min_sortino']
2025-07-17 00:01:40,839 - __main__ - INFO -   Robustness score: 0.38, Sortino stability: 0.00
2025-07-17 00:01:40,840 - __main__ - INFO -    Valid Windows: 6
2025-07-17 00:01:40,840 - __main__ - INFO -    WF Score: 0.600
2025-07-17 00:01:40,840 - __main__ - INFO -    OOS Return: -0.69%
2025-07-17 00:01:40,840 - __main__ - INFO -    OOS Sortino: -9999.00
2025-07-17 00:01:40,841 - __main__ - INFO -    Sortino Stability: -11.688
2025-07-17 00:01:40,841 - __main__ - INFO - BTCUSDT_Adaptive failed robustness validation. Failed checks: ['min_sortino', 'min_profit_factor', 'min_win_rate', 'oos_positive_return', 'oos_min_sortino']
2025-07-17 00:01:40,841 - __main__ - INFO -   Robustness score: 0.38, Sortino stability: 0.00
2025-07-17 00:01:40,843 - __main__ - INFO -    Valid Windows: 6
2025-07-17 00:01:40,843 - __main__ - INFO -    WF Score: 0.600
2025-07-17 00:01:40,843 - __main__ - INFO -    OOS Return: -0.69%
2025-07-17 00:01:40,843 - __main__ - INFO -    OOS Sortino: -9999.00
2025-07-17 00:01:40,844 - __main__ - INFO -    Sortino Stability: -11.688
2025-07-17 00:01:40,844 - __main__ - INFO - BTCUSDT_Adaptive failed robustness validation. Failed checks: ['min_sortino', 'min_profit_factor', 'min_win_rate', 'oos_positive_return', 'oos_min_sortino']
2025-07-17 00:01:40,844 - __main__ - INFO -   Robustness score: 0.38, Sortino stability: 0.00
2025-07-17 00:01:40,846 - __main__ - INFO -    Valid Windows: 6
2025-07-17 00:01:40,846 - __main__ - INFO -    WF Score: 0.600
2025-07-17 00:01:40,846 - __main__ - INFO -    OOS Return: -0.69%
2025-07-17 00:01:40,846 - __main__ - INFO -    OOS Sortino: -9999.00
2025-07-17 00:01:40,846 - __main__ - INFO -    Sortino Stability: -11.688
2025-07-17 00:01:40,846 - __main__ - INFO - BTCUSDT_Adaptive failed robustness validation. Failed checks: ['min_sortino', 'min_profit_factor', 'min_win_rate', 'oos_positive_return', 'oos_min_sortino']
2025-07-17 00:01:40,847 - __main__ - INFO -   Robustness score: 0.38, Sortino stability: 0.00
2025-07-17 00:01:40,849 - __main__ - INFO -    Valid Windows: 6
2025-07-17 00:01:40,849 - __main__ - INFO -    WF Score: 0.600
2025-07-17 00:01:40,849 - __main__ - INFO -    OOS Return: -0.69%
2025-07-17 00:01:40,849 - __main__ - INFO -    OOS Sortino: -9999.00
2025-07-17 00:01:40,849 - __main__ - INFO -    Sortino Stability: -11.688
2025-07-17 00:01:40,849 - __main__ - INFO - BTCUSDT_Adaptive failed robustness validation. Failed checks: ['min_sortino', 'min_profit_factor', 'min_win_rate', 'oos_positive_return', 'oos_min_sortino']
2025-07-17 00:01:40,850 - __main__ - INFO -   Robustness score: 0.38, Sortino stability: 0.00
2025-07-17 00:01:40,851 - __main__ - INFO -    Valid Windows: 6
2025-07-17 00:01:40,852 - __main__ - INFO -    WF Score: 0.600
2025-07-17 00:01:40,852 - __main__ - INFO -    OOS Return: -0.69%
2025-07-17 00:01:40,852 - __main__ - INFO -    OOS Sortino: -9999.00
2025-07-17 00:01:40,852 - __main__ - INFO -    Sortino Stability: -11.688
2025-07-17 00:01:40,853 - __main__ - INFO - BTCUSDT_Adaptive failed robustness validation. Failed checks: ['min_sortino', 'min_profit_factor', 'min_win_rate', 'oos_positive_return', 'oos_min_sortino']
2025-07-17 00:01:40,853 - __main__ - INFO -   Robustness score: 0.38, Sortino stability: 0.00
2025-07-17 00:01:40,854 - __main__ - INFO -    Valid Windows: 6
2025-07-17 00:01:40,854 - __main__ - INFO -    WF Score: 0.600
2025-07-17 00:01:40,854 - __main__ - INFO -    OOS Return: -0.69%
2025-07-17 00:01:40,855 - __main__ - INFO -    OOS Sortino: -9999.00
2025-07-17 00:01:40,855 - __main__ - INFO -    Sortino Stability: -11.688
2025-07-17 00:01:40,855 - __main__ - INFO - BTCUSDT_Adaptive failed robustness validation. Failed checks: ['min_sortino', 'min_profit_factor', 'min_win_rate', 'oos_positive_return', 'oos_min_sortino']
2025-07-17 00:01:40,855 - __main__ - INFO -   Robustness score: 0.38, Sortino stability: 0.00
2025-07-17 00:01:40,857 - __main__ - INFO -    Valid Windows: 6
2025-07-17 00:01:40,857 - __main__ - INFO -    WF Score: 0.600
2025-07-17 00:01:40,857 - __main__ - INFO -    OOS Return: -0.69%
2025-07-17 00:01:40,857 - __main__ - INFO -    OOS Sortino: -9999.00
2025-07-17 00:01:40,857 - __main__ - INFO -    Sortino Stability: -11.688
2025-07-17 00:01:40,857 - __main__ - INFO - BTCUSDT_Adaptive failed robustness validation. Failed checks: ['min_sortino', 'min_profit_factor', 'min_win_rate', 'oos_positive_return', 'oos_min_sortino']
2025-07-17 00:01:40,857 - __main__ - INFO -   Robustness score: 0.38, Sortino stability: 0.00
2025-07-17 00:01:40,859 - __main__ - INFO -    Valid Windows: 6
2025-07-17 00:01:40,859 - __main__ - INFO -    WF Score: 0.600
2025-07-17 00:01:40,859 - __main__ - INFO -    OOS Return: -0.69%
2025-07-17 00:01:40,859 - __main__ - INFO -    OOS Sortino: -9999.00
2025-07-17 00:01:40,859 - __main__ - INFO -    Sortino Stability: -11.688
2025-07-17 00:01:40,859 - __main__ - INFO - BTCUSDT_Adaptive failed robustness validation. Failed checks: ['min_sortino', 'min_profit_factor', 'min_win_rate', 'oos_positive_return', 'oos_min_sortino']
2025-07-17 00:01:40,859 - __main__ - INFO -   Robustness score: 0.38, Sortino stability: 0.00
2025-07-17 00:01:40,861 - __main__ - INFO -    Valid Windows: 6
2025-07-17 00:01:40,861 - __main__ - INFO -    WF Score: 0.600
2025-07-17 00:01:40,861 - __main__ - INFO -    OOS Return: -0.69%
2025-07-17 00:01:40,861 - __main__ - INFO -    OOS Sortino: -9999.00
2025-07-17 00:01:40,861 - __main__ - INFO -    Sortino Stability: -11.688
2025-07-17 00:01:40,861 - __main__ - INFO - BTCUSDT_Adaptive failed robustness validation. Failed checks: ['min_sortino', 'min_profit_factor', 'min_win_rate', 'oos_positive_return', 'oos_min_sortino']
2025-07-17 00:01:40,861 - __main__ - INFO -   Robustness score: 0.38, Sortino stability: 0.00
2025-07-17 00:01:40,864 - __main__ - INFO -    Valid Windows: 6
2025-07-17 00:01:40,864 - __main__ - INFO -    WF Score: 0.600
2025-07-17 00:01:40,865 - __main__ - INFO -    OOS Return: -0.69%
2025-07-17 00:01:40,865 - __main__ - INFO -    OOS Sortino: -9999.00
2025-07-17 00:01:40,865 - __main__ - INFO -    Sortino Stability: -11.688
2025-07-17 00:01:40,865 - __main__ - INFO - BTCUSDT_Adaptive failed robustness validation. Failed checks: ['min_sortino', 'min_profit_factor', 'min_win_rate', 'oos_positive_return', 'oos_min_sortino']
2025-07-17 00:01:40,865 - __main__ - INFO -   Robustness score: 0.38, Sortino stability: 0.00
2025-07-17 00:01:40,867 - __main__ - INFO -    Valid Windows: 6
2025-07-17 00:01:40,867 - __main__ - INFO -    WF Score: 0.600
2025-07-17 00:01:40,868 - __main__ - INFO -    OOS Return: -0.69%
2025-07-17 00:01:40,868 - __main__ - INFO -    OOS Sortino: -9999.00
2025-07-17 00:01:40,868 - __main__ - INFO -    Sortino Stability: -11.688
2025-07-17 00:01:40,868 - __main__ - INFO - BTCUSDT_Adaptive failed robustness validation. Failed checks: ['min_sortino', 'min_profit_factor', 'min_win_rate', 'oos_positive_return', 'oos_min_sortino']
2025-07-17 00:01:40,868 - __main__ - INFO -   Robustness score: 0.38, Sortino stability: 0.00
2025-07-17 00:01:40,868 - __main__ - INFO - Saving optimization results
2025-07-17 00:01:40,868 - __main__ - WARNING - No validated strategies to save
2025-07-17 00:01:40,868 - __main__ - WARNING - Optimization completed but no strategies passed validation
2025-07-17 00:01:40,869 - __main__ - INFO - Binance client connection closed
2025-07-17 00:03:19,240 - __main__ - INFO - Ultratrader Orchestrator initialized
2025-07-17 00:03:19,240 - __main__ - INFO - Ultratrader Orchestrator initialized
2025-07-17 00:03:19,240 - __main__ - INFO - Starting Ultratrader optimization pipeline
2025-07-17 00:03:19,929 - __main__ - INFO - Binance client initialized successfully
2025-07-17 00:03:19,929 - __main__ - INFO - Optimizing strategies for symbols: ['BTCUSDT']
2025-07-17 00:03:19,929 - __main__ - INFO - Fetching market data for BTCUSDT
2025-07-17 00:03:19,941 - __main__ - INFO - Retrieved 1500 bars for BTCUSDT (15m)
2025-07-17 00:03:19,941 - __main__ - INFO - Retrieved 1500 bars for BTCUSDT (1h)
2025-07-17 00:03:19,941 - __main__ - INFO - Optimizing Trend strategy for BTCUSDT
2025-07-17 00:03:26,733 - __main__ - INFO - BTCUSDT Trend: Best score = 1.000
2025-07-17 00:03:26,734 - __main__ - INFO - Running walk-forward validation for BTCUSDT Trend
2025-07-17 00:03:26,734 - __main__ - INFO - Total data available: 15.6 days (1500 bars)
2025-07-17 00:03:26,734 - __main__ - INFO - Walk-forward parameters: IS=7d, OOS=3d, step=1d
2025-07-17 00:03:29,787 - __main__ - INFO - BTCUSDT Trend: 6 valid WF windows, WF score = 0.600
2025-07-17 00:03:29,788 - __main__ - INFO - Optimizing Reversion strategy for BTCUSDT
2025-07-17 00:03:35,682 - __main__ - INFO - BTCUSDT Reversion: Best score = 1.000
2025-07-17 00:03:35,682 - __main__ - INFO - Running walk-forward validation for BTCUSDT Reversion
2025-07-17 00:03:35,682 - __main__ - INFO - Total data available: 15.6 days (1500 bars)
2025-07-17 00:03:35,683 - __main__ - INFO - Walk-forward parameters: IS=7d, OOS=3d, step=1d
2025-07-17 00:03:39,326 - __main__ - INFO - BTCUSDT Reversion: 6 valid WF windows, WF score = 0.600
2025-07-17 00:03:39,326 - __main__ - INFO - Optimizing Adaptive strategy for BTCUSDT
2025-07-17 00:03:44,602 - __main__ - INFO - BTCUSDT Adaptive: Best score = 1.000
2025-07-17 00:03:44,602 - __main__ - INFO - Running walk-forward validation for BTCUSDT Adaptive
2025-07-17 00:03:44,603 - __main__ - INFO - Total data available: 15.6 days (1500 bars)
2025-07-17 00:03:44,603 - __main__ - INFO - Walk-forward parameters: IS=7d, OOS=3d, step=1d
2025-07-17 00:03:49,037 - __main__ - INFO - BTCUSDT Adaptive: 6 valid WF windows, WF score = 0.600
2025-07-17 00:03:49,037 - __main__ - INFO - Validating and scoring strategies with anti-overfitting criteria
2025-07-17 00:03:49,039 - __main__ - INFO - BTCUSDT_Trend failed robustness validation. Failed checks: ['min_sortino', 'min_profit_factor', 'min_win_rate', 'oos_positive_return', 'oos_min_sortino']
2025-07-17 00:03:49,039 - __main__ - INFO -   Robustness score: 0.38, Sortino stability: 0.00
2025-07-17 00:03:49,042 - __main__ - INFO - BTCUSDT_Reversion failed robustness validation. Failed checks: ['min_sortino', 'min_profit_factor', 'min_win_rate', 'oos_positive_return', 'oos_min_sortino']
2025-07-17 00:03:49,042 - __main__ - INFO -   Robustness score: 0.38, Sortino stability: 0.00
2025-07-17 00:03:49,046 - __main__ - INFO - BTCUSDT_Adaptive failed robustness validation. Failed checks: ['min_sortino', 'min_profit_factor', 'min_win_rate', 'oos_positive_return', 'oos_min_sortino']
2025-07-17 00:03:49,046 - __main__ - INFO -   Robustness score: 0.38, Sortino stability: 0.00
2025-07-17 00:03:49,046 - __main__ - INFO - Saving optimization results
2025-07-17 00:03:49,046 - __main__ - WARNING - No validated strategies to save
2025-07-17 00:03:49,046 - __main__ - WARNING - Optimization completed but no strategies passed validation
2025-07-17 00:03:49,047 - __main__ - INFO - Binance client connection closed
2025-07-17 00:05:05,250 - __main__ - INFO - Ultratrader Orchestrator initialized
2025-07-17 00:05:05,250 - __main__ - INFO - Ultratrader Orchestrator initialized
2025-07-17 00:05:05,250 - __main__ - INFO - Starting Ultratrader optimization pipeline
2025-07-17 00:05:05,937 - __main__ - INFO - Binance client initialized successfully
2025-07-17 00:05:05,937 - __main__ - INFO - Optimizing strategies for symbols: ['BTCUSDT']
2025-07-17 00:05:05,937 - __main__ - INFO - Fetching market data for BTCUSDT
2025-07-17 00:05:05,951 - __main__ - INFO - Retrieved 1500 bars for BTCUSDT (15m)
2025-07-17 00:05:05,952 - __main__ - INFO - Retrieved 1500 bars for BTCUSDT (1h)
2025-07-17 00:05:05,952 - __main__ - INFO - Optimizing Trend strategy for BTCUSDT
2025-07-17 00:05:12,542 - __main__ - INFO - BTCUSDT Trend: Best score = 5.638
2025-07-17 00:05:12,543 - __main__ - INFO - Running walk-forward validation for BTCUSDT Trend
2025-07-17 00:05:12,543 - __main__ - INFO - Total data available: 15.6 days (1500 bars)
2025-07-17 00:05:12,543 - __main__ - INFO - Walk-forward parameters: IS=7d, OOS=3d, step=1d
2025-07-17 00:05:15,539 - __main__ - INFO - BTCUSDT Trend: 6 valid WF windows, WF score = 0.600
2025-07-17 00:05:15,540 - __main__ - INFO - Optimizing Reversion strategy for BTCUSDT
2025-07-17 00:05:20,781 - __main__ - INFO - BTCUSDT Reversion: Best score = 5.456
2025-07-17 00:05:20,781 - __main__ - INFO - Running walk-forward validation for BTCUSDT Reversion
2025-07-17 00:05:20,781 - __main__ - INFO - Total data available: 15.6 days (1500 bars)
2025-07-17 00:05:20,781 - __main__ - INFO - Walk-forward parameters: IS=7d, OOS=3d, step=1d
2025-07-17 00:05:23,911 - __main__ - INFO - BTCUSDT Reversion: 6 valid WF windows, WF score = 0.600
2025-07-17 00:05:23,911 - __main__ - INFO - Optimizing Adaptive strategy for BTCUSDT
2025-07-17 00:05:28,152 - __main__ - INFO - BTCUSDT Adaptive: Best score = 5.637
2025-07-17 00:05:28,152 - __main__ - INFO - Running walk-forward validation for BTCUSDT Adaptive
2025-07-17 00:05:28,152 - __main__ - INFO - Total data available: 15.6 days (1500 bars)
2025-07-17 00:05:28,153 - __main__ - INFO - Walk-forward parameters: IS=7d, OOS=3d, step=1d
2025-07-17 00:05:32,218 - __main__ - INFO - BTCUSDT Adaptive: 6 valid WF windows, WF score = 0.600
2025-07-17 00:05:32,218 - __main__ - INFO - Validating and scoring strategies with anti-overfitting criteria
2025-07-17 00:05:32,220 - __main__ - INFO - BTCUSDT_Trend failed robustness validation. Failed checks: ['oos_positive_return', 'oos_min_sortino']
2025-07-17 00:05:32,220 - __main__ - INFO -   Robustness score: 0.75, Sortino stability: -1.00
2025-07-17 00:05:32,222 - __main__ - INFO - BTCUSDT_Reversion failed robustness validation. Failed checks: ['oos_positive_return', 'oos_min_sortino']
2025-07-17 00:05:32,222 - __main__ - INFO -   Robustness score: 0.75, Sortino stability: -1.00
2025-07-17 00:05:32,225 - __main__ - INFO - BTCUSDT_Adaptive failed robustness validation. Failed checks: ['oos_positive_return', 'oos_min_sortino']
2025-07-17 00:05:32,225 - __main__ - INFO -   Robustness score: 0.75, Sortino stability: -1.00
2025-07-17 00:05:32,225 - __main__ - INFO - Saving optimization results
2025-07-17 00:05:32,225 - __main__ - WARNING - No validated strategies to save
2025-07-17 00:05:32,225 - __main__ - WARNING - Optimization completed but no strategies passed validation
2025-07-17 00:05:32,226 - __main__ - INFO - Binance client connection closed
2025-07-17 00:06:17,094 - __main__ - INFO - Ultratrader Orchestrator initialized
2025-07-17 00:06:17,094 - __main__ - INFO - Ultratrader Orchestrator initialized
2025-07-17 00:06:17,094 - __main__ - INFO - Starting Ultratrader optimization pipeline
2025-07-17 00:06:17,774 - __main__ - INFO - Binance client initialized successfully
2025-07-17 00:06:17,774 - __main__ - INFO - Optimizing strategies for symbols: ['BTCUSDT']
2025-07-17 00:06:17,774 - __main__ - INFO - Fetching market data for BTCUSDT
2025-07-17 00:06:17,788 - __main__ - INFO - Retrieved 1500 bars for BTCUSDT (15m)
2025-07-17 00:06:17,789 - __main__ - INFO - Retrieved 1500 bars for BTCUSDT (1h)
2025-07-17 00:06:17,789 - __main__ - INFO - Optimizing Trend strategy for BTCUSDT
2025-07-17 00:06:24,492 - __main__ - INFO - BTCUSDT Trend: Best score = 5.638
2025-07-17 00:06:24,492 - __main__ - INFO - Running walk-forward validation for BTCUSDT Trend
2025-07-17 00:06:24,492 - __main__ - INFO - Total data available: 15.6 days (1500 bars)
2025-07-17 00:06:24,493 - __main__ - INFO - Walk-forward parameters: IS=7d, OOS=3d, step=1d
2025-07-17 00:06:27,425 - __main__ - INFO - BTCUSDT Trend: 6 valid WF windows, WF score = 0.600
2025-07-17 00:06:27,425 - __main__ - INFO - Optimizing Reversion strategy for BTCUSDT
2025-07-17 00:06:32,523 - __main__ - INFO - BTCUSDT Reversion: Best score = 5.456
2025-07-17 00:06:32,523 - __main__ - INFO - Running walk-forward validation for BTCUSDT Reversion
2025-07-17 00:06:32,523 - __main__ - INFO - Total data available: 15.6 days (1500 bars)
2025-07-17 00:06:32,524 - __main__ - INFO - Walk-forward parameters: IS=7d, OOS=3d, step=1d
2025-07-17 00:06:35,647 - __main__ - INFO - BTCUSDT Reversion: 6 valid WF windows, WF score = 0.600
2025-07-17 00:06:35,647 - __main__ - INFO - Optimizing Adaptive strategy for BTCUSDT
2025-07-17 00:06:39,827 - __main__ - INFO - BTCUSDT Adaptive: Best score = 5.637
2025-07-17 00:06:39,827 - __main__ - INFO - Running walk-forward validation for BTCUSDT Adaptive
2025-07-17 00:06:39,827 - __main__ - INFO - Total data available: 15.6 days (1500 bars)
2025-07-17 00:06:39,827 - __main__ - INFO - Walk-forward parameters: IS=7d, OOS=3d, step=1d
2025-07-17 00:06:43,847 - __main__ - INFO - BTCUSDT Adaptive: 6 valid WF windows, WF score = 0.600
2025-07-17 00:06:43,847 - __main__ - INFO - Validating and scoring strategies with anti-overfitting criteria
2025-07-17 00:06:43,850 - __main__ - INFO - BTCUSDT_Trend failed robustness validation. Failed checks: ['oos_positive_return', 'oos_min_sortino']
2025-07-17 00:06:43,850 - __main__ - INFO -   Robustness score: 0.75, Sortino stability: -1.00
2025-07-17 00:06:43,852 - __main__ - INFO - BTCUSDT_Reversion failed robustness validation. Failed checks: ['oos_positive_return', 'oos_min_sortino']
2025-07-17 00:06:43,852 - __main__ - INFO -   Robustness score: 0.75, Sortino stability: -1.00
2025-07-17 00:06:43,854 - __main__ - INFO - BTCUSDT_Adaptive failed robustness validation. Failed checks: ['oos_positive_return', 'oos_min_sortino']
2025-07-17 00:06:43,854 - __main__ - INFO -   Robustness score: 0.75, Sortino stability: -1.00
2025-07-17 00:06:43,854 - __main__ - INFO - Saving optimization results
2025-07-17 00:06:43,854 - __main__ - WARNING - No validated strategies to save
2025-07-17 00:06:43,854 - __main__ - WARNING - Optimization completed but no strategies passed validation
2025-07-17 00:06:43,854 - __main__ - INFO - Binance client connection closed
2025-07-17 00:09:04,603 - __main__ - INFO - Ultratrader Orchestrator initialized
2025-07-17 00:09:04,603 - __main__ - INFO - Ultratrader Orchestrator initialized
2025-07-17 00:09:04,603 - __main__ - INFO - Starting Ultratrader optimization pipeline
2025-07-17 00:09:05,288 - __main__ - INFO - Binance client initialized successfully
2025-07-17 00:09:05,288 - __main__ - INFO - Optimizing strategies for symbols: ['BTCUSDT']
2025-07-17 00:09:05,288 - __main__ - INFO - Fetching market data for BTCUSDT
2025-07-17 00:09:05,309 - __main__ - INFO - Retrieved 1500 bars for BTCUSDT (15m)
2025-07-17 00:09:05,309 - __main__ - INFO - Retrieved 1500 bars for BTCUSDT (1h)
2025-07-17 00:09:05,309 - __main__ - INFO - Optimizing Trend strategy for BTCUSDT
2025-07-17 00:09:12,351 - __main__ - INFO - BTCUSDT Trend: Best score = 5.638
2025-07-17 00:09:12,351 - __main__ - INFO - Running walk-forward validation for BTCUSDT Trend
2025-07-17 00:09:12,352 - __main__ - INFO - Total data available: 15.6 days (1500 bars)
2025-07-17 00:09:12,352 - __main__ - INFO - Walk-forward parameters: IS=7d, OOS=3d, step=1d
2025-07-17 00:09:15,402 - __main__ - INFO - BTCUSDT Trend: 6 valid WF windows, WF score = 0.600
2025-07-17 00:09:15,403 - __main__ - INFO - Optimizing Reversion strategy for BTCUSDT
2025-07-17 00:09:20,449 - __main__ - INFO - BTCUSDT Reversion: Best score = 5.456
2025-07-17 00:09:20,449 - __main__ - INFO - Running walk-forward validation for BTCUSDT Reversion
2025-07-17 00:09:20,449 - __main__ - INFO - Total data available: 15.6 days (1500 bars)
2025-07-17 00:09:20,450 - __main__ - INFO - Walk-forward parameters: IS=7d, OOS=3d, step=1d
2025-07-17 00:09:23,588 - __main__ - INFO - BTCUSDT Reversion: 6 valid WF windows, WF score = 0.600
2025-07-17 00:09:23,589 - __main__ - INFO - Optimizing Adaptive strategy for BTCUSDT
2025-07-17 00:09:27,902 - __main__ - INFO - BTCUSDT Adaptive: Best score = 5.637
2025-07-17 00:09:27,903 - __main__ - INFO - Running walk-forward validation for BTCUSDT Adaptive
2025-07-17 00:09:27,903 - __main__ - INFO - Total data available: 15.6 days (1500 bars)
2025-07-17 00:09:27,903 - __main__ - INFO - Walk-forward parameters: IS=7d, OOS=3d, step=1d
2025-07-17 00:09:31,936 - __main__ - INFO - BTCUSDT Adaptive: 6 valid WF windows, WF score = 0.600
2025-07-17 00:09:31,936 - __main__ - INFO - Validating and scoring strategies with anti-overfitting criteria
2025-07-17 00:09:31,938 - __main__ - INFO - BTCUSDT_Trend failed robustness validation. Failed checks: ['oos_positive_return', 'oos_min_sortino']
2025-07-17 00:09:31,939 - __main__ - INFO -   Robustness score: 0.75, Sortino stability: -1.00
2025-07-17 00:09:31,942 - __main__ - INFO - BTCUSDT_Reversion failed robustness validation. Failed checks: ['oos_positive_return', 'oos_min_sortino']
2025-07-17 00:09:31,942 - __main__ - INFO -   Robustness score: 0.75, Sortino stability: -1.00
2025-07-17 00:09:31,945 - __main__ - INFO - BTCUSDT_Adaptive failed robustness validation. Failed checks: ['oos_positive_return', 'oos_min_sortino']
2025-07-17 00:09:31,946 - __main__ - INFO -   Robustness score: 0.75, Sortino stability: -1.00
2025-07-17 00:09:31,946 - __main__ - INFO - Saving optimization results
2025-07-17 00:09:31,946 - __main__ - WARNING - No validated strategies to save
2025-07-17 00:09:31,946 - __main__ - WARNING - Optimization completed but no strategies passed validation
2025-07-17 00:09:31,946 - __main__ - INFO - Binance client connection closed
2025-07-17 00:11:10,824 - __main__ - INFO - Ultratrader Orchestrator initialized
2025-07-17 00:11:10,824 - __main__ - INFO - Ultratrader Orchestrator initialized
2025-07-17 00:11:10,824 - __main__ - INFO - Starting Ultratrader optimization pipeline
2025-07-17 00:11:11,507 - __main__ - INFO - Binance client initialized successfully
2025-07-17 00:11:11,507 - __main__ - INFO - Optimizing strategies for symbols: ['BTCUSDT']
2025-07-17 00:11:11,507 - __main__ - INFO - Fetching market data for BTCUSDT
2025-07-17 00:11:11,518 - __main__ - INFO - Retrieved 1500 bars for BTCUSDT (15m)
2025-07-17 00:11:11,518 - __main__ - INFO - Retrieved 1500 bars for BTCUSDT (1h)
2025-07-17 00:11:11,518 - __main__ - INFO - Optimizing Trend strategy for BTCUSDT
2025-07-17 00:11:18,491 - __main__ - INFO - BTCUSDT Trend: Best score = 1.000
2025-07-17 00:11:18,491 - __main__ - INFO - Running walk-forward validation for BTCUSDT Trend
2025-07-17 00:11:18,492 - __main__ - INFO - Total data available: 15.6 days (1500 bars)
2025-07-17 00:11:18,492 - __main__ - INFO - Walk-forward parameters: IS=7d, OOS=3d, step=1d
2025-07-17 00:11:21,815 - __main__ - INFO - BTCUSDT Trend: 6 valid WF windows, WF score = 0.175
2025-07-17 00:11:21,815 - __main__ - INFO - Optimizing Reversion strategy for BTCUSDT
2025-07-17 00:11:27,212 - __main__ - INFO - BTCUSDT Reversion: Best score = 1.323
2025-07-17 00:11:27,212 - __main__ - INFO - Running walk-forward validation for BTCUSDT Reversion
2025-07-17 00:11:27,212 - __main__ - INFO - Total data available: 15.6 days (1500 bars)
2025-07-17 00:11:27,214 - __main__ - INFO - Walk-forward parameters: IS=7d, OOS=3d, step=1d
2025-07-17 00:11:30,536 - __main__ - INFO - BTCUSDT Reversion: 6 valid WF windows, WF score = 0.600
2025-07-17 00:11:30,536 - __main__ - INFO - Optimizing Adaptive strategy for BTCUSDT
2025-07-17 00:11:35,070 - __main__ - INFO - BTCUSDT Adaptive: Best score = 1.000
2025-07-17 00:11:35,071 - __main__ - INFO - Running walk-forward validation for BTCUSDT Adaptive
2025-07-17 00:11:35,071 - __main__ - INFO - Total data available: 15.6 days (1500 bars)
2025-07-17 00:11:35,071 - __main__ - INFO - Walk-forward parameters: IS=7d, OOS=3d, step=1d
2025-07-17 00:11:39,396 - __main__ - INFO - BTCUSDT Adaptive: 6 valid WF windows, WF score = 0.600
2025-07-17 00:11:39,397 - __main__ - INFO - Validating and scoring strategies with anti-overfitting criteria
2025-07-17 00:11:39,398 - __main__ - INFO - BTCUSDT_Trend failed robustness validation. Failed checks: ['min_sortino', 'min_profit_factor', 'min_win_rate', 'oos_positive_return', 'oos_min_sortino']
2025-07-17 00:11:39,398 - __main__ - INFO -   Robustness score: 0.38, Sortino stability: 0.00
2025-07-17 00:11:39,400 - __main__ - INFO - BTCUSDT_Reversion failed robustness validation. Failed checks: ['min_sortino', 'min_profit_factor', 'oos_positive_return', 'oos_min_sortino']
2025-07-17 00:11:39,401 - __main__ - INFO -   Robustness score: 0.50, Sortino stability: 0.00
2025-07-17 00:11:39,404 - __main__ - INFO - BTCUSDT_Adaptive failed robustness validation. Failed checks: ['min_sortino', 'min_profit_factor', 'min_win_rate', 'oos_positive_return', 'oos_min_sortino']
2025-07-17 00:11:39,404 - __main__ - INFO -   Robustness score: 0.38, Sortino stability: 0.00
2025-07-17 00:11:39,404 - __main__ - INFO - Saving optimization results
2025-07-17 00:11:39,404 - __main__ - WARNING - No validated strategies to save
2025-07-17 00:11:39,404 - __main__ - WARNING - Optimization completed but no strategies passed validation
2025-07-17 00:11:39,405 - __main__ - INFO - Binance client connection closed
2025-07-17 00:13:07,838 - __main__ - INFO - Ultratrader Orchestrator initialized
2025-07-17 00:13:07,838 - __main__ - INFO - Ultratrader Orchestrator initialized
2025-07-17 00:13:07,838 - __main__ - INFO - Starting Ultratrader optimization pipeline
2025-07-17 00:13:08,525 - __main__ - INFO - Binance client initialized successfully
2025-07-17 00:13:08,525 - __main__ - INFO - Optimizing strategies for symbols: ['BTCUSDT']
2025-07-17 00:13:08,525 - __main__ - INFO - Fetching market data for BTCUSDT
2025-07-17 00:13:08,537 - __main__ - INFO - Retrieved 1500 bars for BTCUSDT (15m)
2025-07-17 00:13:08,538 - __main__ - INFO - Retrieved 1500 bars for BTCUSDT (1h)
2025-07-17 00:13:08,538 - __main__ - INFO - Optimizing Trend strategy for BTCUSDT
2025-07-17 00:13:15,489 - __main__ - INFO - BTCUSDT Trend: Best score = 1.000
2025-07-17 00:13:15,489 - __main__ - INFO - Running walk-forward validation for BTCUSDT Trend
2025-07-17 00:13:15,489 - __main__ - INFO - Total data available: 15.6 days (1500 bars)
2025-07-17 00:13:15,489 - __main__ - INFO - Walk-forward parameters: IS=7d, OOS=3d, step=1d
2025-07-17 00:13:18,931 - __main__ - INFO - BTCUSDT Trend: 6 valid WF windows, WF score = 0.175
2025-07-17 00:13:18,931 - __main__ - INFO - Optimizing Reversion strategy for BTCUSDT
2025-07-17 00:13:24,351 - __main__ - INFO - BTCUSDT Reversion: Best score = 1.323
2025-07-17 00:13:24,352 - __main__ - INFO - Running walk-forward validation for BTCUSDT Reversion
2025-07-17 00:13:24,352 - __main__ - INFO - Total data available: 15.6 days (1500 bars)
2025-07-17 00:13:24,352 - __main__ - INFO - Walk-forward parameters: IS=7d, OOS=3d, step=1d
2025-07-17 00:13:27,790 - __main__ - INFO - BTCUSDT Reversion: 6 valid WF windows, WF score = 0.600
2025-07-17 00:13:27,791 - __main__ - INFO - Optimizing Adaptive strategy for BTCUSDT
2025-07-17 00:13:32,387 - __main__ - INFO - BTCUSDT Adaptive: Best score = 1.000
2025-07-17 00:13:32,388 - __main__ - INFO - Running walk-forward validation for BTCUSDT Adaptive
2025-07-17 00:13:32,388 - __main__ - INFO - Total data available: 15.6 days (1500 bars)
2025-07-17 00:13:32,388 - __main__ - INFO - Walk-forward parameters: IS=7d, OOS=3d, step=1d
2025-07-17 00:13:36,981 - __main__ - INFO - BTCUSDT Adaptive: 6 valid WF windows, WF score = 0.600
2025-07-17 00:13:36,981 - __main__ - INFO - Validating and scoring strategies with anti-overfitting criteria
2025-07-17 00:13:36,983 - __main__ - INFO - BTCUSDT_Trend failed robustness validation. Failed checks: ['min_sortino', 'min_profit_factor', 'min_win_rate', 'oos_positive_return', 'oos_min_sortino']
2025-07-17 00:13:36,983 - __main__ - INFO -   Robustness score: 0.38, Sortino stability: 0.00
2025-07-17 00:13:36,986 - __main__ - INFO - BTCUSDT_Reversion failed robustness validation. Failed checks: ['min_sortino', 'min_profit_factor', 'oos_positive_return', 'oos_min_sortino']
2025-07-17 00:13:36,986 - __main__ - INFO -   Robustness score: 0.50, Sortino stability: 0.00
2025-07-17 00:13:36,989 - __main__ - INFO - BTCUSDT_Adaptive failed robustness validation. Failed checks: ['min_sortino', 'min_profit_factor', 'min_win_rate', 'oos_positive_return', 'oos_min_sortino']
2025-07-17 00:13:36,989 - __main__ - INFO -   Robustness score: 0.38, Sortino stability: 0.00
2025-07-17 00:13:36,989 - __main__ - INFO - Saving optimization results
2025-07-17 00:13:36,989 - __main__ - WARNING - No validated strategies to save
2025-07-17 00:13:36,989 - __main__ - WARNING - Optimization completed but no strategies passed validation
2025-07-17 00:13:36,989 - __main__ - INFO - Binance client connection closed
2025-07-17 00:14:19,464 - __main__ - INFO - Ultratrader Orchestrator initialized
2025-07-17 00:14:19,465 - __main__ - INFO - Ultratrader Orchestrator initialized
2025-07-17 00:14:19,465 - __main__ - INFO - Starting Ultratrader optimization pipeline
2025-07-17 00:14:20,147 - __main__ - INFO - Binance client initialized successfully
2025-07-17 00:14:20,147 - __main__ - INFO - Optimizing strategies for symbols: ['BTCUSDT']
2025-07-17 00:14:20,147 - __main__ - INFO - Fetching market data for BTCUSDT
2025-07-17 00:14:20,162 - __main__ - INFO - Retrieved 1500 bars for BTCUSDT (15m)
2025-07-17 00:14:20,162 - __main__ - INFO - Retrieved 1500 bars for BTCUSDT (1h)
2025-07-17 00:14:20,162 - __main__ - INFO - Optimizing Trend strategy for BTCUSDT
2025-07-17 00:14:26,924 - __main__ - INFO - BTCUSDT Trend: Best score = 5.423
2025-07-17 00:14:26,924 - __main__ - INFO - Running walk-forward validation for BTCUSDT Trend
2025-07-17 00:14:26,924 - __main__ - INFO - Total data available: 15.6 days (1500 bars)
2025-07-17 00:14:26,924 - __main__ - INFO - Walk-forward parameters: IS=7d, OOS=3d, step=1d
2025-07-17 00:14:30,155 - __main__ - INFO - BTCUSDT Trend: 6 valid WF windows, WF score = 0.600
2025-07-17 00:14:30,155 - __main__ - INFO - Optimizing Reversion strategy for BTCUSDT
2025-07-17 00:14:35,382 - __main__ - INFO - BTCUSDT Reversion: Best score = 7.050
2025-07-17 00:14:35,382 - __main__ - INFO - Running walk-forward validation for BTCUSDT Reversion
2025-07-17 00:14:35,382 - __main__ - INFO - Total data available: 15.6 days (1500 bars)
2025-07-17 00:14:35,382 - __main__ - INFO - Walk-forward parameters: IS=7d, OOS=3d, step=1d
2025-07-17 00:14:38,761 - __main__ - INFO - BTCUSDT Reversion: 6 valid WF windows, WF score = 0.600
2025-07-17 00:14:38,761 - __main__ - INFO - Optimizing Adaptive strategy for BTCUSDT
2025-07-17 00:14:43,220 - __main__ - INFO - BTCUSDT Adaptive: Best score = 6.231
2025-07-17 00:14:43,220 - __main__ - INFO - Running walk-forward validation for BTCUSDT Adaptive
2025-07-17 00:14:43,220 - __main__ - INFO - Total data available: 15.6 days (1500 bars)
2025-07-17 00:14:43,221 - __main__ - INFO - Walk-forward parameters: IS=7d, OOS=3d, step=1d
2025-07-17 00:14:47,329 - __main__ - INFO - BTCUSDT Adaptive: 6 valid WF windows, WF score = 0.646
2025-07-17 00:14:47,329 - __main__ - INFO - Validating and scoring strategies with anti-overfitting criteria
2025-07-17 00:14:47,331 - __main__ - INFO - BTCUSDT_Trend failed robustness validation. Failed checks: ['oos_positive_return', 'oos_min_sortino']
2025-07-17 00:14:47,331 - __main__ - INFO -   Robustness score: 0.75, Sortino stability: -0.28
2025-07-17 00:14:47,335 - __main__ - INFO - BTCUSDT_Reversion failed robustness validation. Failed checks: ['oos_positive_return', 'oos_min_sortino']
2025-07-17 00:14:47,335 - __main__ - INFO -   Robustness score: 0.75, Sortino stability: -0.04
2025-07-17 00:14:47,338 - __main__ - INFO - BTCUSDT_Adaptive validated - Comprehensive Score: 5.87
2025-07-17 00:14:47,338 - __main__ - INFO -   Robustness: 1.00, Stability: 0.02, OOS Return: 0.001
2025-07-17 00:14:47,338 - __main__ - INFO - Saving optimization results
2025-07-17 00:14:47,339 - __main__ - ERROR - Critical error in optimization pipeline: 'quantum_score'
2025-07-17 00:14:47,342 - __main__ - INFO - Binance client connection closed
2025-07-17 00:14:47,342 - __main__ - ERROR - Fatal error: 'quantum_score'
2025-07-17 00:16:03,523 - __main__ - INFO - Ultratrader Orchestrator initialized
2025-07-17 00:16:03,523 - __main__ - INFO - Ultratrader Orchestrator initialized
2025-07-17 00:16:03,523 - __main__ - INFO - Starting Ultratrader optimization pipeline
2025-07-17 00:16:04,313 - __main__ - INFO - Binance client initialized successfully
2025-07-17 00:16:04,315 - __main__ - INFO - Optimizing strategies for symbols: ['BTCUSDT']
2025-07-17 00:16:04,315 - __main__ - INFO - Fetching market data for BTCUSDT
2025-07-17 00:16:04,327 - __main__ - INFO - Retrieved 1500 bars for BTCUSDT (15m)
2025-07-17 00:16:04,327 - __main__ - INFO - Retrieved 1500 bars for BTCUSDT (1h)
2025-07-17 00:16:04,327 - __main__ - INFO - Optimizing Trend strategy for BTCUSDT
2025-07-17 00:16:11,240 - __main__ - INFO - BTCUSDT Trend: Best score = 5.423
2025-07-17 00:16:11,240 - __main__ - INFO - Running walk-forward validation for BTCUSDT Trend
2025-07-17 00:16:11,241 - __main__ - INFO - Total data available: 15.6 days (1500 bars)
2025-07-17 00:16:11,241 - __main__ - INFO - Walk-forward parameters: IS=7d, OOS=3d, step=1d
2025-07-17 00:16:14,428 - __main__ - INFO - BTCUSDT Trend: 6 valid WF windows, WF score = 0.600
2025-07-17 00:16:14,428 - __main__ - INFO - Optimizing Reversion strategy for BTCUSDT
2025-07-17 00:16:19,663 - __main__ - INFO - BTCUSDT Reversion: Best score = 7.050
2025-07-17 00:16:19,663 - __main__ - INFO - Running walk-forward validation for BTCUSDT Reversion
2025-07-17 00:16:19,665 - __main__ - INFO - Total data available: 15.6 days (1500 bars)
2025-07-17 00:16:19,665 - __main__ - INFO - Walk-forward parameters: IS=7d, OOS=3d, step=1d
2025-07-17 00:16:22,873 - __main__ - INFO - BTCUSDT Reversion: 6 valid WF windows, WF score = 0.600
2025-07-17 00:16:22,873 - __main__ - INFO - Optimizing Adaptive strategy for BTCUSDT
2025-07-17 00:16:27,378 - __main__ - INFO - BTCUSDT Adaptive: Best score = 6.231
2025-07-17 00:16:27,378 - __main__ - INFO - Running walk-forward validation for BTCUSDT Adaptive
2025-07-17 00:16:27,378 - __main__ - INFO - Total data available: 15.6 days (1500 bars)
2025-07-17 00:16:27,378 - __main__ - INFO - Walk-forward parameters: IS=7d, OOS=3d, step=1d
2025-07-17 00:16:31,579 - __main__ - INFO - BTCUSDT Adaptive: 6 valid WF windows, WF score = 0.646
2025-07-17 00:16:31,580 - __main__ - INFO - Validating and scoring strategies with anti-overfitting criteria
2025-07-17 00:16:31,582 - __main__ - INFO - BTCUSDT_Trend failed robustness validation. Failed checks: ['oos_positive_return', 'oos_min_sortino']
2025-07-17 00:16:31,582 - __main__ - INFO -   Robustness score: 0.75, Sortino stability: -0.28
2025-07-17 00:16:31,584 - __main__ - INFO - BTCUSDT_Reversion failed robustness validation. Failed checks: ['oos_positive_return', 'oos_min_sortino']
2025-07-17 00:16:31,584 - __main__ - INFO -   Robustness score: 0.75, Sortino stability: -0.04
2025-07-17 00:16:31,588 - __main__ - INFO - BTCUSDT_Adaptive validated - Comprehensive Score: 5.87
2025-07-17 00:16:31,588 - __main__ - INFO -   Robustness: 1.00, Stability: 0.02, OOS Return: 0.001
2025-07-17 00:16:31,588 - __main__ - INFO - Saving optimization results
2025-07-17 00:16:31,588 - __main__ - ERROR - Critical error in optimization pipeline: 'walk_forward_score'
2025-07-17 00:16:31,589 - __main__ - INFO - Binance client connection closed
2025-07-17 00:16:31,589 - __main__ - ERROR - Fatal error: 'walk_forward_score'
2025-07-17 00:17:41,756 - __main__ - INFO - Ultratrader Orchestrator initialized
2025-07-17 00:17:41,756 - __main__ - INFO - Ultratrader Orchestrator initialized
2025-07-17 00:17:41,756 - __main__ - INFO - Starting Ultratrader optimization pipeline
2025-07-17 00:17:42,435 - __main__ - INFO - Binance client initialized successfully
2025-07-17 00:17:42,435 - __main__ - INFO - Optimizing strategies for symbols: ['BTCUSDT']
2025-07-17 00:17:42,435 - __main__ - INFO - Fetching market data for BTCUSDT
2025-07-17 00:17:42,448 - __main__ - INFO - Retrieved 1500 bars for BTCUSDT (15m)
2025-07-17 00:17:42,448 - __main__ - INFO - Retrieved 1500 bars for BTCUSDT (1h)
2025-07-17 00:17:42,448 - __main__ - INFO - Optimizing Trend strategy for BTCUSDT
2025-07-17 00:17:49,148 - __main__ - INFO - BTCUSDT Trend: Best score = 5.423
2025-07-17 00:17:49,148 - __main__ - INFO - Running walk-forward validation for BTCUSDT Trend
2025-07-17 00:17:49,148 - __main__ - INFO - Total data available: 15.6 days (1500 bars)
2025-07-17 00:17:49,148 - __main__ - INFO - Walk-forward parameters: IS=7d, OOS=3d, step=1d
2025-07-17 00:17:52,100 - __main__ - INFO - BTCUSDT Trend: 6 valid WF windows, WF score = 0.600
2025-07-17 00:17:52,100 - __main__ - INFO - Optimizing Reversion strategy for BTCUSDT
2025-07-17 00:17:57,178 - __main__ - INFO - BTCUSDT Reversion: Best score = 7.050
2025-07-17 00:17:57,179 - __main__ - INFO - Running walk-forward validation for BTCUSDT Reversion
2025-07-17 00:17:57,179 - __main__ - INFO - Total data available: 15.6 days (1500 bars)
2025-07-17 00:17:57,179 - __main__ - INFO - Walk-forward parameters: IS=7d, OOS=3d, step=1d
2025-07-17 00:18:00,591 - __main__ - INFO - BTCUSDT Reversion: 6 valid WF windows, WF score = 0.600
2025-07-17 00:18:00,591 - __main__ - INFO - Optimizing Adaptive strategy for BTCUSDT
2025-07-17 00:18:05,165 - __main__ - INFO - BTCUSDT Adaptive: Best score = 6.231
2025-07-17 00:18:05,165 - __main__ - INFO - Running walk-forward validation for BTCUSDT Adaptive
2025-07-17 00:18:05,165 - __main__ - INFO - Total data available: 15.6 days (1500 bars)
2025-07-17 00:18:05,166 - __main__ - INFO - Walk-forward parameters: IS=7d, OOS=3d, step=1d
2025-07-17 00:18:09,302 - __main__ - INFO - BTCUSDT Adaptive: 6 valid WF windows, WF score = 0.646
2025-07-17 00:18:09,302 - __main__ - INFO - Validating and scoring strategies with anti-overfitting criteria
2025-07-17 00:18:09,305 - __main__ - INFO - BTCUSDT_Trend failed robustness validation. Failed checks: ['oos_positive_return', 'oos_min_sortino']
2025-07-17 00:18:09,305 - __main__ - INFO -   Robustness score: 0.75, Sortino stability: -0.28
2025-07-17 00:18:09,307 - __main__ - INFO - BTCUSDT_Reversion failed robustness validation. Failed checks: ['oos_positive_return', 'oos_min_sortino']
2025-07-17 00:18:09,307 - __main__ - INFO -   Robustness score: 0.75, Sortino stability: -0.04
2025-07-17 00:18:09,309 - __main__ - INFO - BTCUSDT_Adaptive validated - Comprehensive Score: 5.87
2025-07-17 00:18:09,309 - __main__ - INFO -   Robustness: 1.00, Stability: 0.02, OOS Return: 0.001
2025-07-17 00:18:09,310 - __main__ - INFO - Saving optimization results
2025-07-17 00:18:09,310 - __main__ - INFO - Saved 1 strategies to optimized_strategies.json
2025-07-17 00:18:09,311 - __main__ - ERROR - Critical error in optimization pipeline: 'walk_forward_score'
2025-07-17 00:18:09,314 - __main__ - INFO - Binance client connection closed
2025-07-17 00:18:09,314 - __main__ - ERROR - Fatal error: 'walk_forward_score'
2025-07-17 00:18:39,495 - __main__ - INFO - Ultratrader Orchestrator initialized
2025-07-17 00:18:39,496 - __main__ - INFO - Ultratrader Orchestrator initialized
2025-07-17 00:18:39,497 - __main__ - INFO - Starting Ultratrader optimization pipeline
2025-07-17 00:18:40,181 - __main__ - INFO - Binance client initialized successfully
2025-07-17 00:18:40,181 - __main__ - INFO - Optimizing strategies for symbols: ['BTCUSDT']
2025-07-17 00:18:40,181 - __main__ - INFO - Fetching market data for BTCUSDT
2025-07-17 00:18:40,193 - __main__ - INFO - Retrieved 1500 bars for BTCUSDT (15m)
2025-07-17 00:18:40,193 - __main__ - INFO - Retrieved 1500 bars for BTCUSDT (1h)
2025-07-17 00:18:40,193 - __main__ - INFO - Optimizing Trend strategy for BTCUSDT
2025-07-17 00:18:46,843 - __main__ - INFO - BTCUSDT Trend: Best score = 5.423
2025-07-17 00:18:46,843 - __main__ - INFO - Running walk-forward validation for BTCUSDT Trend
2025-07-17 00:18:46,843 - __main__ - INFO - Total data available: 15.6 days (1500 bars)
2025-07-17 00:18:46,843 - __main__ - INFO - Walk-forward parameters: IS=7d, OOS=3d, step=1d
2025-07-17 00:18:49,923 - __main__ - INFO - BTCUSDT Trend: 6 valid WF windows, WF score = 0.600
2025-07-17 00:18:49,923 - __main__ - INFO - Optimizing Reversion strategy for BTCUSDT
2025-07-17 00:18:55,119 - __main__ - INFO - BTCUSDT Reversion: Best score = 7.050
2025-07-17 00:18:55,120 - __main__ - INFO - Running walk-forward validation for BTCUSDT Reversion
2025-07-17 00:18:55,120 - __main__ - INFO - Total data available: 15.6 days (1500 bars)
2025-07-17 00:18:55,120 - __main__ - INFO - Walk-forward parameters: IS=7d, OOS=3d, step=1d
2025-07-17 00:18:58,256 - __main__ - INFO - BTCUSDT Reversion: 6 valid WF windows, WF score = 0.600
2025-07-17 00:18:58,256 - __main__ - INFO - Optimizing Adaptive strategy for BTCUSDT
2025-07-17 00:19:03,189 - __main__ - INFO - BTCUSDT Adaptive: Best score = 6.231
2025-07-17 00:19:03,189 - __main__ - INFO - Running walk-forward validation for BTCUSDT Adaptive
2025-07-17 00:19:03,189 - __main__ - INFO - Total data available: 15.6 days (1500 bars)
2025-07-17 00:19:03,189 - __main__ - INFO - Walk-forward parameters: IS=7d, OOS=3d, step=1d
2025-07-17 00:19:07,172 - __main__ - INFO - BTCUSDT Adaptive: 6 valid WF windows, WF score = 0.646
2025-07-17 00:19:07,172 - __main__ - INFO - Validating and scoring strategies with anti-overfitting criteria
2025-07-17 00:19:07,174 - __main__ - INFO - BTCUSDT_Trend failed robustness validation. Failed checks: ['oos_positive_return', 'oos_min_sortino']
2025-07-17 00:19:07,174 - __main__ - INFO -   Robustness score: 0.75, Sortino stability: -0.28
2025-07-17 00:19:07,176 - __main__ - INFO - BTCUSDT_Reversion failed robustness validation. Failed checks: ['oos_positive_return', 'oos_min_sortino']
2025-07-17 00:19:07,176 - __main__ - INFO -   Robustness score: 0.75, Sortino stability: -0.04
2025-07-17 00:19:07,179 - __main__ - INFO - BTCUSDT_Adaptive validated - Comprehensive Score: 5.87
2025-07-17 00:19:07,179 - __main__ - INFO -   Robustness: 1.00, Stability: 0.02, OOS Return: 0.001
2025-07-17 00:19:07,179 - __main__ - INFO - Saving optimization results
2025-07-17 00:19:07,179 - __main__ - INFO - Saved 1 strategies to optimized_strategies.json
2025-07-17 00:19:07,181 - __main__ - INFO - Optimization completed successfully with 1 validated strategies
2025-07-17 00:19:07,182 - __main__ - INFO - Binance client connection closed
2025-07-17 00:22:38,304 - __main__ - INFO - Ultratrader Orchestrator initialized
2025-07-17 00:22:38,304 - __main__ - INFO - Ultratrader Orchestrator initialized
2025-07-17 00:22:38,304 - __main__ - INFO - Starting Ultratrader optimization pipeline
2025-07-17 00:22:38,987 - __main__ - INFO - Binance client initialized successfully
2025-07-17 00:22:38,987 - __main__ - INFO - Optimizing strategies for symbols: ['BTCUSDT']
2025-07-17 00:22:38,987 - __main__ - INFO - Fetching market data for BTCUSDT
2025-07-17 00:22:38,999 - __main__ - INFO - Retrieved 1500 bars for BTCUSDT (15m)
2025-07-17 00:22:38,999 - __main__ - INFO - Retrieved 1500 bars for BTCUSDT (1h)
2025-07-17 00:22:39,001 - __main__ - INFO - Optimizing Trend strategy for BTCUSDT
2025-07-17 00:22:45,788 - __main__ - INFO - BTCUSDT Trend: Best score = 5.423
2025-07-17 00:22:45,788 - __main__ - INFO - Running walk-forward validation for BTCUSDT Trend
2025-07-17 00:22:45,788 - __main__ - INFO - Total data available: 15.6 days (1500 bars)
2025-07-17 00:22:45,788 - __main__ - INFO - Walk-forward parameters: IS=7d, OOS=3d, step=1d
2025-07-17 00:22:48,857 - __main__ - INFO - BTCUSDT Trend: 6 valid WF windows, WF score = 0.600
2025-07-17 00:22:48,857 - __main__ - INFO - Optimizing Reversion strategy for BTCUSDT
2025-07-17 00:22:54,018 - __main__ - INFO - BTCUSDT Reversion: Best score = 7.050
2025-07-17 00:22:54,019 - __main__ - INFO - Running walk-forward validation for BTCUSDT Reversion
2025-07-17 00:22:54,019 - __main__ - INFO - Total data available: 15.6 days (1500 bars)
2025-07-17 00:22:54,019 - __main__ - INFO - Walk-forward parameters: IS=7d, OOS=3d, step=1d
2025-07-17 00:22:57,152 - __main__ - INFO - BTCUSDT Reversion: 6 valid WF windows, WF score = 0.600
2025-07-17 00:22:57,152 - __main__ - INFO - Optimizing Adaptive strategy for BTCUSDT
2025-07-17 00:23:01,603 - __main__ - INFO - BTCUSDT Adaptive: Best score = 6.231
2025-07-17 00:23:01,603 - __main__ - INFO - Running walk-forward validation for BTCUSDT Adaptive
2025-07-17 00:23:01,605 - __main__ - INFO - Total data available: 15.6 days (1500 bars)
2025-07-17 00:23:01,605 - __main__ - INFO - Walk-forward parameters: IS=7d, OOS=3d, step=1d
2025-07-17 00:23:06,390 - __main__ - INFO - BTCUSDT Adaptive: 6 valid WF windows, WF score = 0.646
2025-07-17 00:23:06,390 - __main__ - INFO - Validating and scoring strategies with anti-overfitting criteria
2025-07-17 00:23:06,392 - __main__ - INFO - BTCUSDT_Trend failed robustness validation. Failed checks: ['oos_positive_return', 'oos_min_sortino']
2025-07-17 00:23:06,393 - __main__ - INFO -   Robustness score: 0.75, Sortino stability: -0.28
2025-07-17 00:23:06,395 - __main__ - INFO - BTCUSDT_Reversion failed robustness validation. Failed checks: ['oos_positive_return', 'oos_min_sortino']
2025-07-17 00:23:06,395 - __main__ - INFO -   Robustness score: 0.75, Sortino stability: -0.04
2025-07-17 00:23:06,398 - __main__ - INFO - BTCUSDT_Adaptive validated - Comprehensive Score: 5.87
2025-07-17 00:23:06,398 - __main__ - INFO -   Robustness: 1.00, Stability: 0.02, OOS Return: 0.001
2025-07-17 00:23:06,398 - __main__ - INFO - Saving optimization results
2025-07-17 00:23:06,399 - __main__ - INFO - Saved 1 strategies to optimized_strategies.json
2025-07-17 00:23:06,400 - __main__ - INFO - Optimization completed successfully with 1 validated strategies
2025-07-17 00:23:06,400 - __main__ - INFO - Binance client connection closed
2025-07-17 00:29:35,717 - __main__ - INFO - Ultratrader Orchestrator initialized
2025-07-17 00:29:35,717 - __main__ - INFO - Ultratrader Orchestrator initialized
2025-07-17 00:29:35,717 - __main__ - INFO - Starting Ultratrader optimization pipeline
2025-07-17 00:29:36,405 - __main__ - INFO - Binance client initialized successfully
2025-07-17 00:29:36,405 - __main__ - INFO - Optimizing strategies for symbols: ['BTCUSDT']
2025-07-17 00:29:36,405 - __main__ - INFO - Fetching market data for BTCUSDT
2025-07-17 00:29:36,417 - __main__ - INFO - Retrieved 1500 bars for BTCUSDT (15m)
2025-07-17 00:29:36,417 - __main__ - INFO - Retrieved 1500 bars for BTCUSDT (1h)
2025-07-17 00:29:36,417 - __main__ - INFO - Optimizing Trend strategy for BTCUSDT
2025-07-17 00:29:43,186 - __main__ - ERROR - Error optimizing BTCUSDT Trend: No trials are completed yet.
2025-07-17 00:29:43,186 - __main__ - INFO - Running walk-forward validation for BTCUSDT Trend
2025-07-17 00:29:43,186 - __main__ - INFO - Total data available: 15.6 days (1500 bars)
2025-07-17 00:29:43,186 - __main__ - INFO - Walk-forward parameters: IS=7d, OOS=3d, step=1d
2025-07-17 00:29:46,261 - __main__ - INFO - BTCUSDT Trend: 6 valid WF windows, WF score = 0.694
2025-07-17 00:29:46,261 - __main__ - INFO - Optimizing Reversion strategy for BTCUSDT
2025-07-17 00:29:51,346 - __main__ - INFO - BTCUSDT Reversion: Best score = 5.780
2025-07-17 00:29:51,346 - __main__ - INFO - Running walk-forward validation for BTCUSDT Reversion
2025-07-17 00:29:51,346 - __main__ - INFO - Total data available: 15.6 days (1500 bars)
2025-07-17 00:29:51,347 - __main__ - INFO - Walk-forward parameters: IS=7d, OOS=3d, step=1d
2025-07-17 00:29:54,601 - __main__ - INFO - BTCUSDT Reversion: 6 valid WF windows, WF score = 0.600
2025-07-17 00:29:54,601 - __main__ - INFO - Optimizing Adaptive strategy for BTCUSDT
2025-07-17 00:29:58,978 - __main__ - INFO - BTCUSDT Adaptive: Best score = 5.778
2025-07-17 00:29:58,979 - __main__ - INFO - Running walk-forward validation for BTCUSDT Adaptive
2025-07-17 00:29:58,979 - __main__ - INFO - Total data available: 15.6 days (1500 bars)
2025-07-17 00:29:58,979 - __main__ - INFO - Walk-forward parameters: IS=7d, OOS=3d, step=1d
2025-07-17 00:30:03,389 - __main__ - INFO - BTCUSDT Adaptive: 6 valid WF windows, WF score = 0.600
2025-07-17 00:30:03,389 - __main__ - INFO - Validating and scoring strategies with anti-overfitting criteria
2025-07-17 00:30:03,391 - __main__ - INFO - BTCUSDT_Reversion failed robustness validation. Failed checks: ['oos_positive_return', 'oos_min_sortino']
2025-07-17 00:30:03,391 - __main__ - INFO -   Robustness score: 0.75, Sortino stability: -0.34
2025-07-17 00:30:03,393 - __main__ - INFO - BTCUSDT_Adaptive failed robustness validation. Failed checks: ['oos_positive_return', 'oos_min_sortino']
2025-07-17 00:30:03,393 - __main__ - INFO -   Robustness score: 0.75, Sortino stability: -0.52
2025-07-17 00:30:03,394 - __main__ - INFO - Saving optimization results
2025-07-17 00:30:03,394 - __main__ - WARNING - No validated strategies to save
2025-07-17 00:30:03,394 - __main__ - WARNING - Optimization completed but no strategies passed validation
2025-07-17 00:30:03,394 - __main__ - INFO - Binance client connection closed
2025-07-17 00:31:20,659 - __main__ - INFO - Ultratrader Orchestrator initialized
2025-07-17 00:31:20,659 - __main__ - INFO - Ultratrader Orchestrator initialized
2025-07-17 00:31:20,659 - __main__ - INFO - Starting Ultratrader optimization pipeline
2025-07-17 00:31:21,378 - __main__ - INFO - Binance client initialized successfully
2025-07-17 00:31:21,379 - __main__ - INFO - Optimizing strategies for symbols: ['BTCUSDT']
2025-07-17 00:31:21,379 - __main__ - INFO - Fetching market data for BTCUSDT
2025-07-17 00:31:21,392 - __main__ - INFO - Retrieved 1500 bars for BTCUSDT (15m)
2025-07-17 00:31:21,392 - __main__ - INFO - Retrieved 1500 bars for BTCUSDT (1h)
2025-07-17 00:31:21,392 - __main__ - INFO - Optimizing Trend strategy for BTCUSDT
2025-07-17 00:31:28,404 - __main__ - ERROR - Error optimizing BTCUSDT Trend: No trials are completed yet.
2025-07-17 00:31:28,404 - __main__ - INFO - Running walk-forward validation for BTCUSDT Trend
2025-07-17 00:31:28,404 - __main__ - INFO - Total data available: 15.6 days (1500 bars)
2025-07-17 00:31:28,404 - __main__ - INFO - Walk-forward parameters: IS=7d, OOS=3d, step=1d
2025-07-17 00:31:31,470 - __main__ - INFO - BTCUSDT Trend: 6 valid WF windows, WF score = 0.711
2025-07-17 00:31:31,470 - __main__ - INFO - Optimizing Reversion strategy for BTCUSDT
2025-07-17 00:31:36,669 - __main__ - INFO - BTCUSDT Reversion: Best score = 5.780
2025-07-17 00:31:36,670 - __main__ - INFO - Running walk-forward validation for BTCUSDT Reversion
2025-07-17 00:31:36,670 - __main__ - INFO - Total data available: 15.6 days (1500 bars)
2025-07-17 00:31:36,670 - __main__ - INFO - Walk-forward parameters: IS=7d, OOS=3d, step=1d
2025-07-17 00:31:40,351 - __main__ - INFO - BTCUSDT Reversion: 6 valid WF windows, WF score = 0.600
2025-07-17 00:31:40,352 - __main__ - INFO - Optimizing Adaptive strategy for BTCUSDT
2025-07-17 00:31:44,923 - __main__ - INFO - BTCUSDT Adaptive: Best score = 5.778
2025-07-17 00:31:44,923 - __main__ - INFO - Running walk-forward validation for BTCUSDT Adaptive
2025-07-17 00:31:44,923 - __main__ - INFO - Total data available: 15.6 days (1500 bars)
2025-07-17 00:31:44,923 - __main__ - INFO - Walk-forward parameters: IS=7d, OOS=3d, step=1d
2025-07-17 00:31:49,358 - __main__ - INFO - BTCUSDT Adaptive: 6 valid WF windows, WF score = 0.600
2025-07-17 00:31:49,358 - __main__ - INFO - Validating and scoring strategies with anti-overfitting criteria
2025-07-17 00:31:49,361 - __main__ - INFO - BTCUSDT_Reversion failed robustness validation. Failed checks: ['oos_positive_return', 'oos_min_sortino']
2025-07-17 00:31:49,361 - __main__ - INFO -   Robustness score: 0.75, Sortino stability: -0.63
2025-07-17 00:31:49,365 - __main__ - INFO - BTCUSDT_Adaptive failed robustness validation. Failed checks: ['oos_positive_return', 'oos_min_sortino']
2025-07-17 00:31:49,365 - __main__ - INFO -   Robustness score: 0.75, Sortino stability: -0.51
2025-07-17 00:31:49,366 - __main__ - INFO - Saving optimization results
2025-07-17 00:31:49,366 - __main__ - WARNING - No validated strategies to save
2025-07-17 00:31:49,366 - __main__ - WARNING - Optimization completed but no strategies passed validation
2025-07-17 00:31:49,366 - __main__ - INFO - Binance client connection closed
2025-07-17 00:32:49,116 - __main__ - INFO - Ultratrader Orchestrator initialized
2025-07-17 00:32:49,116 - __main__ - INFO - Ultratrader Orchestrator initialized
2025-07-17 00:32:49,116 - __main__ - INFO - Starting Ultratrader optimization pipeline
2025-07-17 00:32:49,796 - __main__ - INFO - Binance client initialized successfully
2025-07-17 00:32:49,796 - __main__ - INFO - Optimizing strategies for symbols: ['BTCUSDT']
2025-07-17 00:32:49,796 - __main__ - INFO - Fetching market data for BTCUSDT
2025-07-17 00:32:49,810 - __main__ - INFO - Retrieved 1500 bars for BTCUSDT (15m)
2025-07-17 00:32:49,810 - __main__ - INFO - Retrieved 1500 bars for BTCUSDT (1h)
2025-07-17 00:32:49,811 - __main__ - INFO - Optimizing Trend strategy for BTCUSDT
2025-07-17 00:32:56,545 - __main__ - ERROR - Error optimizing BTCUSDT Trend: No trials are completed yet.
2025-07-17 00:32:56,545 - __main__ - INFO - Running walk-forward validation for BTCUSDT Trend
2025-07-17 00:32:56,545 - __main__ - INFO - Total data available: 15.6 days (1500 bars)
2025-07-17 00:32:56,545 - __main__ - INFO - Walk-forward parameters: IS=7d, OOS=3d, step=1d
2025-07-17 00:32:59,658 - __main__ - INFO - BTCUSDT Trend: 6 valid WF windows, WF score = 0.711
2025-07-17 00:32:59,658 - __main__ - INFO - Optimizing Reversion strategy for BTCUSDT
2025-07-17 00:33:04,972 - __main__ - INFO - BTCUSDT Reversion: Best score = 5.780
2025-07-17 00:33:04,973 - __main__ - INFO - Running walk-forward validation for BTCUSDT Reversion
2025-07-17 00:33:04,973 - __main__ - INFO - Total data available: 15.6 days (1500 bars)
2025-07-17 00:33:04,973 - __main__ - INFO - Walk-forward parameters: IS=7d, OOS=3d, step=1d
2025-07-17 00:33:08,093 - __main__ - INFO - BTCUSDT Reversion: 6 valid WF windows, WF score = 0.600
2025-07-17 00:33:08,093 - __main__ - INFO - Optimizing Adaptive strategy for BTCUSDT
2025-07-17 00:33:12,644 - __main__ - INFO - BTCUSDT Adaptive: Best score = 5.778
2025-07-17 00:33:12,644 - __main__ - INFO - Running walk-forward validation for BTCUSDT Adaptive
2025-07-17 00:33:12,644 - __main__ - INFO - Total data available: 15.6 days (1500 bars)
2025-07-17 00:33:12,644 - __main__ - INFO - Walk-forward parameters: IS=7d, OOS=3d, step=1d
2025-07-17 00:33:16,704 - __main__ - INFO - BTCUSDT Adaptive: 6 valid WF windows, WF score = 0.781
2025-07-17 00:33:16,704 - __main__ - INFO - Validating and scoring strategies with anti-overfitting criteria
2025-07-17 00:33:16,708 - __main__ - INFO - BTCUSDT_Reversion failed robustness validation. Failed checks: ['oos_positive_return', 'oos_min_sortino']
2025-07-17 00:33:16,708 - __main__ - INFO -   Robustness score: 0.75, Sortino stability: -0.63
2025-07-17 00:33:16,710 - __main__ - INFO - BTCUSDT_Adaptive validated - Comprehensive Score: 5.62
2025-07-17 00:33:16,710 - __main__ - INFO -   Robustness: 1.00, Stability: 0.10, OOS Return: 0.001
2025-07-17 00:33:16,710 - __main__ - INFO - Saving optimization results
2025-07-17 00:33:16,711 - __main__ - INFO - Saved 1 strategies to optimized_strategies.json
2025-07-17 00:33:16,711 - __main__ - INFO - Optimization completed successfully with 1 validated strategies
2025-07-17 00:33:16,712 - __main__ - INFO - Binance client connection closed
2025-07-17 00:37:52,145 - __main__ - INFO - Ultratrader Orchestrator initialized
2025-07-17 00:37:52,145 - __main__ - INFO - Ultratrader Orchestrator initialized
2025-07-17 00:37:52,146 - __main__ - INFO - Starting Ultratrader optimization pipeline
2025-07-17 00:37:52,833 - __main__ - INFO - Binance client initialized successfully
2025-07-17 00:37:52,833 - __main__ - INFO - Optimizing strategies for symbols: ['BTCUSDT']
2025-07-17 00:37:52,833 - __main__ - INFO - Fetching market data for BTCUSDT
2025-07-17 00:37:52,843 - __main__ - INFO - Retrieved 1500 bars for BTCUSDT (15m)
2025-07-17 00:37:52,843 - __main__ - INFO - Retrieved 1500 bars for BTCUSDT (1h)
2025-07-17 00:37:52,843 - __main__ - INFO - Optimizing Trend strategy for BTCUSDT
2025-07-17 00:38:01,609 - __main__ - INFO - BTCUSDT Trend: Best score = 1.000
2025-07-17 00:38:01,610 - __main__ - INFO - Running walk-forward validation for BTCUSDT Trend
2025-07-17 00:38:01,610 - __main__ - INFO - Total data available: 15.6 days (1500 bars)
2025-07-17 00:38:01,610 - __main__ - INFO - Walk-forward parameters: IS=7d, OOS=3d, step=1d
2025-07-17 00:38:04,686 - __main__ - INFO - BTCUSDT Trend: 6 valid WF windows, WF score = 0.600
2025-07-17 00:38:04,686 - __main__ - INFO - Optimizing Reversion strategy for BTCUSDT
2025-07-17 00:38:09,633 - __main__ - INFO - BTCUSDT Reversion: Best score = 5.778
2025-07-17 00:38:09,633 - __main__ - INFO - Running walk-forward validation for BTCUSDT Reversion
2025-07-17 00:38:09,633 - __main__ - INFO - Total data available: 15.6 days (1500 bars)
2025-07-17 00:38:09,633 - __main__ - INFO - Walk-forward parameters: IS=7d, OOS=3d, step=1d
2025-07-17 00:38:12,710 - __main__ - INFO - BTCUSDT Reversion: 6 valid WF windows, WF score = 0.600
2025-07-17 00:38:12,710 - __main__ - INFO - Optimizing Adaptive strategy for BTCUSDT
2025-07-17 00:38:16,926 - __main__ - INFO - BTCUSDT Adaptive: Best score = 5.777
2025-07-17 00:38:16,927 - __main__ - INFO - Running walk-forward validation for BTCUSDT Adaptive
2025-07-17 00:38:16,927 - __main__ - INFO - Total data available: 15.6 days (1500 bars)
2025-07-17 00:38:16,927 - __main__ - INFO - Walk-forward parameters: IS=7d, OOS=3d, step=1d
2025-07-17 00:38:21,633 - __main__ - INFO - BTCUSDT Adaptive: 6 valid WF windows, WF score = 0.600
2025-07-17 00:38:21,634 - __main__ - INFO - Validating and scoring strategies with anti-overfitting criteria
2025-07-17 00:38:21,635 - __main__ - INFO - BTCUSDT_Trend failed robustness validation. Failed checks: ['min_sortino', 'min_profit_factor', 'min_win_rate', 'oos_positive_return', 'oos_min_sortino']
2025-07-17 00:38:21,635 - __main__ - INFO -   Robustness score: 0.38, Sortino stability: 0.00
2025-07-17 00:38:21,637 - __main__ - INFO - BTCUSDT_Reversion failed robustness validation. Failed checks: ['oos_positive_return', 'oos_min_sortino']
2025-07-17 00:38:21,637 - __main__ - INFO -   Robustness score: 0.75, Sortino stability: -1.63
2025-07-17 00:38:21,639 - __main__ - INFO - BTCUSDT_Adaptive failed robustness validation. Failed checks: ['oos_positive_return', 'oos_min_sortino']
2025-07-17 00:38:21,640 - __main__ - INFO -   Robustness score: 0.75, Sortino stability: -0.96
2025-07-17 00:38:21,640 - __main__ - INFO - Saving optimization results
2025-07-17 00:38:21,640 - __main__ - WARNING - No validated strategies to save
2025-07-17 00:38:21,640 - __main__ - WARNING - Optimization completed but no strategies passed validation
2025-07-17 00:38:21,640 - __main__ - INFO - Binance client connection closed
2025-07-17 00:48:16,837 - __main__ - INFO - Ultratrader Orchestrator initialized
2025-07-17 00:48:16,837 - __main__ - INFO - Ultratrader Orchestrator initialized
2025-07-17 00:48:16,837 - __main__ - INFO - Starting Ultratrader optimization pipeline
2025-07-17 00:48:17,537 - __main__ - INFO - Binance client initialized successfully
2025-07-17 00:48:17,537 - __main__ - INFO - Optimizing strategies for symbols: ['BTCUSDT']
2025-07-17 00:48:17,537 - __main__ - INFO - Fetching market data for BTCUSDT
2025-07-17 00:48:17,579 - __main__ - INFO - Retrieved 1500 bars for BTCUSDT (15m)
2025-07-17 00:48:17,579 - __main__ - INFO - Retrieved 1500 bars for BTCUSDT (1h)
2025-07-17 00:48:17,582 - __main__ - INFO - Running parallel optimization for BTCUSDT
2025-07-17 00:48:17,582 - __main__ - INFO - Optimizing Trend strategy for BTCUSDT
2025-07-17 00:48:24,434 - __main__ - INFO - BTCUSDT Trend: Best score = 1.000
2025-07-17 00:48:24,434 - __main__ - INFO - Optimizing Reversion strategy for BTCUSDT
2025-07-17 00:48:29,064 - __main__ - INFO - BTCUSDT Reversion: Best score = 5.798
2025-07-17 00:48:29,065 - __main__ - INFO - Optimizing Adaptive strategy for BTCUSDT
2025-07-17 00:48:33,005 - __main__ - INFO - BTCUSDT Adaptive: Best score = 5.775
2025-07-17 00:48:33,006 - __main__ - INFO - Running parallel walk-forward validation for BTCUSDT
2025-07-17 00:48:33,006 - __main__ - INFO - Running walk-forward validation for BTCUSDT Trend
2025-07-17 00:48:33,006 - __main__ - INFO - Total data available: 15.6 days (1500 bars)
2025-07-17 00:48:33,006 - __main__ - INFO - Walk-forward parameters: IS=7d, OOS=3d, step=1d
2025-07-17 00:48:36,002 - __main__ - INFO - BTCUSDT Trend: 6 valid WF windows, WF score = 0.600
2025-07-17 00:48:36,002 - __main__ - INFO - Running walk-forward validation for BTCUSDT Reversion
2025-07-17 00:48:36,003 - __main__ - INFO - Total data available: 15.6 days (1500 bars)
2025-07-17 00:48:36,003 - __main__ - INFO - Walk-forward parameters: IS=7d, OOS=3d, step=1d
2025-07-17 00:48:38,895 - __main__ - INFO - BTCUSDT Reversion: 6 valid WF windows, WF score = 0.600
2025-07-17 00:48:38,896 - __main__ - INFO - Running walk-forward validation for BTCUSDT Adaptive
2025-07-17 00:48:38,896 - __main__ - INFO - Total data available: 15.6 days (1500 bars)
2025-07-17 00:48:38,896 - __main__ - INFO - Walk-forward parameters: IS=7d, OOS=3d, step=1d
2025-07-17 00:48:42,991 - __main__ - INFO - BTCUSDT Adaptive: 6 valid WF windows, WF score = 0.600
2025-07-17 00:48:42,991 - __main__ - INFO - Validating and scoring strategies with anti-overfitting criteria
2025-07-17 00:48:42,992 - __main__ - INFO - BTCUSDT_Trend failed robustness validation. Failed checks: ['min_sortino', 'min_profit_factor', 'min_win_rate', 'oos_positive_return', 'oos_min_sortino']
2025-07-17 00:48:42,993 - __main__ - INFO -   Robustness score: 0.38, Sortino stability: 0.00
2025-07-17 00:48:42,995 - __main__ - INFO - BTCUSDT_Reversion failed robustness validation. Failed checks: ['oos_positive_return', 'oos_min_sortino']
2025-07-17 00:48:42,995 - __main__ - INFO -   Robustness score: 0.75, Sortino stability: -1.42
2025-07-17 00:48:42,997 - __main__ - INFO - BTCUSDT_Adaptive failed robustness validation. Failed checks: ['oos_positive_return', 'oos_min_sortino']
2025-07-17 00:48:42,997 - __main__ - INFO -   Robustness score: 0.75, Sortino stability: -1.24
2025-07-17 00:48:42,997 - __main__ - INFO - Saving optimization results
2025-07-17 00:48:42,997 - __main__ - WARNING - No validated strategies to save
2025-07-17 00:48:42,997 - __main__ - WARNING - Optimization completed but no strategies passed validation
2025-07-17 00:48:42,998 - __main__ - INFO - Pipeline completed in 26.16 seconds
2025-07-17 00:48:42,998 - __main__ - INFO - Memory usage: 245.5 MB (1.5%)
2025-07-17 00:48:42,999 - __main__ - INFO - Binance client connection closed
2025-07-17 00:48:52,516 - __main__ - INFO - Ultratrader Orchestrator initialized
2025-07-17 00:48:52,516 - __main__ - INFO - Ultratrader Orchestrator initialized
2025-07-17 00:48:52,516 - __main__ - INFO - Starting Ultratrader optimization pipeline
2025-07-17 00:48:53,194 - __main__ - INFO - Binance client initialized successfully
2025-07-17 00:48:53,194 - __main__ - INFO - Optimizing strategies for symbols: ['BTCUSDT']
2025-07-17 00:48:53,194 - __main__ - INFO - Fetching market data for BTCUSDT
2025-07-17 00:48:53,207 - __main__ - INFO - Retrieved 1500 bars for BTCUSDT (15m)
2025-07-17 00:48:53,207 - __main__ - INFO - Retrieved 1500 bars for BTCUSDT (1h)
2025-07-17 00:48:53,210 - __main__ - INFO - Running parallel optimization for BTCUSDT
2025-07-17 00:48:53,210 - __main__ - INFO - Optimizing Trend strategy for BTCUSDT
2025-07-17 00:48:58,883 - __main__ - INFO - BTCUSDT Trend: Best score = 1.000
2025-07-17 00:48:58,883 - __main__ - INFO - Optimizing Reversion strategy for BTCUSDT
2025-07-17 00:49:04,518 - __main__ - INFO - BTCUSDT Reversion: Best score = 5.798
2025-07-17 00:49:04,519 - __main__ - INFO - Optimizing Adaptive strategy for BTCUSDT
2025-07-17 00:49:08,607 - __main__ - INFO - BTCUSDT Adaptive: Best score = 5.775
2025-07-17 00:49:08,608 - __main__ - INFO - Running parallel walk-forward validation for BTCUSDT
2025-07-17 00:49:08,608 - __main__ - INFO - Running walk-forward validation for BTCUSDT Trend
2025-07-17 00:49:08,608 - __main__ - INFO - Total data available: 15.6 days (1500 bars)
2025-07-17 00:49:08,608 - __main__ - INFO - Walk-forward parameters: IS=7d, OOS=3d, step=1d
2025-07-17 00:49:11,611 - __main__ - INFO - BTCUSDT Trend: 6 valid WF windows, WF score = 0.600
2025-07-17 00:49:11,612 - __main__ - INFO - Running walk-forward validation for BTCUSDT Reversion
2025-07-17 00:49:11,612 - __main__ - INFO - Total data available: 15.6 days (1500 bars)
2025-07-17 00:49:11,612 - __main__ - INFO - Walk-forward parameters: IS=7d, OOS=3d, step=1d
2025-07-17 00:49:14,676 - __main__ - INFO - BTCUSDT Reversion: 6 valid WF windows, WF score = 0.600
2025-07-17 00:49:14,676 - __main__ - INFO - Running walk-forward validation for BTCUSDT Adaptive
2025-07-17 00:49:14,676 - __main__ - INFO - Total data available: 15.6 days (1500 bars)
2025-07-17 00:49:14,676 - __main__ - INFO - Walk-forward parameters: IS=7d, OOS=3d, step=1d
2025-07-17 00:49:18,664 - __main__ - INFO - BTCUSDT Adaptive: 6 valid WF windows, WF score = 0.600
2025-07-17 00:49:18,665 - __main__ - INFO - Validating and scoring strategies with anti-overfitting criteria
2025-07-17 00:49:18,666 - __main__ - INFO - BTCUSDT_Trend failed robustness validation. Failed checks: ['min_sortino', 'min_profit_factor', 'min_win_rate', 'oos_positive_return', 'oos_min_sortino']
2025-07-17 00:49:18,666 - __main__ - INFO -   Robustness score: 0.38, Sortino stability: 0.00
2025-07-17 00:49:18,668 - __main__ - INFO - BTCUSDT_Reversion failed robustness validation. Failed checks: ['oos_positive_return', 'oos_min_sortino']
2025-07-17 00:49:18,669 - __main__ - INFO -   Robustness score: 0.75, Sortino stability: -1.42
2025-07-17 00:49:18,670 - __main__ - INFO - BTCUSDT_Adaptive failed robustness validation. Failed checks: ['oos_positive_return', 'oos_min_sortino']
2025-07-17 00:49:18,670 - __main__ - INFO -   Robustness score: 0.75, Sortino stability: -1.24
2025-07-17 00:49:18,670 - __main__ - INFO - Saving optimization results
2025-07-17 00:49:18,672 - __main__ - WARNING - No validated strategies to save
2025-07-17 00:49:18,672 - __main__ - WARNING - Optimization completed but no strategies passed validation
2025-07-17 00:49:18,672 - __main__ - INFO - Pipeline completed in 26.16 seconds
2025-07-17 00:49:18,672 - __main__ - INFO - Memory usage: 229.9 MB (1.4%)
2025-07-17 00:49:18,672 - __main__ - INFO - Binance client connection closed
2025-07-17 00:59:52,915 - INFO - Ultratrader Orchestrator initialized
2025-07-17 00:59:52,915 - INFO - Starting Ultratrader optimization pipeline
2025-07-17 00:59:53,617 - INFO - Binance client initialized successfully
2025-07-17 00:59:53,617 - INFO - Fetching market data for BTCUSDT
2025-07-17 00:59:53,647 - INFO - Retrieved 1500 bars for BTCUSDT (15m)
2025-07-17 00:59:53,647 - INFO - Retrieved 1500 bars for BTCUSDT (1h)
2025-07-17 01:00:01,734 - INFO - Validating and scoring strategies with anti-overfitting criteria
2025-07-17 01:00:01,735 - INFO - Saving optimization results
2025-07-17 01:00:01,736 - INFO - Saved 3 strategies to optimized_strategies.json
2025-07-17 01:00:01,738 - INFO - Pipeline completed in 8.82 seconds
2025-07-17 01:00:01,738 - INFO - Memory usage: 242.5 MB (1.5%)
2025-07-17 01:00:01,738 - INFO - Binance client connection closed
2025-07-17 01:00:09,613 - INFO - Ultratrader Orchestrator initialized
2025-07-17 01:00:09,613 - INFO - Ultratrader Orchestrator initialized
2025-07-17 01:00:09,613 - INFO - Starting Ultratrader optimization pipeline
2025-07-17 01:00:10,399 - INFO - Binance client initialized successfully
2025-07-17 01:00:10,399 - INFO - Fetching market data for BTCUSDT
2025-07-17 01:00:10,411 - INFO - Retrieved 1500 bars for BTCUSDT (15m)
2025-07-17 01:00:10,411 - INFO - Retrieved 1500 bars for BTCUSDT (1h)
2025-07-17 01:00:16,901 - INFO - Validating and scoring strategies with anti-overfitting criteria
2025-07-17 01:00:16,903 - INFO - Saving optimization results
2025-07-17 01:00:16,904 - INFO - Saved 3 strategies to optimized_strategies.json
2025-07-17 01:00:16,906 - INFO - Pipeline completed in 7.29 seconds
2025-07-17 01:00:16,906 - INFO - Memory usage: 228.7 MB (1.4%)
2025-07-17 01:00:16,906 - INFO - Binance client connection closed
