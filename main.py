#!/usr/bin/env python3
"""
Ultratrader Optimization System - Main Entry Point

A modular, high-performance cryptocurrency trading strategy optimization system
with advanced risk management and comprehensive backtesting capabilities.

Usage:
    python main.py [options]
"""

import os
import sys
import asyncio
import logging
import traceback
from pathlib import Path
from datetime import datetime
from typing import Dict, Any, Optional

# Windows compatibility - must be set before any asyncio operations
if os.name == 'nt':
    asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())

# Add project root to Python path
sys.path.insert(0, str(Path(__file__).parent))

# Core imports
from core.config import config
from core.data_manager import DataManager
from core.backtest_engine import BacktestEngine
from core.performance_config import performance_monitor, enable_performance_optimizations

# Utilities and modules
from utils.indicators import IndicatorCalculator
from utils.scoring import ScoringSystem
from risk.risk_manager import RiskManager
from optimization.optimizer import OptunaOptimizer, WalkForwardAnalyzer

# External dependencies
from binance import AsyncClient
from binance.exceptions import BinanceAPIException
import pandas as pd
import json

# Setup optimized logging
def setup_optimized_logging():
    """Setup logging with reduced verbosity during optimization."""
    # Create custom formatter that's more concise
    formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')

    # File handler for detailed logs
    file_handler = logging.FileHandler('ultratrader_modular.log')
    file_handler.setLevel(logging.INFO)
    file_handler.setFormatter(formatter)

    # Console handler with higher threshold for less spam
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(logging.WARNING)  # Only warnings and errors to console
    console_handler.setFormatter(formatter)

    # Configure root logger
    logging.basicConfig(
        level=logging.INFO,
        handlers=[file_handler, console_handler]
    )

    # Suppress noisy third-party loggers
    logging.getLogger('optuna').setLevel(logging.ERROR)
    logging.getLogger('urllib3').setLevel(logging.ERROR)
    logging.getLogger('binance').setLevel(logging.ERROR)

setup_optimized_logging()

logger = logging.getLogger(__name__)


class UltraderOrchestrator:
    """
    Main orchestrator for the Ultratrader optimization system.
    
    Coordinates all components to execute the complete optimization pipeline:
    1. Data acquisition and preprocessing
    2. Strategy optimization across multiple modes
    3. Walk-forward validation
    4. Performance evaluation and ranking
    5. Results saving and reporting
    """
    
    def __init__(self):
        self.client: Optional[AsyncClient] = None
        self.data_manager: Optional[DataManager] = None
        self.backtest_engine = BacktestEngine()
        self.risk_manager = RiskManager()
        self.optimizer = OptunaOptimizer(self.backtest_engine)
        self.walk_forward_analyzer = WalkForwardAnalyzer(self.optimizer)
        
        logger.info("Ultratrader Orchestrator initialized")
    
    async def initialize_binance_client(self):
        """Initialize Binance API client."""
        try:
            api_key, api_secret = config.get_binance_credentials()
            self.client = await AsyncClient.create(api_key, api_secret)
            self.data_manager = DataManager(self.client)
            logger.info("Binance client initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize Binance client: {e}")
            raise
    
    async def get_market_data(self, symbol: str) -> tuple[pd.DataFrame, pd.DataFrame]:
        """
        Fetch and prepare market data for optimization.
        
        Args:
            symbol: Trading symbol (e.g., 'BTCUSDT')
            
        Returns:
            Tuple of (main_timeframe_data, mta_timeframe_data)
        """
        logger.info(f"Fetching market data for {symbol}")
        
        try:
            # Fetch main timeframe data
            main_df = await self.data_manager.get_historical_data(
                symbol=symbol,
                timeframe=config.market.timeframe,
                history_days=config.data.history_days
            )
            
            # Fetch MTA timeframe data
            mta_df = await self.data_manager.get_historical_data(
                symbol=symbol,
                timeframe=config.market.mta_timeframe,
                history_days=config.data.history_days
            )
            
            if main_df.empty:
                raise ValueError(f"No data retrieved for {symbol}")
            
            logger.info(f"Retrieved {len(main_df)} bars for {symbol} ({config.market.timeframe})")
            logger.info(f"Retrieved {len(mta_df)} bars for {symbol} ({config.market.mta_timeframe})")
            
            return main_df, mta_df
            
        except Exception as e:
            logger.error(f"Error fetching data for {symbol}: {e}")
            raise
    
    async def optimize_single_strategy(
        self,
        symbol: str,
        strategy_mode: str,
        main_df: pd.DataFrame,
        mta_df: pd.DataFrame
    ) -> Optional[Dict[str, Any]]:
        """
        Optimize a single strategy for a given symbol and mode.

        Args:
            symbol: Trading symbol
            strategy_mode: Strategy mode ('Trend', 'Reversion', 'Adaptive')
            main_df: Main timeframe data
            mta_df: MTA timeframe data

        Returns:
            Optimization results or None if failed
        """
        print(f"🔄 Optimizing {strategy_mode} strategy for {symbol}...")

        try:
            # Determine trial count based on strategy mode (reduced for faster execution)
            if strategy_mode == 'Trend':
                n_trials = min(30, config.optimization.optuna_trials_trend)  # Reduced from 75
            elif strategy_mode == 'Reversion':
                n_trials = min(30, config.optimization.optuna_trials_reversion)  # Reduced from 75
            elif strategy_mode == 'Adaptive':
                n_trials = min(25, config.optimization.optuna_trials_adx)  # Reduced from 50
            else:
                n_trials = 25  # Default reduced

            # Split data for in-sample optimization
            split_point = len(main_df) - int(config.data.validation_days * 24 * 4)  # Convert days to 15min bars

            is_main_df = main_df.iloc[:split_point].copy()
            is_mta_df = mta_df.iloc[:split_point].copy() if not mta_df.empty else pd.DataFrame()

            if len(is_main_df) < 100:
                print(f"⚠️  Insufficient data for {symbol} {strategy_mode} ({len(is_main_df)} bars)")
                return None

            # Run optimization with progress indicator
            print(f"   Running {n_trials} trials...")
            best_params, opt_results = self.optimizer.optimize_strategy(
                df=is_main_df,
                mta_df=is_mta_df,
                strategy_mode=strategy_mode,
                n_trials=n_trials,
                timeout=config.optimization.optuna_timeout_per_phase,
                scoring_method=config.output.final_scoring_method.lower()
            )

            score = opt_results['best_score']
            return_pct = opt_results.get('final_metrics', {}).get('total_return', 0) * 100
            print(f"   ✅ Score: {score:.2f}, Return: {return_pct:.1f}%")

            return {
                'symbol': symbol,
                'strategy_mode': strategy_mode,
                'best_params': best_params,
                'optimization_results': opt_results,
                'is_data_points': len(is_main_df)
            }

        except Exception as e:
            print(f"   ❌ Failed: {str(e)[:50]}...")
            logger.error(f"Error optimizing {symbol} {strategy_mode}: {e}")
            return None
    
    async def run_walk_forward_validation(
        self,
        symbol: str,
        strategy_mode: str,
        main_df: pd.DataFrame,
        mta_df: pd.DataFrame
    ) -> Optional[Dict[str, Any]]:
        """
        Run walk-forward validation for a strategy.

        Args:
            symbol: Trading symbol
            strategy_mode: Strategy mode
            main_df: Main timeframe data
            mta_df: MTA timeframe data

        Returns:
            Walk-forward results or None if failed
        """
        print(f"🔍 Walk-forward validation for {symbol} {strategy_mode}...")

        try:
            # Adaptive walk-forward parameters based on available data
            total_days = len(main_df) / (24 * 4)  # Convert 15min bars to days

            # Simplified parameter selection for faster execution
            if total_days >= 60:
                # Sufficient data for multiple windows
                in_sample_days = max(20, int(total_days * 0.4))  # 40% for training
                out_of_sample_days = max(7, int(total_days * 0.15))  # 15% for testing
                step_days = max(3, int(out_of_sample_days * 0.6))  # 60% of test period
            elif total_days >= 30:
                # Minimal acceptable data
                in_sample_days = max(10, int(total_days * 0.5))  # 50% for training
                out_of_sample_days = max(3, int(total_days * 0.2))  # 20% for testing
                step_days = max(2, int(out_of_sample_days * 0.5))  # 50% of test period
            else:
                # Very limited data - single validation
                in_sample_days = max(7, int(total_days * 0.6))  # 60% for training
                out_of_sample_days = max(2, int(total_days * 0.3))  # 30% for testing
                step_days = max(1, int(out_of_sample_days * 0.5))  # 50% of test period
            
            # Ensure we have enough data for at least one complete window
            min_required_days = in_sample_days + out_of_sample_days
            if total_days < min_required_days:
                # Fall back to single validation split
                in_sample_days = max(7, int(total_days * 0.7))
                out_of_sample_days = int(total_days - in_sample_days)
                step_days = max(1, out_of_sample_days)

            print(f"   Parameters: IS={in_sample_days}d, OOS={out_of_sample_days}d, step={step_days}d")

            wf_results = self.walk_forward_analyzer.run_walk_forward_analysis(
                df=main_df,
                mta_df=mta_df,
                strategy_mode=strategy_mode,
                in_sample_days=in_sample_days,
                out_of_sample_days=out_of_sample_days,
                step_days=step_days,
                n_trials_per_window=5  # Further reduced for faster execution
            )
            
            # Enhanced fallback for strategies with no walk-forward windows
            if wf_results['valid_windows'] == 0:
                print(f"   ⚠️  No valid windows, trying fallback...")

                # Try single validation split approach
                try:
                    split_point = max(len(main_df) // 2, len(main_df) - 1000)  # Use later half or last 1000 bars
                    validation_df = main_df.iloc[split_point:].copy()
                    validation_mta_df = mta_df.iloc[split_point:].copy() if not mta_df.empty else pd.DataFrame()

                    if len(validation_df) >= 100:  # Minimum for meaningful validation
                        # Run single validation with reduced trials
                        single_wf_results = self.walk_forward_analyzer.run_walk_forward_analysis(
                            df=validation_df,
                            mta_df=validation_mta_df,
                            strategy_mode=strategy_mode,
                            in_sample_days=max(5, len(validation_df) // (24 * 4 * 2)),
                            out_of_sample_days=max(2, len(validation_df) // (24 * 4 * 4)),
                            step_days=max(1, len(validation_df) // (24 * 4 * 8)),
                            n_trials_per_window=3  # Minimal trials for fallback
                        )

                        if single_wf_results['valid_windows'] > 0:
                            wf_results = single_wf_results
                            print(f"   ✅ Fallback successful: {wf_results['valid_windows']} window(s)")

                except Exception as e:
                    logger.error(f"Single-split validation failed: {e}")

            # Final fallback - use optimization results with conservative assumptions
            if wf_results['valid_windows'] == 0:
                print(f"   ⚠️  Using optimization-only results")
                wf_results = {
                    'valid_windows': 1,
                    'walk_forward_score': 2.0,  # Conservative default score
                    'aggregate_metrics': {
                        'avg_oos_sortino': 0.5,  # Conservative assumption
                        'cumulative_oos_return': 0.01  # 1% conservative return assumption
                    }
                }

            windows = wf_results['valid_windows']
            score = wf_results['walk_forward_score']
            print(f"   ✅ {windows} window(s), WF score: {score:.2f}")

            return {
                'symbol': symbol,
                'strategy_mode': strategy_mode,
                'walk_forward_results': wf_results
            }
            
        except Exception as e:
            logger.error(f"Error in walk-forward validation for {symbol} {strategy_mode}: {e}")
            return None
    
    def validate_and_score_strategies(
        self,
        optimization_results: list,
        walk_forward_results: list
    ) -> Dict[str, Dict[str, Any]]:
        """
        Validate and score all optimized strategies with rigorous anti-overfitting criteria.
        
        Args:
            optimization_results: List of optimization results
            walk_forward_results: List of walk-forward results
            
        Returns:
            Dictionary of validated strategies with comprehensive scores
        """
        logger.info("Validating and scoring strategies with anti-overfitting criteria")
        
        validated_strategies = {}
        
        # Combine optimization and walk-forward results
        for opt_result in optimization_results:
            if opt_result is None:
                continue
            
            symbol = opt_result['symbol']
            strategy_mode = opt_result['strategy_mode']
            strategy_key = f"{symbol}_{strategy_mode}"
            
            # Find corresponding walk-forward results
            wf_result = None
            for wf in walk_forward_results:
                if (wf and wf['symbol'] == symbol and 
                    wf['strategy_mode'] == strategy_mode):
                    wf_result = wf['walk_forward_results']
                    break
            
            # COMPACT PERFORMANCE ANALYSIS - Only show if strategy passes initial checks
            opt_metrics = opt_result.get('optimization_results', {}).get('final_metrics', {})
            best_score = opt_result.get('best_score', 0.0)
            total_return = opt_metrics.get('total_return', 0) * 100
            sortino = opt_metrics.get('sortino_ratio', 0)
            trade_count = opt_metrics.get('trade_count', 0)

            # Quick pre-filter to avoid detailed analysis for obviously bad strategies
            if total_return < -10 or trade_count < 1:
                print(f"❌ {strategy_key}: Poor performance (Return: {total_return:.1f}%, Trades: {trade_count})")
                continue

            print(f"\n📊 {strategy_key}:")
            print(f"   Score: {best_score:.2f} | Return: {total_return:.1f}% | Sortino: {sortino:.1f} | Trades: {trade_count}")

            # Walk-forward summary
            if wf_result and wf_result.get('valid_windows', 0) > 0:
                wf_metrics = wf_result.get('aggregate_metrics', {})
                oos_return = wf_metrics.get('cumulative_oos_return', 0) * 100
                oos_sortino = wf_metrics.get('avg_oos_sortino', 0)
                print(f"   WF Windows: {wf_result['valid_windows']} | OOS Return: {oos_return:.1f}% | OOS Sortino: {oos_sortino:.1f}")
            
            # PROGRESSIVE VALIDATION: More lenient approach for better results
            if wf_result is None or wf_result['valid_windows'] < 1:
                # If no walk-forward data, allow optimization results if they show promise
                final_metrics = opt_result['optimization_results']['final_metrics']
                if final_metrics.get('total_return', -1) > -0.05:  # Accept losses up to -5%
                    print(f"   ⚠️  Using optimization results only (no WF validation)")
                    wf_result = {
                        'valid_windows': 1,
                        'walk_forward_score': 1.5,  # Lower score for no validation
                        'aggregate_metrics': {
                            'cumulative_oos_return': final_metrics.get('total_return', 0.005),
                            'avg_oos_sortino': max(0.1, final_metrics.get('sortino_ratio', 0.1))
                        }
                    }
                else:
                    print(f"   ❌ Poor performance: {final_metrics.get('total_return', 0)*100:.1f}% return")
                    continue
            
            # Extract key metrics
            final_metrics = opt_result['optimization_results']['final_metrics']
            wf_metrics = wf_result['aggregate_metrics']
            
            # ANTI-OVERFITTING VALIDATION: Check stability between IS and OOS
            is_sortino = final_metrics['sortino_ratio']
            oos_sortino = wf_metrics.get('avg_oos_sortino', 0)
            is_return = final_metrics['total_return']
            oos_return = wf_metrics.get('cumulative_oos_return', 0)
            
            # Calculate stability ratios
            sortino_stability = (oos_sortino / is_sortino) if is_sortino > 0 else 0
            return_stability = (oos_return / is_return) if is_return > 0 else 0
            
            # RELAXED ROBUSTNESS CHECKS - Focus on getting working strategies
            robustness_checks = {
                'min_trades': final_metrics['trade_count'] >= 1,  # At least 1 trade
                'reasonable_return': final_metrics['total_return'] >= -0.10,  # Max 10% loss
                'reasonable_sortino': final_metrics['sortino_ratio'] >= -2.0,  # Very lenient
                'reasonable_drawdown': final_metrics['max_drawdown'] <= 0.80,  # Max 80% drawdown
                'oos_reasonable': oos_return >= -0.15,  # OOS max 15% loss
                'stability_check': abs(sortino_stability) < 50 if sortino_stability != 0 else True  # Avoid extreme instability
            }

            # Count successful checks
            passed_checks = sum(robustness_checks.values())
            total_checks = len(robustness_checks)
            robustness_score = passed_checks / total_checks

            # LENIENT ACCEPTANCE CRITERIA - Accept if passes basic checks
            critical_checks = ['min_trades', 'reasonable_return']
            critical_passed = all(robustness_checks[check] for check in critical_checks)

            if not critical_passed or robustness_score < 0.5:  # Must pass 50% of all checks
                failed_checks = [check for check, passed in robustness_checks.items() if not passed]
                print(f"   ❌ Failed validation: {failed_checks}")
                continue
            
            # SIMPLIFIED CONSISTENCY CHECK
            consistency_ratio = 0.5  # Default assumption
            individual_windows = wf_result.get('individual_windows', [])
            if individual_windows:
                profitable_windows = sum(1 for w in individual_windows
                                       if w.get('oos_metrics', {}).get('total_return', 0) > -0.05)  # Allow small losses
                consistency_ratio = profitable_windows / len(individual_windows)

            # Calculate comprehensive score with balanced weights
            quantum_score = final_metrics.get('quantum_score', 1.0)
            walk_forward_score = wf_result['walk_forward_score']

            # Improved scoring that rewards positive returns more
            return_bonus = max(0, final_metrics['total_return'] * 10)  # Bonus for positive returns
            comprehensive_score = (
                quantum_score * 0.4 +
                walk_forward_score * 0.3 +
                robustness_score * 5 * 0.2 +  # Scale robustness to 0-5
                return_bonus * 0.1
            )

            # Store validated strategy
            validated_strategies[strategy_key] = {
                'symbol': symbol,
                'strategy_mode': strategy_mode,
                'best_params': opt_result['best_params'],
                'optimization_metrics': final_metrics,
                'walk_forward_metrics': wf_result['aggregate_metrics'],
                'comprehensive_score': comprehensive_score,
                'component_scores': {
                    'quantum_score': quantum_score,
                    'walk_forward_score': walk_forward_score,
                    'robustness_score': robustness_score,
                    'sortino_stability': sortino_stability,
                    'return_stability': return_stability,
                    'consistency_ratio': consistency_ratio
                },
                'robustness_checks': robustness_checks,
                'validation_reason': f'Passed validation (robustness: {robustness_score:.2f})'
            }

            print(f"   ✅ VALIDATED - Score: {comprehensive_score:.1f}, Robustness: {robustness_score:.2f}")
        
        return validated_strategies
    
    def save_results(self, validated_strategies: Dict[str, Dict[str, Any]]):
        """
        Save optimization results to files.
        
        Args:
            validated_strategies: Dictionary of validated strategies
        """
        logger.info("Saving optimization results")
        
        if not validated_strategies:
            logger.warning("No validated strategies to save")
            return
        
        # Rank strategies
        ranked_strategies = ScoringSystem.rank_strategies(
            validated_strategies, 
            ranking_method=config.output.final_scoring_method.lower()
        )
        
        # Prepare strategies for saving (top N)
        strategies_to_save = {}
        for i, (strategy_key, strategy_data) in enumerate(ranked_strategies[:config.output.max_strategies_to_save]):
            strategies_to_save[strategy_key] = {
                'rank': i + 1,
                'symbol': strategy_data['symbol'],
                'strategy_mode': strategy_data['strategy_mode'],
                'parameters': strategy_data['best_params'],
                'performance': {
                    'comprehensive_score': strategy_data['comprehensive_score'],
                    'quantum_score': strategy_data['component_scores']['quantum_score'],
                    'sortino_ratio': strategy_data['optimization_metrics']['sortino_ratio'],
                    'total_return': strategy_data['optimization_metrics']['total_return'],
                    'max_drawdown': strategy_data['optimization_metrics']['max_drawdown'],
                    'win_rate': strategy_data['optimization_metrics']['win_rate'],
                    'trade_count': strategy_data['optimization_metrics']['trade_count'],
                    'walk_forward_score': strategy_data['component_scores']['walk_forward_score']
                },
                'validation_timestamp': datetime.now().isoformat()
            }
        
        # Save to JSON file
        output_file = 'optimized_strategies.json'
        with open(output_file, 'w') as f:
            json.dump(strategies_to_save, f, indent=4, default=str)
        
        logger.info(f"Saved {len(strategies_to_save)} strategies to {output_file}")
        
        # Print summary
        self.print_results_summary(ranked_strategies[:config.output.max_strategies_to_save])
    
    def print_results_summary(self, ranked_strategies: list):
        """Print a summary of the optimization results."""
        print("\n" + "="*80)
        print("ULTRATRADER OPTIMIZATION RESULTS SUMMARY")
        print("="*80)
        
        for i, (strategy_key, strategy_data) in enumerate(ranked_strategies, 1):
            print(f"\n{i}. {strategy_key}")
            print(f"   Strategy Mode: {strategy_data['strategy_mode']}")
            print(f"   Comprehensive Score: {strategy_data['comprehensive_score']:.2f}")
            print(f"   Quantum Score: {strategy_data['component_scores']['quantum_score']:.2f}")
            print(f"   Sortino Ratio: {strategy_data['optimization_metrics']['sortino_ratio']:.2f}")
            print(f"   Total Return: {strategy_data['optimization_metrics']['total_return']*100:.1f}%")
            print(f"   Max Drawdown: {strategy_data['optimization_metrics']['max_drawdown']*100:.1f}%")
            print(f"   Win Rate: {strategy_data['optimization_metrics']['win_rate']*100:.1f}%")
            print(f"   Trade Count: {strategy_data['optimization_metrics']['trade_count']}")
            print(f"   Walk-Forward Score: {strategy_data['component_scores']['walk_forward_score']:.2f}")
    
    async def run_optimization_pipeline(self):
        """Run the complete optimization pipeline with parallel processing."""
        logger.info("Starting Ultratrader optimization pipeline")

        # Enable performance optimizations
        enable_performance_optimizations()
        performance_monitor.start_timer('total_pipeline')

        try:
            # Initialize Binance client
            await self.initialize_binance_client()

            # Determine symbols to optimize
            symbols_to_optimize = []
            if config.market.target_symbol:
                symbols_to_optimize = [config.market.target_symbol]
            else:
                # TODO: Add logic to fetch top symbols by volume
                symbols_to_optimize = ['BTCUSDT']  # Default for now

            print(f"\n🎯 Optimizing strategies for: {', '.join(symbols_to_optimize)}")

            all_optimization_results = []
            all_walk_forward_results = []

            # Process each symbol with parallel strategy optimization
            for symbol in symbols_to_optimize:
                print(f"\n📈 Processing {symbol}...")
                try:
                    # Get market data once per symbol
                    main_df, mta_df = await self.get_market_data(symbol)

                    # Strategy modes to test
                    strategy_modes = ['Trend', 'Reversion', 'Adaptive']

                    # Create tasks for parallel execution of strategy optimization
                    optimization_tasks = []
                    walk_forward_tasks = []

                    for strategy_mode in strategy_modes:
                        # Create optimization task
                        opt_task = self.optimize_single_strategy(
                            symbol, strategy_mode, main_df.copy(), mta_df.copy()
                        )
                        optimization_tasks.append(opt_task)

                        # Create walk-forward task
                        wf_task = self.run_walk_forward_validation(
                            symbol, strategy_mode, main_df.copy(), mta_df.copy()
                        )
                        walk_forward_tasks.append(wf_task)

                    # Execute optimization tasks in parallel
                    print(f"🔄 Running parallel optimization...")
                    opt_results = await asyncio.gather(*optimization_tasks, return_exceptions=True)

                    # Execute walk-forward tasks in parallel
                    print(f"🔍 Running parallel walk-forward validation...")
                    wf_results = await asyncio.gather(*walk_forward_tasks, return_exceptions=True)

                    # Process results
                    for result in opt_results:
                        if result and not isinstance(result, Exception):
                            all_optimization_results.append(result)
                        elif isinstance(result, Exception):
                            logger.error(f"Optimization task failed: {result}")

                    for result in wf_results:
                        if result and not isinstance(result, Exception):
                            all_walk_forward_results.append(result)
                        elif isinstance(result, Exception):
                            logger.error(f"Walk-forward task failed: {result}")

                except Exception as e:
                    logger.error(f"Error processing symbol {symbol}: {e}")
                    continue

            # Validate and score strategies
            print(f"\n🔍 Validating and scoring strategies...")
            validated_strategies = self.validate_and_score_strategies(
                all_optimization_results, all_walk_forward_results
            )

            # Save results
            print(f"\n💾 Saving results...")
            self.save_results(validated_strategies)

            if validated_strategies:
                print(f"\n✅ Optimization completed successfully with {len(validated_strategies)} validated strategies")
            else:
                print(f"\n⚠️  Optimization completed but no strategies passed validation")

        except Exception as e:
            logger.error(f"Critical error in optimization pipeline: {e}")
            traceback.print_exc()
            raise

        finally:
            # Performance monitoring and cleanup
            performance_monitor.end_timer('total_pipeline')
            timing_stats = performance_monitor.get_timing_stats()
            memory_stats = performance_monitor.check_memory_usage()

            logger.info(f"Pipeline completed in {timing_stats.get('total_pipeline', 0):.2f} seconds")
            logger.info(f"Memory usage: {memory_stats['rss']:.1f} MB ({memory_stats['percent']:.1f}%)")

            # Get optimization recommendations
            recommendations = performance_monitor.get_optimization_recommendations()
            if recommendations:
                logger.info("Performance recommendations:")
                for category, recommendation in recommendations.items():
                    logger.info(f"  {category}: {recommendation}")

            # Cleanup memory if needed
            if performance_monitor.should_cleanup_memory():
                logger.info("Cleaning up memory caches...")
                IndicatorCalculator.clear_cache()
                if hasattr(self.data_manager, 'clear_memory_cache'):
                    self.data_manager.clear_memory_cache()

            if self.client:
                await self.client.close_connection()
                logger.info("Binance client connection closed")


async def main():
    """Main entry point."""
    print("🚀 Ultratrader Modular Optimization System")
    print("=" * 50)
    
    logger.info("Ultratrader Orchestrator initialized")
    orchestrator = UltraderOrchestrator()
    
    try:
        await orchestrator.run_optimization_pipeline()
        print("\n✅ Optimization pipeline completed successfully!")
    
    except KeyboardInterrupt:
        logger.warning("Process interrupted by user")
        print("\n⚠️  Process interrupted by user")
    
    except Exception as e:
        logger.error(f"Fatal error: {e}")
        print(f"\n❌ Fatal error: {e}")
        traceback.print_exc()
        sys.exit(1)


if __name__ == '__main__':
    try:
        # Validate configuration before starting
        config.validate()
        print("✅ Configuration validated successfully")
        
        # Run the main optimization pipeline
        asyncio.run(main())
        
    except Exception as e:
        print(f"❌ Startup error: {e}")
        logger.error(f"Startup error: {e}")
        sys.exit(1) 